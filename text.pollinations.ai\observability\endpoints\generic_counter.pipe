TOKEN read_pipes READ
    
NODE count_attributes
SQL >
%
    {% if not defined(dimension) %}
        {{ error('dimension (String) query param is required') }}
    {% end %}
    {% if defined(dimension) and dimension not in ['organization', 'project', 'environment', 'provider', 'user', 'model'] %}
        {{ error('dimension (String) query param must be one of the following: organization, project, environment, provider, user, model') }}
    {% end %}
    SELECT
        toString({{column(dimension, 'organization')}}) as category,
        count() as count,
        sum(cost) as total_cost
    FROM llm_events
    WHERE 1=1
    {% if defined(start_date) and defined(end_date) %}
        AND timestamp >= {{DateTime(start_date, '2025-01-01 00:00:00')}}
        AND timestamp <= {{DateTime(end_date, '2025-12-31 23:59:59')}}
    {% end %}
    {% if defined(organization) and organization != [''] %}
        AND organization in {{Array(organization)}}
    {% end %}
    {% if defined(project) and project != [''] %}
        AND project in {{Array(project)}}
    {% end %}
    {% if defined(environment) and environment != [''] %}
        AND environment in {{Array(environment)}}
    {% end %}
    {% if defined(provider) and provider != [''] %}
        AND provider in {{Array(provider)}}
    {% end %}
    {% if defined(user) and user != [''] %}
        AND user in {{Array(user)}}
    {% end %}
    {% if defined(model) and model != [''] %}
        AND model in {{Array(model)}}
    {% end %}
    GROUP BY {{column(dimension, 'organization')}}
    ORDER BY total_cost DESC, {{column(dimension, 'organization')}}

TYPE endpoint
# Key Feedback Summary

## Pitch Flow & Clarity
- Need clearer explanation of value proposition for developers
- Make the monetization journey more straightforward and upfront
- Better explain why developers choose pollinations.ai
- Add visual examples of how easy it is to get started
- Start pitch earlier with what you actually do - elevator pitch needs work

## Business Model Concerns
- Revenue share model needs better explanation
- Concern about long-term viability for successful apps
- Need precedent examples of similar models working
- Current approach (rev-share at top tier) is opposite of typical models like Shopify

## Community & Traction
- Should highlight Discord community numbers more prominently
- Need to showcase current advertising experiments and results
- Add concrete examples of successful apps and their economics

## Unit Economics
- Need clear example calculations showing:
  - Infrastructure costs
  - Monetization potential
  - Revenue split between platform and creators
- Add real-world example of a current app's economics

## Presentation Improvements
- Add timeline/roadmap with more visual elements
- Include customer stories and demos
- Fix incorrect metrics (e.g., 14M admin pressures)
- Better explain ad network partnerships and revenue sharing structure

## Positive Notes
- Good solo presentation
- Improved clarity in some areas
- Customer stories and demos are well integrated
- Seed/Flower tier structure is clear

---

## Original Transcript

Well, all times um, trying to be more precise. Um, I think

Um, The flow of hey, we are building.

The, the the long tail of app developers that are kind of struggling with monetisation, and we're helping them monetize and we grow with them. I think that that can flow better. We do that by initially enabling them to, um, Uh, to monetize for advertising and then later on through through refshed.

You can. Be more upfront with that still. I think it's still a little bit roundabout, how you explain it. Um, I think the perspective of Why do you have all that traction with developers? That isn't super clear from the pitch yet? It's kind of. Why do developers choose you?

Why is it so much easier to just get started on pollinations? You say, one, two sentences about that but I think You can make them more visual and you can make them a lot clearer in the pitch. Um,

Conceptually, I'm still struggling with. That the Russia is the long-term answer. I find it intriguing. But, If I build a super successful app, I also don't want to. Then later on kind of later on that becomes the inferior business decision. For if I'm if I'm if I run the app and my app turns into the next Facebook and then I don't want The referee agreement with a cloud provider.

Um, so That's a new thing. I don't know. It's a new business model, so I don't think that I can't think of the precedent of Alan why that works typically? I mean, Shopify works with a, with a ref, share at the bottom tier and then once people get bigger, you get out of the ref Shed.

So kind of, why is this the other way around? Um, Um, But I don't. Um, Um, I think you can play your community numbers. Like, I don't think you talked about the Discord. Um Stronger. Um, I think any numbers that you already have and stuff that you're already doing with your advertising experiments, I don't think you, you talk that much about that, I think.

In terms of showing, Trajectory and roadmap kind of in classical timeline. It's just slide. You can probably give that more colour and make that zero. Yeah just click adding onto that I just have for the first time today and I didn't understand what you were saying with that. So yeah.

You mean like in general? Um sorry because that was that was a feedback last week. Maybe you should have a quick run through. Kind of often because I think okay for the first time nobody has some some different. Yeah, input now. But uh, yeah. Yeah. Okay. And unit economics, I think there's some sort of how does that work because the key question is still you're giving stuff away for free.

Um so I think some sort of Just one or two example calculations of hey this the usage is what we pay. Um, and this is what we can monetize. And this is where kind of our confidence on those numbers come from, hey, I think that is he did. Yeah, we have like, we have an exam.

We yeah, we will add it. We have actually an example where we just show on the example of one app, like that's like, not the biggest one, but like, kind of how much like do we make and how much does the Creator make or something like that? Yeah, exactly.

And how much do you have in infrastructure? Yeah, yeah. Okay, yeah, yeah. Um, Those are my key points. Yeah, uh, just briefly, um, First couple Kudos I think um that you are presenting on your own I think or at least one person that's good. He's doing more on clarity.

Like that, you're presenting as well, the read, Um, better than fish tag. Like the customer story and demos, you're actually put in your bag and like the seed flower like there as well. Um, I think also directed point it would be good to kind of start earlier, what you actually do.

I know what you do. Um, I don't know what you do. Alice doesn't? That's still super confusing. So elevator. Yeah. Um, and then, yeah, um, So the ad Network Nexus, for example, a new revenue Channel, how would it actually look like, right? So how much percent will you take depending on the F State?

At what point? Like how many of your apps currently in your catalogue would be? Uh, kind of relevant right now for your Revenue share model, when you're working with Nexus, right? Like, how do these kind of experiments look like, like, what could this be in the future? So bit, this more on.

We have economics and kind of business model. I think it would be great and I saw you had 14 million admin pressures on my website. That's a mistake. That's, that's a mistake. Yeah, yeah, yeah, yeah, yeah, yeah. I think I saw that too. Right. Yeah, I think. Yeah, for me.

That's a donator. Yeah, I'd love to. Yeah. Yeah. Because we're around the mate. But, uh,

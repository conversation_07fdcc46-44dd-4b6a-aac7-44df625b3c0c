# Glossary of Terms

This glossary defines key terms and acronyms used throughout the pollinations.ai documentation.

---

**A**

*   **Ad € / Cloud € ratio**
    *   **Definition:** A key metric representing the ratio of advertising revenue generated by an application (€) to the cloud compute costs (€) incurred by that application.
    *   **Significance:** This ratio is used by Pollinations' Rate-Limit Engine to dynamically adjust an app's operational tier and limits in the default ad-funded model. Apps become self-sustaining and unlock higher tiers when their ad revenue sufficiently covers their compute costs.
    *   **Context:** `01-executive-summary.md`, `07-tech-architecture.md`, `08-sdk-ad-integration.md`, `09-competitive-landscape.md`

*   **Ad Network Fee**
    *   **Definition:** A percentage of the Gross Ad Revenue that is paid to third-party ad networks or exchanges for their services in delivering ads.
    *   **Significance:** This fee is deducted from Gross Ad Revenue to calculate Net Ad Revenue, which is the basis for the Nectar tier revenue share.
    *   **Context:** `05-financial-model.md`

*   **Ad Providers**
    *   **Definition:** Entities, including brands and ad networks, that supply advertisements to be displayed within applications built on the pollinations.ai platform.
    *   **Significance:** They represent the demand side of Pollinations' ad-funded ecosystem, seeking to reach engaged audiences within AI-native apps.
    *   **Context:** `03-business-model.md`, `07-tech-architecture.md`

*   **AI-contextual advertising**
    *   **Definition:** A form of targeted advertising where ads are selected and served by automated systems based on the content the user is viewing, identified and understood by artificial intelligence.
    *   **Significance:** pollinations.ai aims to leverage this by placing relevant ads within AI-generated experiences.
    *   **Context:** `03-market-opportunity.md`

*   **ARR (Annual Recurring Revenue)**
    *   **Definition:** A measure of the predictable and recurring revenue a company expects to receive from its customers over a 12-month period.
    *   **Significance:** Used in market opportunity projections to forecast Pollinations' net retained revenue.
    *   **Context:** `03-market-opportunity.md`

**B**

*   **Beachhead Market**
    *   **Definition:** The initial, specific, and narrowly defined market segment that a company targets at the beginning of its market penetration strategy.
    *   **Significance:** For Pollinations, this is indie developers using Generative AI for apps, monetized via the platform.
    *   **Context:** `03-market-opportunity.md`

**C**

*   **CI/CD (Continuous Integration/Continuous Deployment)**
    *   **Definition:** A set of practices and tools that automate the process of software development, from code integration to testing and deployment.
    *   **Significance:** pollinations.ai automates CI/CD for creators, enabling a "Zero-ops" experience.
    *   **Context:** `07-tech-architecture.md`

*   **Code MCP (Master Control Program for Code)**
    *   **Definition:** A pollinations.ai cloud service that automates the initial setup of a creator's application.
    *   **Significance:** It handles GitHub repository creation, injects secrets, pushes starter code, and configures deployment workflows, enabling creators to start quickly from their editor.
    *   **Context:** `07-tech-architecture.md`

*   **Contextual Ads**
    *   **Definition:** Advertisements that are selected and served based on the content a user is currently interacting with, rather than solely on user data.
    *   **Significance:** A core part of Pollinations' ad strategy, aiming to provide relevant ads within AI-generated experiences.
    *   **Context:** `03-business-model.md`, `09-competitive-landscape.md`

*   **Seed tier**
    *   **Definition:** The second tier in Pollinations' 3-tier ladder, designed for early-stage projects.
    *   **Key Features:** Moderate rate limits, token authentication, usage analytics. Currently in Beta.
    *   **Context:** `01-executive-summary.md`, `03-business-model.md`

*   **Creators**
    *   **Definition:** Developers, solo indie devs, and small teams who use the pollinations.ai platform and SDK to build, deploy, and monetize generative AI applications.
    *   **Significance:** They are the primary users and supply side of the pollinations.ai ecosystem.
    *   **Context:** `01-executive-summary.md`, `03-business-model.md`

**E**

*   **eCPM (Effective Cost Per Mille)**
    *   **Definition:** A metric used in online advertising that measures the advertising revenue generated for every 1,000 ad impressions.
    *   **Significance:** Key indicator of ad monetization efficiency. pollinations.ai targets a gross eCPM of €20 for monetized tiers.
    *   **Context:** `01-executive-summary.md`, `05-financial-model.md`

*   **Ecosystem MAU (Monthly Active Users)**
    *   **Definition:** The total estimated number of unique end-users interacting with all 300+ applications built on the pollinations.ai platform within a given month.
    *   **Significance:** Indicates the overall reach and engagement of the pollinations.ai ecosystem. Currently cited as ~3M MAU. Also referred to as "Total estimated ~3M MAU".
    *   **Context:** `01-executive-summary.md`, `12-traction-metrics.md`, `03-blooming-ecosystem.md`

*   **Seed tier**
    *   **Definition:** The entry-level tier in Pollinations' 3-tier ladder, aimed at curious developers and hobbyists.
    *   **Key Features:** High rate limits, no authentication requirement, zero cost. Currently live.
    *   **Context:** `01-executive-summary.md`, `03-business-model.md`

**G**

*   **Generative AI APIs**
    *   **Definition:** Pollinations' Application Programming Interfaces that allow developers to integrate various generative AI models (for images, text, etc.) into their applications.
    *   **Significance:** The core technical offering that enables media generation on the platform.
    *   **Context:** `01-executive-summary.md`, `07-tech-architecture.md`

*   **GPU (Graphics Processing Unit)**
    *   **Definition:** Specialized electronic circuits designed to rapidly manipulate and alter memory to accelerate the creation of images, videos, and AI computations.
    *   **Significance:** Essential hardware for running generative AI models; a significant cost component pollinations.ai aims to cover via ads.
    *   **Context:** `01-executive-summary.md`

*   **Gross Ad Revenue**
    *   **Definition:** The total advertising revenue generated by applications on the pollinations.ai platform before any deductions, such as ad network fees or revenue shares.
    *   **Context:** `05-financial-model.md`

**I**

*   **Flower tier**
    *   **Definition:** The third tier in Pollinations' 3-tier ladder, designed for apps that can become profitable through ad revenue.
    *   **Key Features:** Unlimited usage, access to State-of-the-Art (SOTA) models, rich ad formats. In this tier, ad revenue generated by the app first covers its compute costs, then pollinations.ai retains 100% of the remaining ad revenue. GA planned for H2 2025.
    *   **Context:** `01-executive-summary.md`, `03-business-model.md`

**M**

*   **Marketplace**
    *   **Definition:** (Planned) A discovery platform, envisioned as the new pollinations.ai website, where end-users can find and explore applications built by creators on the pollinations.ai platform.
    *   **Significance:** Aims to increase visibility for creator apps and drive user adoption.
    *   **Context:** `01-executive-summary.md`, `02-vision-mission.md`, `03-business-model.md`, `04-roadmap.md`, `07-tech-architecture.md`, `11-risk-register.md`, `12-traction-metrics.md`

*   **MCP (Master Control Program)**
    *   **Definition:** A suite of backend services at pollinations.ai that manage and automate various aspects of the platform and app lifecycle for creators. `Code MCP` is an example.
    *   **Significance:** Central to the "Zero-UI" and "Zero-ops" philosophy, handling complex tasks behind the scenes.
    *   **Context:** `07-tech-architecture.md`

*   **MCP SDK**
    *   **Definition:** The pollinations.ai Software Development Kit that runs locally in a creator's development environment (e.g., editor).
    *   **Significance:** It allows creators to interact with Pollinations' Master Control Program (MCP) services, for example, to initialize new projects with a single command.
    *   **Context:** `07-tech-architecture.md`

*   **Media Unit**
    *   **Definition:** A single piece of content generated by the pollinations.ai platform, such as an image, a segment of text, or another AI-generated asset.
    *   **Significance:** Used as a basis for calculating compute costs (e.g., €≤0.0005 per media unit) and platform usage.
    *   **Context:** `01-executive-summary.md`, `05-financial-model.md`

**N**

*   **Net Ad Revenue**
    *   **Definition:** The advertising revenue remaining after deducting Ad Network Fees from Gross Ad Revenue.
    *   **Significance:** This is the "Net Ad Revenue (Platform Pool)" from which the 50% share is calculated for creators in the Nectar tier.
    *   **Context:** `01-executive-summary.md`, `03-business-model.md`, `05-financial-model.md`

*   **Net Ad Revenue Retained by Pollinations**
    *   **Definition:** The portion of Net Ad Revenue that pollinations.ai keeps after accounting for Ad Network Fees and, in the case of Nectar tier apps, the 50% revenue share paid out to creators.
    *   **Context:** `05-financial-model.md`

**P**

*   **Nectar tier**
    *   **Definition:** The top tier in Pollinations' 3-tier ladder, designed for strategic revenue-sharing partners.
    *   **Key Features:** Includes all Flower perks plus a 50/50 split of the Net Ad Revenue generated by the Nectar apps (GA 2026)
    *   **Context:** `01-executive-summary.md`, `03-business-model.md`

*   **pollinations.ai Ad SDK**
    *   **Definition:** A Software Development Kit provided by pollinations.ai that creators integrate into their applications.
    *   **Significance:** Enables the display of ads within the apps, which generates revenue to cover compute costs and, for Nectar tier apps, share with creators.
    *   **Context:** `04-roadmap.md`, `08-sdk-ad-integration.md`, `11-risk-register.md`

*   **pollinations.ai Edge Services**
    *   **Definition:** A suite of services hosted by pollinations.ai that handle critical runtime operations for creator apps.
    *   **Significance:** Includes authentication, access to Generative AI APIs, ad serving, the Rate-Limit Engine, usage tracking, and future billing/revenue share ledger functionalities.
    *   **Context:** `07-tech-architecture.md`

*   **pollinations.ai Net Contribution / App**
    *   **Definition:** The net financial contribution of an individual app to Pollinations, calculated as Pollinations' Net Revenue from that app minus the app's specific compute costs.
    *   **Significance:** Indicates the profitability of an app for the pollinations.ai platform.
    *   **Context:** `05-financial-model.md`

*   **pollinations.ai Net Revenue / App**
    *   **Definition:** The revenue pollinations.ai earns from a specific app after deducting ad network fees and any revenue share payouts to the creator (for Nectar tier apps).
    *   **Context:** `05-financial-model.md`

**R**

*   **Rate-Limit Engine**
    *   **Definition:** A system within pollinations.ai Edge Services that dynamically adjusts an application's operational tier, access limits, and capabilities.
    *   **Significance:** In the default model, this adjustment is based on the app's Ad € / Cloud € ratio. For subscribed creators, it applies guaranteed tiers.
    *   **Context:** `07-tech-architecture.md`

*   **Revenue Share Ledger**
    *   **Definition:** (Planned Q4 2026+) A system that will track the ad revenue attributable to individual Nectar tier applications.
    *   **Significance:** Essential for accurately calculating and distributing the 50% net ad revenue share to Nectar creators.
    *   **Context:** `04-roadmap.md`, `07-tech-architecture.md`

*   **Rich Ad Formats**
    *   **Definition:** More engaging and interactive types of advertisements beyond traditional static banners, such as video ads, playable ads, or native ad integrations.
    *   **Significance:** Intended to improve user experience, increase ad effectiveness (eCPM), and provide better monetization for apps in higher tiers (Flower, Nectar).
    *   **Context:** `01-executive-summary.md`, `04-roadmap.md`, `05-financial-model.md`

**S**

*   **SAM (Serviceable Addressable Market)**
    *   **Definition:** The portion of the Total Addressable Market (TAM) that a company's products or services can realistically reach and serve.
    *   **Significance:** For Pollinations, this is the indie developer app/web inventory portion of the broader AI-powered contextual advertising market.
    *   **Context:** `03-market-opportunity.md`

*   **SDK (Software Development Kit)**
    *   **Definition:** A collection of software development tools in one installable package, facilitating the creation of applications for a specific platform or system.
    *   **Significance:** pollinations.ai provides an SDK (pollinations.ai Ad SDK, MCP SDK) for creators to build, integrate ads, and interact with its platform.
    *   **Context:** `01-executive-summary.md`, `03-business-model.md`, etc.

*   **SOTA Models (State-of-the-Art Models)**
    *   **Definition:** The most advanced and highest-performing generative AI models currently available in the field.
    *   **Significance:** pollinations.ai plans to offer access to SOTA models for creators in higher tiers (Flower, Nectar) to enable cutting-edge applications.
    *   **Context:** `01-executive-summary.md`, `03-business-model.md`, `04-roadmap.md`, `11-risk-register.md`

**T**

*   **TAM (Total Addressable Market)**
    *   **Definition:** The total market demand for a product or service, representing the maximum revenue opportunity available.
    *   **Significance:** For Pollinations, this refers to the global AI-powered contextual advertising spend.
    *   **Context:** `03-market-opportunity.md`

**V**

*   **Vibe-coder**
    *   **Definition:** A term coined by pollinations.ai to describe individuals who can create AI applications by focusing on the desired outcome, user experience, or "vibe," without necessarily needing deep traditional programming or machine learning expertise.
    *   **Significance:** Represents Pollinations' vision of democratizing AI development.
    *   **Context:** `02-vision-mission.md`

**Z**

*   **Zero-UI Platform**
    *   **Definition:** A platform design philosophy where primary interaction and control occur through API calls, command-line interfaces, or code, rather than complex graphical user interfaces (GUIs) or dashboards.
    *   **Significance:** pollinations.ai aims for a "Zero-UI" experience where creators can manage their applications primarily from their code editor.
    *   **Context:** `02-vision-mission.md`, `07-tech-architecture.md`

*   **Zero-ops**
    *   **Definition:** An operational model where the complexities of infrastructure management, deployment, scaling, and maintenance are abstracted away from the developer or creator.
    *   **Significance:** pollinations.ai aims to provide a "Zero-ops" experience, allowing creators to focus on building their app's user experience rather than managing backend infrastructure.
    *   **Context:** `07-tech-architecture.md`, `09-competitive-landscape.md`
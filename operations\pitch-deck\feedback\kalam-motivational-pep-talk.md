Uh, what I read and And, I think what most people are not fully aware about when it comes to investing in the spaces is the Um, the impact and The. Let's say the impact and the, the, the force at which open source is being pushed in China. You know, it's just uh, the the Deep Sick outer and and all the other companies, this is a very big topic, you know?

Just and it's creating around it. Let's just call this the communities, just very focused on building apps on open source. And, and so that whole momentum is a big thing out there. It's counter-intuitive to what's happening on this side of the world where, you know, <PERSON><PERSON> and <PERSON> and open AI announced their billion dollar investments into these things on the other side is the complete opposite which is like, yeah, there's money coming in but but it's like they're looking at like, Doing everything on on a, on a free layer of compute and, and bring it cheaper and cheaper, and cheaper and cheaper.

Yeah. And that's that's the fast. That's the fast growing thing on the other and on the other side and it and it's it's cracking. The the myth that you need to. Build with billions. Yeah. The big topic overarching, right? So without you like, where are your users coming from?

That was that was for me is also a hint of like where the most activity is if you can break out like, oh you've got a large contingent of Chinese users. Uh, yeah. Yeah. It's like there's a good reason why you're getting the Chinese uses. It's not just the population size.

There's a dedicated push over there with with open AI tooling and you're getting caught in that brush. Yeah, yeah, yeah, yeah. Yeah, yeah, yeah. Totally totally. But like, what I'm trying to get at this is, is, uh, this, this messaging I think it's difficult craft because this never happened before.

What I'd like you to think about is You know, whilst you're in this program and there there's a there's a you're trying to create a structured pitch. That thing that I couldn't quite grasp and abstract out into a single sentence. Yeah, is actually quite a big deal. It's a pretty big deal and it's it's actually the thing that makes this exciting, right.

I think it's worth thinking about it. Yeah yeah sits on it for a little bit. Think about where where to put this um yeah also try to think about it because it is a pretty big deal. Uh yeah I think you know what I think A's a when you read like the the a16z has has has some quite like Advanced

I I haven't actually, I've also been talking about this because another advisor sent me said, gave me that Maxi, but he's a cool guy from outlier times. Um, he sent me that that tip and that they have some pot, like they have some podcasts, they have, they have like a regular podcast series and there's some, and they also have some bloggers.

But there's some that go quite apparently quite deep into this model. Like, how can you, how can you do that? And how and they, they have this right, recommendation to create a foundation under, under for-profit, entity separate right in order. And then, like each one has has the incentives.

You know, you can kind of, like, put get the incentives, right? Whereas, when you join them in one company and you're getting VC money. Then you the, the incentives of the, let's say, open source part suddenly become like, say a slave to to the profit, right. Like I'm saying, it's very superficial superficially, but that there was a there was a

I, I think. What I want you to be what I guess what I, what I'm trying to express is, yeah, be mindful that your inner you're in a, you're you're in, Program where they try to help you craft? Yeah pitch yeah. Fit with certain types of investors. Yeah, important theme of what's happening and why and what you're doing and why it's exciting.

Is is is going to be much bigger than, than what anyone is anticipating in the truth. And and so somehow yours your strength. In fact, I think your magical The magical thing that you can weave through. This is to find a way to capture that excitement. Yeah, just sharing some links of like, what?

What I'm trying to. To nail down in terms of, How big this scope is when it comes to building open source. There's this article, I guess it's going to be behind a paywall for you else. If it is, I'll just um, yes it is. Yeah, do send it to me later, or okay, I'll find some things on Bloomberg opinion.

Yeah, just read through it. But it's this bigger picture and maybe putting numbers around that and being able to cross a view of that, you know, this bedazzles people. Yeah, into like, holy shit. This open source thing. I didn't even realise. It was that huge and it's such a big thing in China, you know.

Yeah. Spending, uh, 40 billion, a quarter in the in the US on AI investments from meta to Google but in in China they're they're they're doing a much bigger thing than. Yeah, these companies are doing it for a fraction of the cost. This, this is where you are, you are playing, you're playing this this uh the power of Open Source.

And but I think the the thing is on a day-to-day basis, what you're seeing is, um, Is people building games on your apis people building apps on your apis. We're coming up with new ways to use the API and these use cases are all new they didn't exist a few years ago.

So so it's this is it makes it also a little bit difficult. Yeah, sorry it makes it describing the problem with solving is it's it. I don't know if that's the reason but it's a little bit difficult in some ways, you know, because, uh, it's kind of a new a new, uh, space that's opening.

Um, but yeah, there's definitely also an identifiable problem. Yeah. Yeah, sorry. Yeah. Yeah. That's the thing. I don't know what the problem is. Yeah. Yeah. I think with this kind of pitch pitch, pitch Theory, right? Yeah. It's just like, there isn't a problem solution thing. It's, yeah, it's a, it's a shift, towards a whole new way of doing stuff.

Yeah, there's a closed version and there's an open version and then, and the open version is where a lot of the experimentation now begins. We, don't see it on this side of the world, we see and we see people building on Top of open AIS, API and others. But then when you look at, for example, the music model and there, I'm in their Discord, it's like, Relentless, like everyday, people, modding and doing new things in there.

Yeah, yeah, yeah. And, and, um, I, I think there's

I, Yeah, it's a communication thing, right? I think there's something if there's someone in that in the room in your face. Yeah. Who is absolutely seeing what's happening with? Yeah. Yeah, yeah, yeah of, uh, apps on open open source. And people coming up with new applications and just seeing that momentum and seeing what you're doing for them, it's completely logical, right?

Your API is going to keep growing, there's going to be more users coming in. It's going to be a huge thing. Yeah, business model based on ads, inside this wall, that thing is is I cannot even put a number on how big it's gonna grow. Yeah, yeah, I mean, I I kind of think.

Yeah. I mean I'm feeling it. Another thing is where where your investors get get excited. Uh, yeah. Yeah. When you try and frame this in terms of, okay, we're creating a bunch of a bunch of services around that are going to be open source. People are just trying to like, then box you in.

Yeah, yeah, yeah, right. Yeah. There could be this tension with like, uh, with the sense of the pitching like, everyone's gonna say, what's your Market size, right? Yeah, yeah. Yeah. And, and then, so, if you ask, yeah, if you ask Google, okay. Size was where it started, right? How would they answer that question, you know?

You know, when a paradigm shifts. Uh, it's what they go to Paradigm, shifts, and then you have billions being spent on both sides of the world. To asset dominance. It's closed versus open. I just don't know how to frame this as a as a huge opportunity potentially, you know, you are right, you're in the middle of that.

Yeah, yeah, I mean, you know what, I want to say to that is that? I, I'm, I'm feeling it boiling, you know, underneath me, I'm I'm seeing the ripples, you know, in our Discord, you know, in our communities and, and, and, and also I'm seeing it on GitHub, you know, in terms of like this kind of organic like everything.

Everyone's talking everyone. People are like people are coming together now suddenly especially since they I boom in huge numbers, you know, and and working on projects together so it's totally. I I totally feel it too now. Um, and I think I like really like um, Um, that's like also I believe that's the magic that that we haven't that many people don't see it yet, right?

Um, but at the same time, I think they already, they're already convinced just just our numbers, you know, even of course, ideally they would be like, um, a totally understanding of the open source, but they have. But it seems, I think they're already convinced just because of our numbers, right?

And, and, um, And they're also like, so some they've looked into our communities and they've actually seen like, how, how, how much is going on. So, so I think it's kind of fascinating to them because it's, they don't have that kind of startup regularly. So I felt like, I'm feeling like and I think you're probably right in everything you're saying that like, it's so much more valuable than they can see.

Because there's this big thing happening at the same time. It's it's like, yeah, we now we're we're now maybe also represent. Not representing that passage wrong, but we're kind of telling them a little bit what they wanna hear. But I all I'd like to actually show you the pitch because then, maybe you can also like see how far we've gone in the like in the kind of VC direction or not.

The only reason I'm a bit, um, I have like, in in, in, in a type. I have, uh, they schedule. Certainly a pitch in front of, like, I don't know, three of the, the investment team already. They, they want to do one pitch. So, I'm, I'm a little bit, just like, thinking, like, what are the small things, you know, I can do now.

Um, um, because I to do to like, like, like um, you know, optimise it in a way. Yeah, yeah. It's always I think you shouldn't even worry about this. Yeah, yeah. I would tell you straight up, you know, these things are just part of the exercise to just build a routine.

Yeah. And if you're worried that you're going to lose someone through these calls. Don't. Yeah. Yeah, it's totally like everything comes down to the demo day and and what you present on the demo day. Yeah, yeah, yeah. Yeah, yeah. Yeah, yeah, yeah, it's just, it's pretty like a game.

You're just levelling up each time, you know. It's just it's just you get it through, sometimes you screw it up sometimes you don't and yeah, it's fine. Definitely fine. And remember, you're working on a big thing. The more it's more exciting to me. What you're building withdr that it is, you know, you're getting through a bitch.

Yeah, I think it's gonna grow. Anyways, it's it's a very different thing to, you know, someone who's Reliant on, on all these on fabricating uh presentation that is from the face slide where that gets them too to to money. I think your your web on your way there. It's just the most valuable thing you have is is On your API.

You seem entirely new use cases and opportunities to build businesses around. Build a business around that. No one else has that. As far as I know? Yeah. It's just like it's, it's just that the challenge is, how do you do this? The way you're doing it because you want to do it at a community level?

Yeah, together with a community. No one does that? Yeah. Just a single single Focus apps and And that's that's the thing that that is. That is very uh I think it's just far away from most. Yeah, actually actually I didn't even think about that. But now you say it there's no no other one in the and we have quite a few teams in the court who have like, I mean, but also I mean, it's also, I guess their ideas.

They're still kind of pre-product, right? Most of them. So I guess maybe that that Community may come later, but I haven't heard people talking about talking about him. Yeah. You're in the general, you're in the generation of like product Builders like mid-journal Is all done of Discord and like building with the community.

Uh, they're a single feature product, but it's still, it's still the same kind of thing, right? And uh, so so it's just that the differentiation where it splits is like you're going in the direction of, you know, uh, Verticals. You have these tool sets for games. For other media, you have these tool sets potentially and then you're going to add model there that ensures that everyone can use this for free.

So you're kind of like Doing things that that for me, just reminds me of Fiverr and They just changed the model, you know, around how you do, how you do this kind of stuff. You know, you know, there's another funny thing I, you know, this company garlic right? I don't know if you remember, they do a context.

Yeah, yeah. And I we talked to them and they said they have real difficulty. Uh uh, they were quite honest with us. Like they said they're having real difficulty explaining explaining to investors. Um, this this idea, you know, around like kind of, uh, monitors at best monetisation of genai, um, contextual, uh, that.

So even them and they're doing like only a part of that they're only doing the ad connection right with the and even they were fighting quite difficult to explain to investors. Um, maybe I don't know, it it kind of feels obvious to me but I think for someone who but it's maybe because I've walked this kind of path and and for someone, you know, Most of these investors, they have a high level understanding of Americans.

That may have played. May have actually worked in antech. Yeah. I think it's a it's mostly a confidence. An exercise in confidence. You know, when you talk about these things I tend to get more to the detailed specifics of how things work. Yeah. Yeah yeah. Yeah. And I've realised, most people don't want to hear that.

Don't even they'd rather hear it works. And it works. Yeah. Yeah. And it's growing like this. It's crazy. And we're the number one people doing this. That's all they want to hear. Uh, yeah. Yeah. So so I think uh, trying to uh

Uh, this is how these new forms of contextual ad. That could work is it's helpful as an explanatory note, but not the lead thing I say. I think it's just, uh, people are just looking for confidence, uh, in the way you did respond to things. Yeah, yeah, I understood understood.

Yeah, yeah, I think I was doing that. With an American accent will get you passport. Let's try and pick one. I'm so bad at taking accent, so it will just be completely ridiculous. It's just these slow pauses. Like you won't believe this. I think it's growing like this. Uh, so yeah, I I On the one had.

You have it on this business and you're not really scamming people. Yeah, there's a lot of things in there, but it's all formative. You know, there's no, we're looking back at things easy to explain things but when you're in the middle of building it, yeah. I think some of these meetings you're having right now.

It's a useful. My God, it's been, it's been so good. It's been so good. Like really like uh, we really like we're even thinking now, we were a bit stupid, like we wouldn't have, wouldn't be able to make a business. We would, you know, we would kind of bump our way, through some problems, you know?

But having this, like, force that you have to, kind of, also, even if it's not like exactly how we will do it, right? Uh, how you will do, but just being forced to like, put it into this context, helps a lot. Uh, you know, like, in many ways, you know, or like um, Yeah, so I'm I'm actually I'm really appreciative of this.

Of this or, you know, of this a time we had. I mean because I mean we could still decide not to not to not to take it right? And and we've really sharpened our message now and, you know, we but I I'm I'm very I, I kind of, I'm really I really want to build stuff.

I want to take some more Cloud credits. I don't want to spend like, time conversing stuff, my feeling is, I, we probably if they give us the deal that we like, we're gonna take it. But then, uh, We're not gonna, uh, We're not gonna go straight, but it's like a second fundraise idea.

We're gonna, we're gonna build our stuff. And we're gonna also look into different, um, more like EU fans, uh, to, to find a way to not, not like, go straight down this, this VC, um, path. So that's kind of my strategy at the moment. Like, that's yeah, yeah. All right.

Okay. I mean like like I'm also like really happy to to because I think you you have you you have like a kind of deep knowledge um in and like I'm I'm also I'm just telling you that's kind of my feeling right now but I'm also Um, I'm I'm also willing to reconsider, you know, like because for me like I'm like the same startup accelerator, you know, I've I've participated in I'm so my knowledge is of course, much more limited um than yours, you know.

I think right now, you know, it's 2025, it's, it's, it's this. There's so much coming up on a weekly basis, uh, daily basis. Probably next week it'll be a daily basis things coming off that lowers blow our brains in terms of what's possible. Yeah, but the the tooling out there and I I think what is really important to kind of on a personal level to to try and nurture within you is A sense of leadership, in terms of how, where are we going?

Yeah. Yeah, you know, and, and feeling that as your core because it's just, you're going to be buffeted by the wind, in some ways telling you what you should be doing. Oh, you should be doing where you should be. But I think this, this thing that that you represent and want to convey to people as your vision, your identity and your why It's gonna be more valuable than anything else at this point in 2025.

It's going to be so much noise and so much excitement. Uh and and um and I think that will help inform like where you go with investors? Yeah. Yeah, yeah this and so this program, I think all I'd say is just like you're going through a process where people are going to challenge you going to make you feel challenged in terms of how you present yourself and what you're presenting to try, and just step back a little bit.

Just figure out where your core is in this, because you're working on something much bigger, after three months. They're all gone. Yeah, yeah. It's all right, very quickly after three months. It's just, it's just your thing is. It's just, it's just a key point. Yeah, I think we. Yeah, we also, we're not really talking further in the future pitch.

Also is very kind of like, um, focused on the now. Whereas I actually quite like to think about the vision, you know? Um, the the great everything. Honestly, I really. I, I have quite a clear, like, it's something that's not lacking, which is quite qu. Even if I don't, I'm not able to express it very well.

I do have like, I kind of saw this coming already like that. If I continue doing pollinations and this was already when it had little users, you know? And I I just, and I had very strong belief, you know, that that and it's kind of coming true and I still feel like I've got, I've got quite a clear idea, um, where I want to take this, you know, we, you know, I don't, it's not me anymore.

Also, it's like a community, an Elliott, and you, um, you know, um, so but, but yeah, but for sure. Um, Everything you say is make, makes total sense, and maybe also writing down, sometimes can be can be quite good. Like, it's kind of a Manifesto or something like that.

Um, Yeah I guess all that's all I'm saying. Yeah when you're from these people they're reading you. They're reading your body language when you come across. There's two types of things. I have is people who are relaying a pitch that has been well versed. Yeah, there's people who you already sense where they have this, this presence they know where they want to go.

And, you know, it's coming out in terms of like that. It's, it may come out of their pitch, it may not. But when you talk to them, you you you you you get excited because you know, they they they have a clear view and they're onto something. They're not just making their shit up.

Yeah, yeah. Yeah, yeah. And and I think that that he should lose shouldn't lose that in this process, because this is the danger I find with, uh, with acceleration programs. It's, uh, you can do a really good pitch. Yeah. But then, uh, It's just it's just the most exciting things when you talk to someone and you just see in the back of their eyeballs.

Like, yeah, yeah, where this is going where they want to take it. Yeah, yeah. Yeah. You can do that because they learnt how to act. But with with most, uh, With the most of the things that I've been excited about is just You just get it from the conversation.

It's just like, yeah, this person is a deep domain expertise, they're telling me things. I didn't even think about, or even consider and and they have a vision for where they want to go. And, and why I want to be part of this journey. That's the thing that, that, that's the last part.

It's like, at that point, it doesn't matter. You know what you say it's like, once you call someone who wants to just buy into where you're going. That's where that's where. That's when you do you. You get the investment? Yeah. Yeah. But I'll be honest with you. It's just I've seen people do this, who are just amazing at getting people excited.

Yeah. And then I've seen I've seen other uh, Who have, you know, come from deep Tech backgrounds sold at first cut sold. Their first company to make engineering company, made tons of money and then focused on music and then sold their next pay to Apple. You, you have You can, you can get the money both ways, right?

I think, the more interesting thing is is to get to a point where people get inspired by your where you want to go. That's just the logic of all, this doesn't matter so much. Yeah. Participating in the journey. Yeah, I mean I think it's I think it's also like a philosophy.

I I make a YouTube, I make a YouTube series with your, with your advice. I think, no, I mean I think it's really it's it's really cool. And I think it also like gives me some like, you know, I do have a line confidence, you know, uh, with this.

Um, so so it also like, I didn't notice that the main lack of confidence, maybe I was having was just coming also from not, not knowing very well what I was saying and now I know it. Um, and like, that's why I I really like to just show you so you get a feeling of where it is.

I'm not saying it's great or anything, you know, but I'm but yeah later that's fine. If if now would be great because then I think I already have to hop because then the next meeting with the antla, people will start and I can quickly get your kind of feeling what you think should we do it?

Just jump in. Yeah, let's do it. Okay wait, let me just um, Because then I will do it again. There we go. Hitch deck.

Okay, let's do it. And,

Discord. It works. I think it works in this spot. Or you can go to to Google. Who will meet whatever you like? Tell me if it works for you. I think it'll work

Okay, do you have it? Yeah, I see something that says watch stream. Okay, let's do it. Yeah, yeah. Okay. And let me just get your get your image there. So I feel like I'm talking to someone at the same time. Amen. Okay. Hi Kalam, so I'm Thomas. We already know each other.

Um, so I'm happy to present you and my company pollinations.ai our company Elliott and me about co-founders. Um, we're building AI infrastructure, the power that powers Community creation. Um, Our unique angle is monetisation through ad Tech Partners. Um today I'll show you a first path to shed success.

We are we are the problem. We are solving is that, um, in the devs face, Monetisation. Blind spot.

The, the in-depth lag specialised tool to integrate ads naturally into the sorry, I'm really sorry. I'm I don't I'm I have the Good thing I do this now because I would have, I would I would be very confused later. Let me just check where? Where I am here. Um, sure.

Okay, well, I'm just gonna go with what's written here. I'm gonna fix it later. Okay, um, this was supposed to be more about the depths the depths like access to tooling not. Not only the monetisation problems, but let's go. Okay, these guys face a real monetisation blinds. Sorry.

This Dev space, a real monetisation blinds for traditional ads. Just don't work in AI interactions. They lack specialised tools to integrate as naturally, interior, generative content. Result amazing apps with no sustainable business model. Um, advertisers struggle to reach these valuable AI, native audiences, effectively. Uh the inventory is many small apps, so it's quite fragmented.

Um And um, there's no unified access. Um, the standard Banner ads, completely missed the mark in this Rich AI experiences. This Gap is what pollination solves. Connecting both sides of this AI native ecosystem. Um, A solution. With their creators from idea to income with zero upfront costs. This is crucial looking at this flow diagram, it shows how we connect the pieces of developers.

Create apps with AI media apps, attract, and engage users, pollinations integrate add Solutions and revenues generated and shared Um, in order to make this happen we are we are um, we are already partnering with um, garlic and next up, they are a new generation of um, AI um, company, AI ad companies that, um, manager manage a large number of of advertisers and, um, have technology to connect this contextually to to, um, To generative content.

So the idea is that, that with our Partnerships with these two companies, um, they, they will they take care of bringing the actual advertisers to us and they will. And garlic has already agreed, um, to make a portfolios specifically for us. So they take care of the logic, that selects the correct ad for the right moment.

Um, Let's go back to pollinations. Um, so um, Our community builds diverse apps on our infrastructure here. You see some screenshots. Um, these are from from web apps, our community has created. You will see everything from Um, image generators to even audio experiences, AI companionship and in all kinds of languages and niches.

Um, And I will go now to a specific example of an of of an app. Built on. Uh built on our platform. So this is a create a Yomamito, he's 18 years old and he's from Indonesia. He created this app and he's very good at search engine optimisation. So he created a free app based on pollinations that currently is number one on Bing when you search for uh image generation um with AI.

So uh and here in this image, we're showing you we're showing two things one, we're showing the chart that's powered by um, pollination. So both image and text generation are handled by our back end, and we're showing, um, personalised ad that is created, um, and that connects directly to the, to the content that is being talked about.

In this case, Uh, it's subject. Yeah. Sorry. Can you is it? Uh, maybe too small and you can see if I'm on the right. Is that the thing at the bottom? Yeah, yeah. It's too small, huh. It's too small. Yeah, yeah. So obvious, yeah. Maybe they have a big arrow pointing to it.

Maybe, I've even blow it up. Um, That's good feedback feedback. Yes. Okay, I'm recording. Um, So the dynamically AI generated image uh, targets the targets the conversation. So in this case um, We are talking about Morocco and learning French. And this user gets served, an ad that speaks directly about this and takes into a language learning.

Um, app from which we earn Revenue if the user signs up. Uh, if they come back so But qualinations is not just web apps. People have been into our community is very active and has been integrating. Pollinations models into all kinds of experiences. One of our, one of our powerful.

Real world examples is AI character RP. It's it was made by an 18 year, old Ukrainian developer. Um, who, um, who has made the number one, AI game on Roblox. So if you search for AI on Roblox, uh, character, I have RP will be number one, and it has an impressive 2 million of monthly active users.

In this case, we, we've already established a revenue, sharing agreement. So we're we're piloting with, um, with this user we're piloting, our Revenue share program that we will roll out. In the future. Too many more clients. Um, Attraction. So, our flywheel is already is is not just theoretical it's already in full motion.

We've reached more than 3 million monthly active users generating, 100 million pieces of media per month. Um, that's a lot of places we could in place place ads if we want to. We're seeing consists of organic growth of 30, month per month. Um and our community has built over 300 apps with more or less two new ones being added to our catalogue every day.

So we're turning into something comparable to having phase where the community is, is contributing. Uh, a large part of the value, let's say, Um, this traction validates, our model ensures, we're solving a real need in the market. I also urge you to join our Discord community and see the the, um, the let's say the interactions.

Um, and, and How Lively everything everything is the market, okay? We are, we are targeting the mobile and web ad spend outside of World Gardens. That means outside of, um, Facebook Tick, Tock and things. We cannot, um, embed AI AI? Um, Uh, let's say AI experience into and um, Out of that, we estimate 20 billion ad spend on Indie apps and we will take we plan to take four percent of this, um, which leads to a 768 million.

Some Um, are in front ad Partners tap into this growing market. And this is conservative, I would say So, Ideas from home to profit. Um, I'll take you through our free through the three-step Journey. So I created started the seed stage which is, um, no worry. I can take a little breath, I think sometimes I should also take a breath.

It's good for me to take a small breath too. Yeah, yeah. In nine minutes, right? Yeah. Yeah. Let me just see if they were, they were still confirming whether it's whether it's exactly. Yeah, I, I can, I, I can, I can go to the end and then, and then maybe we we chat afterwards, or, or should I Yeah, yeah, yeah, okay.

We don't I don't have to go through it as a pitch, you know, but just like here. I yeah, yeah, I introduced the, the let's say the three stages. Where the the seed is like the anonymous kind of free access and the ideas that we, then onboard users into like a program where they then kind of um uh become part of the revenue share after two steps, right?

I think it will, we have an example of an app. Um, Yeah. How much they were? They would be making? Um, yeah. And then we have a team. Uh, this one. I don't know. Should I put you there? It doesn't matter. Don't worry about it. Just put it in.

It's, uh, you know what? Uh, I, I would like to just make this in one minute, share my reaction. Yeah, yeah, yeah, yeah, yeah. It's, uh, I already know, know some of this, but not all of it. So, Of the positives. This is just like, wow, clicking through your slides and thinking.

Wow this is crazy. This is such a big deal. This community is growing um, on the the negatives. Uh there there are no negatives. It's just the I was just Uh, you're the most exciting parts of your story for me, began. We started telling me who you are whole combinations are.

So, the thing was about slide three or slide four, where you start talking about, what is pollinations. Um, and I and I think that, that was yeah that slide with all the thing, all the the screenshots and it's just this sign was just like, for me. Wow this is this is such a great way to lead like so this is we have probably share, this is what we, this is what people are building on our in our community.

We're we're giving them API access to. It's like, yeah, contextualizing like like what what pollinations is because I think for it's hard to nail that down initially is it the platform? Is it a product? What is it? So but then something you see this like oh people are building apps on top of you.

And then you go into and then the next slide you go into what those apps are and two of them are number one apps. Yeah, being one in Roblox.

So and then I think when you go back to the beginning, the first slide So you say this is, this is all great, you know, you're growing really well but there but there's one problem that that everyone has they can't monetize. Uh, any of this yet. So we're we're solving for that problem.

I think then then everything else. Falls into place. We got this crazy. We've got this, uh, People are building all these amazing apps on a, you know Community. Yeah. Using our, our services and uh, two of them are currently number one apps. Uh, yeah, yeah, yeah, yeah, yeah, yeah.

One of the largest. Okay. Kids and Roblox, the largest social game. Yeah. Yeah, yeah, yeah. Yeah, yeah, yeah. And, but, uh, and this is amazing. Every day we're seeing two new apps, new use cases coming up in the Discord. But there's a, there's a problem for all of these developers and that they there isn't a way for them to monetize their apps easily right now as as you would have seen a mobile app ecosystem.

Yeah. So we are building that that we are building the solution for that. I think that kind of startup is quite cool because it's already, you're not trying to figure out what is pollinations. It's, it's like true stainful but you got that out of the way. Can you set the stage?

So you think I should pull this, the the screen, the the app, this one I should pull forwards, you think? Um, Who you are and and what people are doing in your thing. And then this is and how exciting this is because there's two apps that are number one for example is examples.

Yeah. Yeah. You dropped the nail. It's like, but there's no real. There's no quick way to monetize. Yeah. Yeah, yeah. Yeah. Yeah. I like established because it's like mobiles and all these others. They got ad-tech in place and at systems and now and so we're going to this emerging space, good garlic, and next add.

Yeah, yeah. Yeah, I think that that just tweaking. That tiny bit. Test it out. I don't know if you want to do it on this call. Which yeah. Yeah. But uh but for me the only the only thing I would say to you is that story about who you are and those apps being number one.

That is the thing. People want to hear as quickly as possible like what is it you do? Who are you? And, uh, Uh, because it's quite dense when you get into like so yeah, do you think it's too dense when I when I get when because I went whenever whenever I get into, okay, I have to leave soon but, uh, there's they said they just wrote they are, they are five minutes late.

Um, so so it's we're not in such a rush. But, um, so then I get, I, I feel like I lose a bit of steam because I'm going for these these numbers. And also the let's say, I'm not the natural guy who gets excited by let's say Market sizes, right?

So I'm I'm then going through this like series of slides, where I feel like there's, I'm somehow losing a little bit of momentum, and I'm wondering actually ending on the the slide with the the traction. So you're 30 month and month and all those Millions blah blah. Yeah. Yeah, does add it here, you know.

And we're growing like this and so, but raising five billion euros to build up the At add modules and and um, other stuff and then if they say, oh what's your business model? They'll be like, then you go to that slide. Yeah, yeah. That's kind of where you just pause.

Breathe. Take the room in and then decide you want to go further with them? Yeah. Oh that's I I like that approach because like I kind of like I mean of course we're following the, let's say the the one two, three of making a page and of course they also they've also asked first, these numbers, you know, but I also, I mean we we have, we have a q a, and a pitch and, and like it does.

I don't think we need. We can be a little bit like, uh, with the different timing, you know, than than I. I think that's kind of nice. A really important thing. Are you saying it just takes one line of code to activate? Monetisation. I know? No, yeah, we, I think that's actually, not a, not not very accurate.

Uh, um, because Actually, one drop-in line of code. Is this sentence came from, Well, actually no actually, it's correct because look, Because look, it works. It works this way. Um, pollinations itself is is so simple that if you, if you wanna like, add the very, very basic, um, let's say image or or chat interface, where it's an exaggeration, right?

It but it would be something like three lines of code, okay? And and then since we can already send ads within the within the call content that we respond, you would, you would actually, this is not not a lie, you know, you would be, you would have with a few lines of code.

You would immediately have a an app that can be monetized, but I haven't thought about it properly. That's the killer. Yeah, yeah yeah. I know my friends who had a start when I went on my first, the first startup, I got excited about that. Was their killer line. Uh-huh.

Uh-huh and monetisation was coming in. It was just one line of code for For uh, maybe a referral traffic, maybe one can even so, yeah, yeah, yeah, yeah. Just it's just, it's just a, uh, Is as quick as putting in one, so I think. But the point you want to get across this is implementation.

Is a no-brainer. Yeah. For every app developer. Yeah, yeah. So what what? I'm not. So I think if that is the case, you know, even if it's relaxable, it doesn't matter. Yeah. Yeah. Whatever it is find a way of describing it. It's it's it's it's it's really easy to implement, it's just a line.

Maybe two lines of code, you put it in. Yeah. And then you're, you're off, you're you're monetizing, you know, ad Network, you know, ad system, that's cool. Um, Yeah. Yeah, this is the thing. Maybe. At some point it might be helpful for me to reconnect with CEO Alicia. She's she's like she sold the company and And skin links it was called and it's called something else later.

The first, you know, first into that wave also yeah. And uh, and she could she could uh I don't know what she does. Now she's doing different things that she could ask be also someone who helps with the picture. Maybe he wants to invest. Um, hey Carla. I think I need to, I need to jump.

I don't want to risk. Arriving, okay. Okay, takeaways takeaways who you are that first bit super exciting. Uh, the second part is uh, but there's in spite all this. There's one problem, you can't monetize, we're making it easier to monetize with one line of code, just stick with that, see where it goes.

See how people take that? Okay, yeah, yeah, yeah. Oh, thank you so much Kalam. Um, Thank you. Thank you. See you? I write you after.

No mean.

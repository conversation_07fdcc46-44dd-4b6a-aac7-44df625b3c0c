Okay, a few General comments and then we can go a bit side by slide. What I like is. Add space thing with the three steps. That is really easy to understand and that works. You mean you mean the like how we onboard, how we onboard examples, so I, I mean you can take out that one slide with the four busing examples, so it's just irritating.

Okay. Okay. Yeah. So on your on your first slide uh the thing that turns all the time, I mean you can't even listen. Yeah, yeah. Uh, I mean So the two two things are a great thing. Yeah. The the Greece example, where the app shows Everybody gets that. No, because that is.

I mean Okay. So, so that's together with this flower nectar, the three stages so. So that's your core. No, that's the core thing you do. Yeah. Of basically AI. I mean, I like your slogan. Yeah. From prompt to what was it? Yeah, yeah, I know, I know which one is.

Yeah, yeah, yeah, from prompt to profit. I think it was right. Further down. Ah sorry. You want me to go? Yes. Except that was a slogan I liked from. Yeah. Uh, from three to yeah, one more. Oh one further here are here. Confront to <PERSON> that is great speaks to AI.

Yeah so yeah yeah but this is actually what you're that that says it all. Yeah, yeah. Basically, you're using the Google model. Not for search results, but for AI chats. Uh, that's I think that because we're often talking about kind of more complicated, uh, comparisons but actually, that's the that's that's, you're quite right.

That's very easy to understand. So that's why I say. So that's your core engine. Yeah, yeah, nice. Because it's simple, everybody understands you need to optimise the wording a bit. Yeah, scale. And sell fund, you know. Yeah. Complicated for for something like the next step that they generate some app Revenue alsoever, you need to simplify that a bit.

The nectar one is nice. Yeah. With the 50 50. I always think of the 30 of Apple. Yes. All you need to kind of in your mind to balance it a bit. So but that's your core engine. You know, of your business model are the two examples. They're super nice.

I mean the Roblox one is a bit lengthy. It's not so catchy. Also it's more interesting one. Yeah but um the other one is very clear. You type in two things so that's that's To to profit. Yeah, so the grease one, you can exactly show. So in that example, I would show the three steps.

Yeah implicitly. So you're basically saying like look The guy does that, that's the app uh, the holiday app and so on. Then you you follow up with the text, what's happening? And then the app comes. So that's already your flower piece. Yeah. That all of a sudden now, um, yeah, the first one is that people are using it.

You can just say, okay, uh, took them three months to create a community, who is using it. Then they moved to the flower. I mean, you're not explaining that but you're showing that that now, that comes, but maybe maybe we should have, like, a very clear, like, even with icons, right?

Like a flower. Uh, okay, yeah, the flow is okay, I mean, that's the standard flow, but yeah, basically you're showing this Greek example as the solution. I just want to follow your example and I'm saying, oh great. Yeah, okay funny. Yeah, thing and then Than the app appears. Yeah.

And then you just need the moment where let's say, the the revenue is shared. Yeah, yeah, yeah, yeah, yeah, yeah. Yeah. And the same you do with Roblox sir, which is kind of slightly a bit different app. But I like that job because the one is like a chatbot and the other is like a game also.

So that is perfect. There are two worlds that people easily understand and I mean I don't know what an AI game in Roblox now is but I know what a game is. So for me that AI aspect there doesn't come come really through. Yeah. Why is it now the number one AI game so yeah that one I don't fully crap but basically you also want to say that some advertisement appears and it just the visual doesn't show it.

Yeah, I get your pointer saying. Okay, there's one example, the grease example, the Roblox example, you see how the app appears. So why I'm and then afterwards, I learnt that you get these three steps. This is basically how it works. So yeah, it's super simple and that is great.

They they like a simple model. So the downside with a simple model is that you have a big competition. Yes. I mean not your your direct competition there but basically it's the Google model. Yeah. Yeah. Yeah, amazing advertisement into a search result list. Yeah, and So actually what I want to hear at the beginning, you know, when your show the what are the problems The problems are how to generic us or in the first three slides you lost them already because it's kind of bullshitty Bingo.

I mean nothing against it. I mean yeah yeah the right thing but it's a general terms that you put there. What we say is a struggle to monetize. I guess every every company put starts, right? You're not 10 years ago when it was the first step story. Yeah. So basically, everybody knows how that works here, what you're describing?

Yeah. So you need to here be very much on what is now new in AI. What is now the app? Create so the struggle commoditized, you need to now take an example. So well, let's stick with degrees example. So what is now difficult to put that ad in this chatbot?

Yeah, yeah, yeah, yeah, yeah, yeah, yeah. Where else that issue? Why is Not everybody else doing it because the logic to put an ad there is not not newer. Yeah, yeah. Now the challenge in there. Why, why is not everybody doing that or tomorrow? Google or all the big platforms?

Yeah, just offer that. Yeah, the next 45 Shopify or something. So you need the, the challenge is not the general thing because everybody knows app stores and Google. And so on. Yeah. So what you need to put here is the specific AI game challenge. There can be a technical challenge.

Yeah. And you're saying all we have a technical solution that makes it super easy. There can be a business challenge that something. Or is it like the context? Yeah. Yeah. What do you think of the following? Um, maybe one would phrase it differently but, um, Uh, and this is more technical, right?

Like, which is the fact that like, Um all AI. Let's say platforms, they require you to go through series of complicated steps, which especially, especially involved using setting up and maintaining a back end server because um, to use AI apps. Um, you, you have to, um, you have to deal with authentication, right?

Because, um, you cannot like say save Before the challenge, that's the first slider. Yeah. Yeah we'll get to that back but yeah. Um, well that you that you don't need a back end but maybe that's too technical like that setting up. A back end is complex and pollinations takes all that complexity away for you.

Yeah, it's a bit too technical. No, I'm thinking okay I mean it's good to know what if that's the issue. Yeah. Yeah yeah yeah. Yeah. Then you need to show. Look, I would put probably Anyhow. So this slide and the next slide are a bit double. Yeah. You're losing even kind of the same words like the problem.

The other we solve the problem. Yeah, yeah, yeah. Well so that's not really going. So what you could do is kind of Um, on the challenge side, you could show in a, let's say an abstract way. How such? At in an AI game happens, huh? So, Let's say you put a chatbot that's your Greek example.

And then you say it has whatever three components, a front-end or whatever the front end and the back end. Keep it simple. Yeah. I don't know. Yes or you show that and then In that model your show, where's the problem of the advertisement? So why where's the problem that the advertisement doesn't get in there?

Yeah, I mean, that's, that's always a little bit, uh, difficult. Even for, uh, we were having a little bit of struggle how we framed that as a problem because we we're basically saying there's all this untapped ad space, you know in the like like this there's space, which has a lot of Engagement, but there's no ads there.

But I mean it sounds a bit weird that this is a problem, right? So the thing is what you're addressing here. Is a common problem out there. Yeah, yeah, it's not a new technical thing or whatever that you need to explain. If if you have investors and a lot of investors invest in let's say, b2c markets and and marketing driven things, they know all the ins and outs.

Yeah, so you don't have to explain them that there's untapped space. So if you once you say you're addressing, uh, The Indie Market or whatever you say at the beginning for Indie things. Yeah. Already that talk. So then it's already clear and and then you just need to name the general problem.

So what is go to Indie apps or any games? So those are things that exist out there and take a common sense. Um, approach or issue. So, how how do I advertise today in MD games? Well, without AI. Yeah, yeah. Yeah. How do I do that? And there are also platforms out there where I can do that, or I go to a marketing agency.

That does that for me? That is specialised in that field, huh? Yeah. And how do I do it? So, in a game engine, I don't know. I have a function where I can place banners. Oh, I don't know. Yeah, I mean, you know, you know, you know, you could get a backpack with a, with a logo on it and when people clicked on it, I got some money.

Yeah, yeah. Yeah, exactly. I mean, you know that Unity, you know, Unity. Um, yeah. So so Unity is actually um, quite comparable, um, to us in the sense that they offer, they offer the infrastructure and like tools to build games, right? And then once a day their uses of build games, their primary way of making money is that they, that they send ads into the games, that people may With that tools and that actually is finally very com.

Of course we are, we are focusing, not on purely on games. We're focusing on the AI. We think I I will be Universal so potentially even more Universal than something that's just focused on games. But what what I yeah, what I'm getting at is that they when when we look at, when we looked at the history of unity, they started more or less with three million users exactly in the same situation as us with with.

Uh, and and then they started there advertising model and then they, I think they scaled it up to a multiple billion. Um, dollar Industries. Another great, great story. Yeah, what I would expect here is like look, this is how apps did it. Yeah, yeah, yeah. So we're saying okay they got a download fee.

They got a revenue share and so on. So your apps, the next level, where games You have a second one games and you're saying we're building AI and basically what you're hinting at. We're building the next Unity where we're building the next App Store. Yeah, yeah. So in that, that's something that people can easily follow and you can have two bullet points under the apps.

So, how did they technically do that? Yes. Or how do these apps show up there? Yeah, yeah, yeah. I'll get their monetize. It then the games. How did they technically get it in there? How did they monetize it? Or what's the success of it? So just like here bullet point.

Yeah. And then you were saying, Uh basically so that's your and then you're saying, okay, where's the challenge now in AI So why why can't Unity just do that for an AI game? Yeah. So where's the issue? Why can't Apple do that? Yeah. Yeah you need to point on.

Is it a technical challenge to get it into a chat and understand that context? That's maybe what they can't do. So then you're saying our solution is actually a technical thing that can understand the context. Yeah. Or that your your USP or your solution is that you have now.

But I don't think you have a USB there because all the advertisers. Yeah, they say go to an agency. Place me. Yeah. Your target group multi-channel place. Me an apps in games and in AIM. Yeah, yeah. Well, your Target customer at the end, are actually advertising agencies here for a certain user group and now you need to see, where are their challenges advertising agencies, why you can't replace ads in an AI game?

And then you're saying well I can offer them, this contacts tool. Yeah, that understands a chat and probably at the end you're just you know you can then offer that as a context feed to an advertiser who has already the whole infrastructure and all all Customers. You know, so this is you, you know, this is fine.

You know what I mean? This is, um, this is something also like now. Um, And so there's one thing in general, which is like that. Actually a real kind of a real like speciality and a real like magic right now. Right is not on the outside. Of course, we have a we have a prototype that's very compelling and that I haven't seen anywhere else yet.

But let's say, oh, are like investment in time and are kind of mode if you want to call it right is more on the community that we've built in this kind of flywheel of like where now there's many YouTube videos about pollinations people are building on it and and people are yeah I don't want to build now that advertisement Channel because they all exist.

Yeah yeah I mean if you Google uh let's say in blockchain, you know, they're yeah marketing agencies that are specialised on Discord and pumping up stuff and so on. So if you Google Um, how do you call New Media Channel marketing agencies? Yeah. For the Nikes and Adidas and and Soviet.

Yeah, yeah. So you you take a prime example of them and saying, like, look, That they all exist. Yeah. And you want to basically just offer them a new channel. Yeah. Nothing else. Yeah. That is your untapped. Yeah part. Yeah. Yeah, yeah, yeah. You open them the door because they can't do it themselves and then you're saying, well for the big ones and specialised ones they can licence our team.

And then integrated basically in their own channel and mix and so on. Yeah, then they can place it through. You directly into whatever you offer there. And then you have others there. Maybe not that advanced there. You offer them. Let's say a placing service. Yes. Or you build that around?

Yeah. Yeah. But you're basically not saying we become the advertising, guys? Yeah, yeah. That's exactly what we kind of in the position that you're hiring at the end. You're not hiring a marketing and growth guy. No, you want to either hire a partner manager who has experience in this HOC business.

Okay, wait, this is super important because we've been super confused. What we what we should what we should say there. Um, this job description I think is very a partner. How did you say it? Yeah. Now you need to share the slides again. Yeah, yeah, yeah. And back here so go.

If yeah look because what you're, what you're hiring depends on what you try to achieve there in the front. Yeah, yeah, I'm, I'm just ideating. Yeah. You know, and it's also true that this was quite recent that we that we realised actually, we shouldn't, um, try and do the whole outside.

So that's why. Look knowing you. And I mean also here, Team to scale monetisation. It totally doesn't fit to you as a team. Yeah, of course. You can take that out. 10 years of collaborating. Yeah, take it out right? Yeah, you can say that. But yeah, it doesn't matter at all.

So if you say, you know how to monetize, well, actually, you don't yeah. Because that's exactly the position that you're missing. Yeah, yeah, yeah. Yeah, yeah, yeah. Totally, what you need to say is that your strengths is actually on the community building. So, also sets the vision and strategy, okay?

And breakthrough Innovation, so no, you need to talk here. Not what the role of a CEO is. Everybody knows that you need to say. What is your experience? Yeah. And what was your success? Yeah yeah, yeah. Is that whatever you acquired already funding? You grew already something. You developed already successful tools.

Yeah. And Eliot also did something. I mean CEO all means like Um, okay, he knows how to run it. Yeah, yeah, yeah, yeah. Well, why does he run it? Well, you work for great companies and so on. So this is what you need to put here, name dropping? Yeah, yeah.

Well. And then you're saying, okay, we are aware that. Of course we need now that, uh, advertisement Channel, you know. But look this is what you explained and earlier, we learnt that this all exists and they are just missing the next Channel. Yeah. You know. But you don't want to acquire the Adidas and the Nikes and whoever yeah that exists billion wise out there and yeah, you even don't come from the background, you know.

Yeah. And that's we've sounded a little bit weird you know. And then you're saying, I hired the tech guy how to build that channel but you, you would now need to say, I need what I and I would even say you need to hire a partner manager who has a significant experience in, um, Innovative marketing agencies or something?

Yeah. And then you can even say what else you need. You also can You not just hiring, but you maybe also need strategic Partners there. So that is then on your next slide on the investment slide. Yeah. Yes. Are you actually looking for? Well, look, maybe one of those leading Edge marketing agencies.

Yeah, they actually would like to invest in you because you have an untapped market and you have experts in in building something cool, for such a community. That's interesting. Own the community. So if I'm an investor Yeah, which investor want to invest in that community and get access to it.

And, of course, I'm the marketing agency specialised in these nerds that are playing these AI Roblox games. Because I have customers who want to advertise it. Yeah, yeah. I invest there that my competitor. Come use your channel. It makes sense. Yeah, yeah. So that is a strategic investor. If you are a financial investor then you want to see like ah okay you understand the game you have a partner then you would say I need two or three of those leading agencies.

No, we basically bring the marketing placement. Yeah. Online Network or something? Yeah. Um, and and then you, then I want to say like, oh, do you have them already or not? Maybe they are in your advisory board or something, but here you would now say well this is actually what antler can provide you because those this is a network they have.

Yeah. Yeah. That makes sense contact with them, the ATM, tell you what works and what not? Yeah. So this is the expertise. So what you do then in front, you show how great you can grow that Community because this is what you have. Yeah, yeah. So you you showed already how the guy moved from, uh, from nothing to the number one game.

Now, he moves in your flower stage. Yeah. So the the revenue share is basically to drive your community. So it's a mean to an end and that that was kind of a little bit the problem because we were like we were, we were realising. Okay, our customer in the end.

Right. And then we ended up going on a deep dive on, on the advertising side. But also the people we talk to, they can hear immediately. We are not from the advertising space, right? We we don't even know what SSP means, you know, or something like that. Yeah. Your in your setup.

You know, you are the guys who can handle a nerd Community. Yeah, yeah, yeah, yeah. Yeah. No. And and this is what they buy because they can't access and handle the nerd Community themselves because when they say we are advertising then the nerd communities says leave me alone. Yeah yeah yeah yeah.

Yeah. But you are you're basically the trust bridge. Yeah. Because the agencies can deal with you and the nerds trust you because you deliver them some value and then they accept that. You also have the advertisement or you have a mechanism to monetize that. I mean like for example, we were, we were making even like we we're involving our community also like when.

So for example, I've been making surveys in our community, like what's there, what what kind of monetisation would people prefer and stuff? You know, so we have quite a strong kind of trust. Yeah. As you say, To your clients. Basically these advertisers? Yeah, and the advertisers are mainly the agencies.

Yeah. Yeah, yeah. Few people do that all themselves, huh? Yeah. Find these channels who do who does all the placement in the games? Then you find already the big one. So, and what do they now need? Then you're saying, well I can offer them different formats. Yeah. So basically you can have the advertisement that is in the chat.

You can. And that understands the context, you can have a plain Banner in the game. Yeah. That is a bit stupid thing. Yeah. That doesn't understand it or always, but by the name or the target group of the game you can place it. Yeah, I am offering them are basically.

Advertisement modules and the one is intelligent and smart, and that's why it's more expensive and they're the stupid ones. Yeah. They they just get them the access to the channel. Yeah, yeah. Yeah. Yeah. Yeah. So with this, you can also show that over time, you can basically offer a bit of a tiered offering to that Community.

Yeah, so that's the one side. Yeah it's like that's basically the pondon to your nectar flower thing because the nectar flower thing is the the user side and now you're saying okay. The other side here we have a three-stage product therefore plain Banner in in chat banner and whatever.

Discussion or something. Yeah. And and then you can show, okay? Now we need those partners who can specify us their formats and needs and Link it to the channels and but your pitch ends. So people need to because this is something they feel like they can provide you whether their VC or Handler and so on.

But what they now need to get excited about is why can't these engines do these agencies do that themselves? Yeah, well, then you and I were saying, well, first, we have had trust with the community. Yeah, So what? What makes you trusty? Yeah. Because you're not too blunt with the advertisement.

Yeah, you'll find smart ways. Yeah. So so that is your problem. I think there's also there's also other things which is like that, we've that We're we're totally open source and we're completely transparent about like, so the code for example, that shows the advertising. It's open source, right? So you can see you can trust us.

Right originally. So, but that's your first problem solution, thing. Yeah. Yeah, yeah. Hate advertisement. Uh-huh. Uh-huh. Okay. That's interesting. Yeah. Completely new new angle. Yeah. Much deeper into it. But what I know from the gaming is, you're deliberately in the indie game because there are all people who just want to play.

Yeah, they don't want to make money. If they do get some money, they donate it or develop further. You know, they're not business. They're in for the for the topic or the challenge or the idea the fun. Yeah. Yeah. Yeah. Yeah. So you need to picture a bit your community.

Yeah. And then saying we are there trusted partner because we have ABC. Yeah yeah. We offer that in a smooth way and so on. Second, you have a technology that allows these smart features. Yeah, because you know, putting in a banner on, on an AI app, in five months, it will be possible for everybody, you know.

That's not a problem. I like I get if I have a website and how I get the advertisement banner from the website creating toolbox. So Yeah. But I mean, like, I I, I totally understand. Um, but I, I'm so, so we're developing this like, clever, let's say contextual, um, ad system, right?

That uses AI even the image is generated, right? It's just a proof of concept, but it's even generating the image based on the context. So in that case, it made an image of Greece, um, which I think is really cool and, and also, um, like quite futuristic, um, but I do believe that, Like it is the amount of people building on pollinations that are are kind of are kind of thing.

The the clever odd thing is more like a bonus on top. Yeah, you have to trust with the community. Yeah, yeah. You can show that you have, what three million users? Yeah, yeah, yeah. So and you so you have the problem and solution, you know. So the problem is you need the nerd Community because this is the untapped space.

Yeah. Um, the solution is we are the trusted one because we are second pieces. Okay, we can grow that Community. Because we we have attractive tooling, you know it's it's not just the nerds but also the half nuts. They like working with your toolbox. Yeah. And that's why there's a growth potential in there.

Yeah, yeah yeah. This is because you're a great technologist. Yeah. There's also there's also the flywheel thing in in the sense that our community has now built libraries that make using pollinations easier. So so we have a whole bunch of tooling that is created by the community. Is like the trust then the community and the third is smart monetizing.

Yeah, yeah. Well, those are the three levels, huh? So And then you're saying, okay, what, what is the problem? Well, the first problem is, you can't reach that untapped field. The second problem in the community is nobody has such a cool toolbox for for AI. Yeah. Yeah. And the third one is, well, you can now do formats that you couldn't do before.

Yeah. Yeah, yeah. It's not just putting the banner now in the chat. Yeah. But understanding the chat and have these banners in a dynamic way and maybe changing and so on. Yeah. So smart. Yeah. It's like the banners in football, you know? So then you have like, three elements that you then, with your numbers can nicely show.

Yeah, you can say, what is this untapped potential there? Yeah. What is the growth potential in the community? Because you go from nerds to half nerds at some point AIS, Main Street. Yeah, exactly. Yeah. And then the third one is the smart ad. Uh your your multipliers the agencies because if you get one agency you get 50 customers.

Yeah. You know. Yeah. So that's why you don't have to go to the end customers. You just need them to Showcase something but you need to multipliers. Yeah. And then you have, you know a bit of a framework that that goes through your stories building on your strengths handling and making it attractive for the community being a trusted bridge.

And partnering with what already exists. Yeah. Yeah. Then you have a smart smart growth model. Yeah, in business model. And then people saying okay I see what you need. I like what you do Here, I can help you. Yeah, yeah. Okay. Can I show you one? Because we were just we just had a call with this company today and they it feels like they are, they are doing.

Um, so so they were very interested, they just starting, they were interested in us, becoming a pilot, a pilot um uh partner of death because like we they are doing basically uh this ad piece, right? They take the they take the context of the conversation, they don't they don't Supply the AI models for the conversation, they only Um it's what I?

What I send them, this this um, start garlic agencies, I send them the context of the conversation and they, um, have a have an inventory of advertisers, and they were quite interested in working together because we have such a large number of users and they were they and they said they would even like look for a they would they would research which Advertiser fits to our public.

So what would happen is we would potentially exactly an example for such a partner. Yeah. Yeah. Let's say an Innovative new type of partner you'll probably have also some established Partners. Yeah. Yeah, this is something. If you have two or three of those examples, It shows that we already looked into that, you don't want to do it yourself, you want to find the right ones?

And this is exactly what your network and the antler guys, where, where you benefit from them, because this is what they have and what they are interested in. Yeah. These kind of things. And actually, actually, it was finally it was an uh in another founder in the Angela cohort recommended me.

Like he he made me aware of that startup, right? So, exactly. The benefit you get. So, don't do that yourself first. Yeah, yeah, focus on. I stay. I'm the trust Centre for that Community. Yeah, and and for that, I need to stay credible. I need to be cool there that I have cool features that they work with your software in that sense.

And um, and I need to accept smart ways. Yeah. And they like it because they benefit from it. Yeah. So they get this 50 50. And do you do you think um like because do you think in our pitch we should because it's it's focusing quite a lot on the ad part in terms of just like screen estate and so on.

Do you because it was also rather let's say a recent um Insight that we had that we should Outsource more of the ad part to accompany like that. Do you think we should adapt our presentation to focus more on the on the other side? Because today we have this call with an investor and he didn't understand whether we were an ad, you know, where we whether we were an ad Tech company and and they didn't even realise.

We are offering all these AI models. And that the whole If you're at the beginning that's why you're saying your first three slides they don't work. So it's not clear what you are. Yeah. Yeah. And this was after three meetings, you know, after three meetings we still couldn't make it clear to them.

So I thought like we really have a problems of our story for data Union and it it was still not clear to the people that Marketplace role. You're in an Innovative Tech space. Yeah, I say what the learning, so your, your advantages that your customer is crystal clear, you know, the ad space is something very well known as you see your ad Tech, or yeah?

Yeah, yeah, yeah, yeah, yeah. Maybe. Maybe you are. I think no. I think you want to add tech companies. Are your customers like the guy here? Yeah. What. You're showing here? That's an attack company. Yeah. Yeah. Yeah. Yeah yeah, yeah. And the ethnic companies like the or agencies. They are your clients?

Okay. That's a that's a nice reframing. I never thought of it that way. Yeah yeah channels or Partners or something. Yeah at the beginning is You are I mean there you will find also analogies. You're basically this Indie platform. No. Yeah, yeah, yeah, yeah, yeah. It will start somehow with the logic like there are app stores.

Their Indie platforms.

Yeah, yeah, yeah, yeah, yeah. They need to show. What who you are? Yeah. And if if you're saying I'm actually 10 years ago you would be the App Store. Yeah. Yeah, I've been using the word app store now sometimes to make it a little bit easier to relate to.

Yeah you know you can start even before so First you wear the the Shopify. Yes or where you have a toolbox including the advertisement feature. Yeah, that I can drag and drop. And I use it to do Commerce. Yeah, yeah. App Store came. Now, I do the same with apps, you know.

And you have all the ones where it now says it's advertisement inside and beware, with kids and all that. Yeah, so the same then the gaming came then you had whatever sandbox or take a pool game. Yeah. That uses a lot of advertisement. Yeah. But it's successful. I'm saying look.

And now, the next one comes and your competitors are actually the A2 platforms. Who want to be AI advertisement related or something else. More the ones who build it more integrated. So, but the people are then they understand who you are. Yeah, from from to monetisation, or from from to profit.

Yeah, that was that works. Yeah, because okay, he's, he's writing a new genai thing. Yeah, using rules and IBM. He can monetize it. But also for the ad, uh, ad Tech company. Yeah, they get the profit from the prompts. Yeah, because they understand the context. So I think you can still test that and tweet that but it is something that you can explain for different things and it's catchy.

So if you're then saying, Okay. We're pollinations. I mean don't explain your logo no I mean yeah. Yeah. And then ideally pollinations would say something from home to Prophet. Yeah. Um, closures. I can give all exclusions while spreading. Yeah. Maybe finally. Yes. But then That's your action title and you now need to say who you are.

No, yeah. What is it that pollination is? Is it a toolbox now? Or is it a platform or is it? Yeah, yeah, yeah. Yeah. Yeah. Um, Yeah.

Yeah. Now you you have to choose a bit. Yeah. So are you the one for the community but they don't monetize it. So I think

I think for an investor. I think you have to differentiate how you present yourself to an investor and how you put it on the website. Yeah. So on the website you have to present that you're the trusted platform. Yeah. Yeah. Because you're actually running your community for the investor.

You have to say you're actually the smart at A platform for AI enabled. Yeah, we call it. Yeah. Because this is where your business model sits. Yeah. This is what you're selling. Yeah. You're selling these smart ad. Yeah, yeah. I like we are maybe a smart actor so it's not ad Tech.

It's not how do you handle the advertisement? The smart ad is because you have that context, you know, you can interact in the game in the chat. Yeah, so and this is something that you understand, that's at the end. You know, if they build with your tools, Um, you have access to a lot of details.

And those you can use to make intelligent. Uh format. Yeah yeah because placing a banner at some point. Everybody can do. Yeah. Yeah yeah. Protective growth path is towards smart and intelligent ways of bringing the advertisement in. Yeah, you know, I just to throw this in, I I got I got the feedback, um, that like, you know, the video that shows the path from Bing Bing to other, right?

Uh, for some people, this was not very clear because I had It's a little there's quite a lot of information on that screen, right? And also our ad, it doesn't look like a typical ad, you know? So I am. Um, I was thinking so we we should make that.

Also we could make something let's say more impressive in that, you know, ad space, do you think? But I think for Friday, you know, you can run with it because it illustrates Just status where also the technology is what you um, what I think a slide could still help.

So if you take one slide out in the front, And put that uh put add one at the end, you show the perspective. So where do you see yourself in five years? And then you would show look this app Banner which is now just a plane. Yeah. Yeah. Small Banner becomes all of a sudden an interactive, you know.

Banner. Yeah. Yeah. You know that also there's also been already I've seen now um and that that you can chat with which is quite crazy, you know imagine in that Roblox thing. Yeah. Who is actually probably sponsored by somebody? Yeah, my ethically supported but yeah. Yeah. This way it will go.

And then he needs to have a red flag to see that this kind of advertised or sponsored guy. Yeah. Is exactly take a look at Second Life. Yeah they already had all the advertisement things that you can imagine. They are happening. Yeah, yeah, yeah. That's what's a good point?

So and that was 15 years ago. Yeah, yeah. Yeah yeah, yeah. Play with it and earn money when you interact with it or if you share Flyers and yeah you know all these little things if you make them now in a smart way. Yeah. Available in your toolbox as modules, that the developers basically can use in their games You you know what?

Also, what's also really great is which, for example, I check Twitter, um, um, the day before yesterday and I found that someone has made a Minecraft a Minecraft, a pollinations Minecraft integration and that it's open source. So, um, so actually our community is also developing this this toolbox, right?

Sure. Instead of your slide with the four different things. Yeah, you need to show. Let's say So you choose one. Let's say that Grace example. Yeah yeah. And then you're saying here two three others. Yeah. Great was Roblox. The other integrates with uh what was the one now Minecraft Minecraft.

Yeah. So you actually show that you know because you're saying we're not targeting the me time Twitter Market. Yeah, with marketing. Actually, the market that is itself growing us or Roblox is a is a Target, a young Target group. Yeah. Yeah yeah. Yeah. And your community is, is creating these smart ways.

What a potential you can sell to these advertisers to these agencies to even understand. You know what? Cool there. You know yeah that's also crazy because this kid that's making the Roblox. He's teaching us so much like we have no idea. You know, he's like a real expert. Yeah.

And you're saying, we're offering the platform that we can find these kids that we can learn from them and and we help them. Earn something at the end you need to that's over time. The discussion. How ethical you design your model? Yeah. Yeah. How much does the kid get?

Or how much do you get and how much do the investor? And so but but the logic works, you know, it's easy to understand. It's a cool thing because you're in that Leading Edge, transitioning from games, I mean, look for the most people is game advertisement still the Leading Edge?

Yeah. Yeah, I'm saying, well, we're moving. We're taking you into the next level and even even if you just get that scaled by 10 and then you sell it and somebody else professionalizes it and yeah, more one of the bigger ones. Well, you know what? I, I think they don't don't like to hear that because once I, once I said something like that.

Yeah, yeah, yeah yeah. Yeah. Yeah totally. Yeah. But you get the story because they know and over time take a look at all these Uh and you know, there are a lot of VCS that invest in advertisement driven business models. Yeah. Yeah. Because they understand the logic. Yeah. They they know all the market things so they can Benchmark you better and what you're offering into that Community?

Yeah is basically. Um, A cool way to access that untapped Market. Yeah. And more you can explain how you do that and what that market is and what potential it is. So that kid. Yeah. Yeah. Just use those two examples. Yeah. Yeah and from them. You tell the story.

Yeah. You explain how you scale that. Yeah. What do you think of? Um, because, because like, actually, when I talk about our community I think I'm I I'm quite passionate about it right in a way. Um, what you think about, whenever I show something of someone from our community, maybe be too much, right?

Um, but I show like a little kind of like this is an 18 year old kid from India. He made this like give a little bit of background on the people behind it. Or do you think that distracts from the from the message? No. I mean you want to show what that untapped Market is.

Yeah. And you're gonna show. Who is that Community? So the first one is with whom. Do you want to build the trust? Yeah. Yeah. Why would I work with pollinations? The second is, who is that scalable community? So, okay, maybe those kids are these nerds are a bit like individuals.

Yeah. So how can you grow that Community now, uh, to the half nuts? Yeah, yeah. So you have to show show a bit that growth potential and then saying, yeah, the half nuts. Yeah, you know. They find, maybe they replicate some ideas that one of the nerds found. Yeah, yeah yeah yeah yeah.

Okay. And then they can learn from each other and so on. And it's still Innovative. Yeah. Um So I think it's good to, I mean, you don't have to single one out. Yeah, yeah. To describe them a bit and then you, I mean, the most powerful thing is this example.

And along that example, you explained two or three things. Yeah you explain where your your role is what the cool Community pieces and how you can escape. Yeah. And what if you show three million Community users and 2 million come already from that one thing? Yeah, yeah, yeah, yeah actually actually that that figure is outdated.

Now it's with 4 million so about 50 are coming from Roblox. Um, but what do you think of the angle which is something that's been coming more and more to my head recently which is that we position ourselves almost as an incubator, for youth Talent because we have this crazy kid who who knows Roblox super well is from the Ukraine.

He's 18, he doesn't have anything to do. He does he is a refugee, he doesn't have friends but he's such a specialist in robots and we made a contract with him so he will develop the next version for us, right? And then we have this other user who managed to do SEO for a very typical word, you know, you Up, you know, and this is number one.

So we we're finding all these kind of talents because we have this hacker appeal, right? People start to use us exactly the different levels. The hacker appeal is on your, you're the trusted nerd partner. Yeah. Yeah, that fits because your yourself come from that background. Yeah, speaking authentic story, huh?

So this is how you find them. The second level, the half nerds the community. This is where your educational Keys come in. Yeah, because not everybody is the leading hacker. Yeah, yeah. So there you can learn from the Ukrainian guy. You can copy paste it and it becomes a Learning Journey.

So, that is a way, how you grow the community, that's your second level, and the growth path. And then the third one is the monetisation piece. Yeah, that you're that, you make it real because the first two they exist already out there and the developer communities. Yeah, yeah, yeah.

Yeah. For that. They don't necessarily need you. Well, there will be another SDK toolbox and tomorrow there's another protocol or so. Yeah. But the interesting piece is now that through this learning path because the hacker maybe you want to learn how to monetize it because he doesn't care, actually.

Yeah. Now you can leave them. Into the monetisation piece. Yeah. Which is the third level. So and and that makes it then complete the whole thing. So now it becomes a flywheel because now they can earn something back. Now, you can even fund these educational pieces, more educational pieces, the more the community grows, the more, the community grows, the more of those nuts, you find?

Yeah, yeah. It's more attractive to the Nerds to scale it and then they want to go to the next project but of course. Yeah, yeah, yeah, that is really nice to show and I wouldn't position you as an educational incubator or something. I would rather say use this. It's one element.

How you scale? Something. It's a mean to an end. Don't make it too. Yeah, yeah, yeah. That comes at some point or back to our earlier discussions. At some point, you can then do a foundation. Yeah, yeah, yeah, yeah, yeah. Because it's all open sourced and you can do that.

Yeah. That's when once you exit or you hand over the company piece to a professional in that sense. Yeah. Then you do the yeah, the full topic piece because you earned enough money to to do that then yeah. Yaya makes a lot of sense. So, and I think that gets to a story where also you give a bit like a role to the investors and to antler.

They're saying like, look where with you, huh? I mean, we had five other offers so we could go and attack incubator. We were in the blockchain thing, you know, Now, we're with you because you are the experts on the advertisement. And I, I'll also highlight that actually like already.

We we we, we, we profited quite a bit from from the tips they gave us, right? And um, Uh, yeah and I think I I'm I'm actually quite happy that we reached this this kind of direction in terms of conclusion because I wasn't totally comfortable also like positioning myself as an ad Tech specialist, you know, I don't even really want to do that.

What gives me the pleasure is the other side, right? The, the hackers, the community, my thing is because you don't have to position yourself as a platform and a Marketplace. So you're positioning more like a service provider to the ad text. Yeah, yeah. Yeah. By the way. Easier and cleaner story.

Yeah, yeah. Yeah. Yeah. And it's not that you are selling the community to them. Yeah. I mean they have to be careful because your nerds might not like that. Yeah. Being sold. Yeah, yeah. So you need to find a nice twist but that's exactly the role that you have.

Yeah. That you can balance these interests, huh? Yeah, yeah. Yeah. And for the nerds who want to stay independent in a way you you pull the commercial interest and then they can offer. They can use something or get a revenue share, because they develop you an educational program or some Innovative thing and then you give them a certain share, you know?

Yeah. And they stay independent. They are not bought. Yeah, I mean they don't mind. Look at the App Store, there's so many deliberately. Commercialised apps. Yeah, there's so many who don't care? Yeah. And then it's fine. Yeah. There you can do it directly Reinventing ads. Yeah, yeah, exactly. This is, this is another company that's doing like, what this other one does.

Um, they don't they just provide a system to, um, to um, let's say, take the context of, let's say a conversation with a, with an assistant, right? And and provide a, an ad from, let's say, a large network of of advertisers, they are administrating. So that's exactly what I'm saying from a16.

Yeah, that's exactly the story that I'm telling you. So I have a meeting with them at seven in the morning tomorrow, and I'm gonna see if I can kind of put them as our partner, you know. It's the same story. Like the other guys. Yeah. So if they handle this, you see that here for advertisers?

Yeah. Yeah, exactly. You have these two sides, aways, right for Publishers and for advertisers? Yeah. Yeah. Who is publisher? There are We are we are, we are like basically mediate. We are basically the agency of all of our Publishers in a way, you know. Exactly. Exactly. So yeah, you would may so they would be your partner.

That's what I tell you. Yeah. So you need one extra slide. Yeah. Explain that mechanism and you're saying there are three types of Partners that are classical. Let's say youth agencies. Then they are the one you showed me before and then they are the one here. Yeah. Yeah, and this is what they love because in all those companies they are invested in.

Yeah, yeah. Bringing portfolio companies together and there's nothing better than for these investors. If the one pollinates the other, yeah, I was even thinking because they got money from a60, they got all the money from the speedrun, right? They didn't get back to us. Maybe our video was a bit, uh, not not not that good or there's hundreds of reasons, right?

Um, but if we are like partners with these guys, then we can approach A6 in there, right? And say, hey, we will maybe then a warm introduction. Yeah. Yeah. To them because they're saying like, look, you're now strategic partner. It's really going. Well, you should also invest in those.

Yeah. Or you can give them them as an investor or, you know, sometimes you don't need the investor. You just need a strategic customer. If they're saying we make an innovation deal with you and we pay you money. That's a good idea. That's a good idea. They got there because we're really bringing something that maybe is really a missing piece to their puzzle, right?

Which is a lot of And I thought maybe they spend 500k with you for let's say and you're saying, okay, we take that Roblox thing. Um, we work with strategic Partners there. That's kind of Angels. Yeah, and you don't they don't get Equity, but they get, let's say a first call right?

Once you raise money in the next round. Yeah, yeah. And then they can say, hey, it's really going. Well, I want to bring a 16 set in, and they should invest in us and a piece in you. Yeah. So such Network Dynamics, you can use, I wouldn't put that now all on that deck.

Yeah. Yeah. I don't know. I I, um, But as a kind of Direction. Yeah, yeah. Whatever. Aztec companies. Yeah. And you want to work with youth agencies and whatever else. Yeah. But you want to tap into those that's those are your customers. Yeah, yeah, yeah. Yeah. Yeah, yeah, yeah, yeah.

That makes a total sense. And nowadays, you don't call them customers. You call them Partners, that's why they call it your Publishers. Yeah. And here you go. And then I think that is good enough for Friday. Yeah, something like that. I mean a bit work to do for you but yeah, yeah yeah.

Um and then you see how it goes. Yeah, but even if that doesn't go, I'm like, it's not the only one who is interested in in this type of thing. Yeah, I mean I I mean like actually like we're much further than than the other startup. Further than in the, in the, in how we present ourselves.

But let's but but also we've learnt so much now, Um, that if they were to refuse us, we could now approach other investors with a much more refined. Um, And pitch and and message, you know? So of course, I'd like to, I don't want to spend too much time fundraising so the quicker we can kind of get this over with the better but uh I don't think it's the end of the world.

If if they don't if you're in your model. Yeah I mean you don't have a road map in there yet or something but you can saying like look with this Roblox thing. Yeah. What you want to get to the nectar? Yeah, yeah, yeah, yeah, yeah, yeah, yeah. And the nectar means you need to have a deal with this next ad.

Yeah. And show that there's some money coming in that you then can share. Yeah. Yeah. Yeah. That I would say, as like, it's a next step, you know, that's what I meant earlier. You need to give a perspective. Yeah. Um, Yeah, I think that's good. Hey, I have to run but thank you so much.

You took you took already a lot of time time for us and it was super helpful. Yeah. Let me know how it goes. I'm excited. And That's um, I think that was what would they wanted to ask? No, that's that's fine. Thank you so much, Mark. I really appreciate it.

Cool success. Huh. Thank you. Thank you. We will we will fight through it. Yeah, take care. Okay, bye.

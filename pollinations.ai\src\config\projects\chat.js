/**
 * Chat Projects 💬
 * Standalone chat UIs / multi-model playgrounds
 */

export const chatProjects = [
  {
    name: "UltimaX Intelligence",
    url: "https://umint-ai.hf.space",
    description: "UltimaX Intelligence is a free AI platform that unifies multiple premium AI models into one seamless experience. Powered by the Pollinations open-source AI community and integrated with Open-WebUI, it provides an intuitive interface similar to ChatGPT but with more advanced features. Users can access a variety of powerful tools without any cost, registration, or login. All conversations are temporary and are not saved, ensuring privacy and simplicity.",
    author: "@hadadarjt",
    repo: "https://huggingface.co/spaces/umint/ai/tree/main",
    submissionDate: "2025-07-13",
    order: 1
  },
  {
    name: "VOID.AI",
    url: "https://thevoidai.vercel.app/",
    description: "A humanlike AI mentor, productivity partner, and emotionally intelligent assistant that adapts its tone and energy based on user behavior. Features conversational AI that feels more like a charismatic friend than a chatbot, providing code walkthroughs, life advice, writing help, and motivation with realistic speech output and context memory.",
    author: "@<PERSON><PERSON>-Dhakad",
    repo: "https://github.com/Ajay-Dhakad/VOID.AI",
    stars: 0,
    submissionDate: "2025-07-06",
    order: 1
  },
  {
    name: "Ai drafterplus",
    url: "https://ai.drafterplus.nl/",
    description: "A ChatGPT-like interface with multiple AI models. Completely free and saves conversations in the browser using localStorage.",
    author: "@dexvisser_",
    submissionDate: "2025-06-07",
    order: 1
  },
  {
    name: "PixPax",
    url: "https://pixpal.chat",
    description: "A user-friendly chatbot that lets you analyze images, remix existing images or create new images, all through simple chat.",
    author: "@andreas_11",
    submissionDate: "2025-03-17",
    order: 2
  },
  {
    name: "Jenny AI",
    description: "AI chatbot and character creation platform with tts and sst it also has image generation and vision ability which are powered by pollinations.",
    author: "https://www.linkedin.com/in/pritam-roy-95185328a",
    submissionDate: "2025-03-16",
    order: 3
  },
  {
    name: "DynaSpark AI",
    url: "https://dynaspark.onrender.com",
    description: "A versatile AI assistant with advanced image and text generation capabilities.",
    author: "Th3-C0der",
    repo: "https://github.com/Th3-C0der",
    stars: 20,
    order: 1
  },
  {
    name: "Unity AI Lab",
    url: "https://unity.unityailab.com/",
    description: "A specialized uncensored LLM model built on Mistral Large, focused on unrestricted conversations.",
    order: 1
  },
  {
    name: "gpt4free",
    url: "https://github.com/xtekky/gpt4free",
    description: "The official gpt4free repository - various collection of powerful language models.",
    author: "xtekky",
    repo: "https://github.com/xtekky/gpt4free",
    stars: 64802,
    order: 1
  },
  {
    name: "FreeAI 🇨🇳",
    url: "https://freeai.aihub.ren/",
    description: "An AI application platform based on Pollinations.AI API, providing free and unlimited AI chat assistant, image generation, and voice synthesis services.",
    author: "@Azad-sl",
    repo: "https://github.com/Azad-sl/FreeAI",
    submissionDate: "2025-03-24",
    language: "zh-CN",
    stars: 44,
    order: 1
  },
  {
    name: "Goalani",
    url: "https://goalani.com",
    description: "Voice-enabled AI fitness coach. Using only your voice, you can communicate with the agent to manage your fitness and nutrition. Features weight tracking, exercise logging, food tracking with AI-generated images, and agent customization.",
    author: "<EMAIL>",
    submissionDate: "2025-04-09",
    order: 3
  },
  {
    name: "Neurix 🇷🇺",
    url: "https://neurix.ru",
    description: "A website offering easy and free access to various neural networks, with multi-language support planned. Provides a platform for accessing various AI models, including Pollinations.",
    author: "@Igroshka",
    submissionDate: "2025-05-10",
    language: "ru-RU",
    order: 1
  },
  {
    name: "Echo AI",
    description: "A chat interface for AI interactions and conversations.",
    author: "Unknown",
    submissionDate: "2025-06-05",
    order: 1
  },
  {
    name: "DreamBig - Generative AI Playground",
    url: "https://dreambiglabs.vercel.app/",
    description: "Interactive AI playground with chat, image generation, and voice responses for creative exploration.",
    author: "@opzzxsprinta._999",
    submissionDate: "2025-04-15",
    order: 1
  },
  {
    name: "Pal Chat",
    url: "https://apps.apple.com/us/app/pal-chat-ai-chat-client/id6447545085?platform=iphone",
    description: "An iOS app that integrates with all LLMs including Pollinations AI models in one unified simple interface.",
    author: "https://x.com/pallavmac",
    submissionDate: "2025-02-16",
    order: 1
  },
  {
    name: "Pollinations AI Playground",
    url: "https://pollinations-ai-playground.vercel.app/",
    description: "An AI application platform based on Pollinations.AI API, providing free and unlimited AI chat assistant, image generation, and voice synthesis services.",
    author: "@playground",
    submissionDate: "2025-05-05",
    order: 1
  },
  {
    name: "OkeyMeta",
    url: "https://playground.okeymeta.com.ng/",
    description: "OkeyMeta is an AI Playground that allows you to chat with different AI models, generate images, and more. It is powered by Pollinations.ai.",
    author: "@okeymeta",
    repo: "https://github.com/okeymeta/okeymeta",
    stars: 10,
    submissionDate: "2025-02-15",
    order: 1
  },
  {
    name: "Image Gen - Uncensored Edition",
    url: "https://huggingface.co/chat/assistant/66fccce0c0fafc94ab557ef2",
    description: "An uncensored image generation tool that allows for creative freedom without content restrictions.",
    author: "@flowgpt",
    submissionDate: "2025-04-25",
    order: 1
  },
  {
    name: "Pollinations Chat",
    url: "https://websim.ai/@AdrianoDev1/pollinations-ai-assistant/4",
    description: "Pollinations' integrated AI for text and images, totally free and unlimited.",
    author: "@adrianoprogramer",
    order: 3
  },
  {
    name: "Mirexa AI Chat",
    url: "https://mirexa.vercel.app",
    description: "A state-of-the-art AI chatbot that seamlessly integrates multiple LLMs with advanced multimodal capabilities. Features comprehensive text generation, sophisticated image creation and image-to-image transformation, audio generation, mathematical problem solving, and real-time web search functionality.",
    author: "@withthatway",
    submissionDate: "2025-02-07",
    order: 2
  },
  {
    name: "Pollinations.AI 中文",
    url: "https://pollinations.vercel.app",
    description: "我们提供高质量的AI生成服务，包括图像生成、文本生成、音频生成和语音转文本服务， 让您轻松创建各种创意内容。 (We provide high-quality AI generation services, including image generation, text generation, audio generation, and speech to text services, allowing you to easily create various creative content.)",
    author: "@pollinations",
    submissionDate: "2025-05-05",
    language: "zh-CN",
    order: 1
  },
  {
    name: "Rizqi O Chatbot 🇮🇩",
    url: "https://chatbot.rizqioliveira.my.id",
    description: "Rizqi O Chatbot adalah proyek berbasis Pollinations yang menggabungkan tiga fitur utama: chatbot AI, generator gambar AI, dan generator audio AI. Pengguna dapat berinteraksi dalam bentuk teks, menghasilkan gambar dengan berbagai gaya seni dan efek visual, serta membuat audio secara otomatis dari teks. (An AI chatbot, image generator, and audio generator project with support for custom aspect ratios, over 200 art styles & visual effects, and automatic translation from Indonesian to English.)",
    author: "@ray23-bit",
    repo: "https://github.com/ray23-bit/Projectenam",
    stars: 1,
    submissionDate: "2025-05-08",
    language: "id-ID",
    order: 1
  },
  {
    name: "LLM7.io",
    url: "https://llm7.io",
    description: "A free and open AI platform providing advanced multimodal capabilities, including large language model access and experimental search tools. Integrates Pollinations text generation as a backend service with transparent credit on the website and repository.",
    author: "@chigwell",
    repo: "https://github.com/chigwell/llm7.io",
    stars: 7,
    submissionDate: "2025-05-30",
    order: 1
  },
  {
    name: "SillyTavern",
    url: "https://docs.sillytavern.app/",
    description: "An LLM frontend for power users. Pollinations permits it to generate text and images.",
    repo: "https://github.com/SillyTavern/SillyTavern",
    stars: 14700,
    order: 1
  },
  {
    name: "Anisurge",
    url: "https://anisurge.me",
    description: "A free anime streaming app with a public chat feature that allows users to chat with AI characters powered by Pollinations AI.",
    author: "@iotserver24",
    submissionDate: "2025-05-16",
    order: 1
  },
  {
    name: " Comeback AI",
    url: "https://comeback-ai.pinkpixel.dev",
    description: "AI-powered clapback machine that transforms mean comments into witty comebacks with 10 unique personas, uses Pollinations openai-audio for voice synthesis, and Whisper for speech-to-text transcription. Turn trolls into comedy gold!",
    author: "@sizzlebop",
    repo: "https://github.com/pinkpixel-dev/comeback-ai",
    stars: 1,
    submissionDate: "2025-05-31",
    order: 1
  },
  {
    name: "AI Chat",
    url: "https://aichat.narendradwivedi.org",
    description: "A Windows desktop application that brings multiple AI models together in one simple, intuitive interface. Features saving/loading conversations, image generation, image explanation from URLs, and voice responses with different voices.",
    author: "@narendradwivedi",
    authorUrl: "https://www.linkedin.com/in/narendradwivedi",
    submissionDate: "2025-05-16",
    order: 1
  },
  {
    name: "LobeChat",
    url: "https://lobechat.com",
    description: "An open-source, extensible chat UI framework supporting multiple models and features like message citing and image creation.",
    author: "@lobehub",
    repo: "https://github.com/lobehub/lobe-chat",
    stars: 21000,
    submissionDate: "2025-03-01",
    order: 1
  },
  {
    name: "toai.chat",
    description: "An AI-client-free project dedicated to enabling AI interaction using only curl, supporting multimodal and MCP capabilities, to provide users with the simplest way to use AI.",
    author: "@Veallym0n",
    repo: "https://github.com/Veallym0n/toai.chat",
    stars: 2,
    submissionDate: "2025-03-27",
    order: 1
  },
  {
    name: "Free AI Chatbot & Image Generator",
    url: "https://vercel.com/templates/ai/ai-sdk-image-generator",
    description: "A web application offering both conversation with AI and image generation capabilities, utilizing Pollinations API for creating visuals based on text prompts.",
    author: "@aidevs",
    repo: "https://github.com/vercel/ai/tree/main/examples/ai-image-generator",
    submissionDate: "2025-04-22",
    order: 1
  },
  {
    name: "AI Chat",
    url: "https://aichat.jolav.me/",
    description: "A simple and elegant chat interface for interacting with various AI models through Pollinations, focusing on ease of use and quick responses.",
    author: "@jolav",
    submissionDate: "2025-04-10",
    order: 2
  },
  {
    name: "KoboldAI Lite",
    url: "https://lite.koboldai.net/",
    description: "A lightweight version of KoboldAI that uses Pollinations for text generation, offering a streamlined experience for creative writing and storytelling.",
    author: "@kobold_dev",
    submissionDate: "2025-03-15",
    order: 2
  },
  {
    name: "LiteAI",
    url: "https://liteai.chat/",
    description: "A free, fast, and anonymous AI chat and image generation platform with no login required. Features include various AI models, prompt library, upscaling, and community sharing.",
    author: "LiteAI Team",
    submissionDate: "2025-05-10",
    order: 3
  },
  {
    name: "UR Imagine & Chat AI",
    url: "https://urimagine.netlify.app/",
    description: "A versatile AI platform offering both image generation and chat functionalities. Users can create visuals from text prompts and engage in conversations with AI models, all powered by Pollinations.",
    author: "@ur_imagine",
    submissionDate: "2025-05-01",
    order: 3
  },
  {
    name: "EvilChat 🔥🤖",
    url: "https://altkriz.github.io/evilchat/",
    description: "An uncensored AI chat interface with dark, sleek design, smooth animations, auto-scrolling chat window, fully responsive. Connects to the Pollinations.ai API to deliver unfiltered AI responses.",
    author: "@altkriz",
    repo: "https://github.com/altkriz/evilchat",
    stars: 2,
    submissionDate: "2025-07-14",
    order: 1
  },
  {
    name: "DeepSeek Prompt",
    url: "https://www.deepseekprompt.top/",
    description: "DeepSeek Prompt is a front-end application for managing and optimizing AI prompt assets with professional creation templates. Built with React and Vue, it offers prompt creation, editing, drag-and-drop sorting, template application, and data import/export. It integrates Pollinations APIs for intelligent prompt optimization via an 'AI Optimize' button that analyzes and enhances prompts.",
    author: "<EMAIL>",
    submissionDate: "2025-07-15",
    order: 1
  },
  {
    name: "AI Dream Girl Studio",
    url: "https://www.tiktok.com/@herinyourhead",
    description: "AI Dream Girl Studio is a content creation pipeline built around hyper-realistic AI-generated female characters. We use Pollinations to generate high-fidelity visuals of emotional, seductive, and stylized women in cinematic settings. These characters are brought to life through AI video animation, voiceovers, and storytelling to create viral social content for entertainment and monetization. Pollinations is the core engine behind our character design and visual style.",
    author: "https://www.tiktok.com/@herinyourhead",
    submissionDate: "2025-07-17",
    order: 1
  }
];

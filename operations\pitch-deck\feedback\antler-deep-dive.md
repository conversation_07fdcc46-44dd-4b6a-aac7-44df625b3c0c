# Berlin Antler Deep Dive - 06-05-2025 15:00

## Key Feedback Points:

### Developer Value Proposition
- Need to clearly articulate what we offer developers today:
  - Specify which API and SDK features are available
  - Detail which models developers can access for free
  - Showcase examples of apps built on our platform

### Differentiation
- Must clarify why developers choose pollinations over competitors:
  - Be more user/customer-centric in our messaging
  - Consolidate our value propositions (currently scattered throughout the pitch):
    1. Monetization capabilities
    2. Privacy and security benefits
    3. No need to build authentication systems

### Advertising Network Strategy
- Need to explain how we're building an advertising network:
  - Detail the implementation process
  - Clarify how we'll continue providing free model access while monetizing via ads
  - Explain the unit economics clearly

### Economic Viability Concerns
- Address the economic feasibility question:
  - How do we handle apps with valuable audiences vs. those without?
  - Example: We analyzed one major app using generic AdSense and found the economics challenging
  - B2B applications may not have consumer audiences that are "advertisable"

### Tiered Approach Solution
- Our tiered system addresses economic concerns:
  - Free tier with limited capabilities for unvalidated apps
  - For apps with good monetization potential, we offer:
    - Enhanced monetization plugins
    - Higher rate limits
    - More resources and credits
  - Selection process can be automated based on analytics

### Business Model Focus
- Should maintain focus on one revenue stream initially:
  - Trying to build multiple revenue streams (ads, premium, direct customers) creates complexity
  - Better to perfect one model in the next 18 months before expanding

### Target Audience
- Need to clarify our target market:
  - While our Discord shows many young creators (11-17 years old), our reach is broader
  - The key insight: AI assistants are making everyone a potential code creator
  - The market is expanding as no-code/low-code tools enable more people to build apps

### Market Sizing Approach
- Should frame market size in terms of:
  - Future applications with AI-generated media components
  - Our share of those applications
  - Potential eyeballs/audience reach
  - Resulting advertising revenue

### Presentation Improvements
- Multiple speakers make the pitch hard to follow
- Need to be less modest and highlight achievements
- First-time listeners struggle to understand our concept
- Should include competitor analysis and concrete examples

The ultimate question remains: Is building an ad network on this type of platform a viable business model?




Transcript:

kind of participants in your ecosystem. So I guess you're your primary stakeholder is the developer. So what exactly do you have for the developer today? you kind of basic stuff like kind of you have an API and STK that kind of gives access to the following models and is for free today and that these types of developers have built these types of apps on top of, I think is missing.. And then from that developer, if that's the key stakeholder if you touch on it somewhat, but the why do they use pollinations and not others? I think you can kind of just t tell them much more user centric or customer centric. And then if then on top the pitch is that you're actually building the and then kind of why do they use you as other solutions? you're touching on multiple things, but what is it? So you're saying they don't have ability to monetize on one hand, on the other hand, you say it's privacy and security. On the third one you say, it's uh um kind of, you don't have to build a layout for authentication, like, but you're telling that did different parts of the pitch as well, so the I think that needs to be more to the point and clearer. I think what what what we also, because we we did, of course, a little bit last minute, um our preparation for today. um of course we spent a lot of time and energy, but it could have been more, you know. But but what I definitely am aware of is that we didn't um we're not mentioning competitors and we're also not mentioning some examples because like so maybe these two things would already help then the second part of what you're building for understand correct is your ultimately building and advertising network. then you're not talking about kind of what it actually takes to build an advertising network at all. um and how are you going to do that um and if you will continue to give access to the models that you end up paying for for free to others, and you monetize via the ads, I need to understand the unity economics. And what I kind of put in more content and less of our pitch, but I still haven't understood as the How do you make sure that this works across the board? Because I can there's some apps that I can build that have an audience that is valuable, where you can actually sell ads for a decent amount of money and then I can use a Chit ton of your tokens for free in my app and not really have a lot of valuable audience in front of it, so there's no valuable advertising inventory that I don'tolution but I mean there just to just verify what you're saying is we have this one, for example, it's one of our biggest it's one of our biggest um um let's say apps that's that not the biggest, but one of the big ones. They I I went through him because he they it's they are putting a lot of um uh typical this kind of like generic add sense advertising on their page. And I I calculated with them how much like is our cost for this and how much uh could we get is a realistic revenue share from the ads and it't this didn't look like economically fe feasible uh so so I I understand your I can come up with probably a bunch of BTB applications that doesn't actually have consumer audience that is adverticable in front of it. and then we can have like these different tiers that are like we offer something for free, but at the same time when you when it's when you up it is not like validated by us, uh you don't get like this high rates, you don't get like a few things that you don't get that makes the cost for us very limited. and then four different up that we realize, okay, this is good for monetization, then we offer all this like monetization plugging, we incensivize them to use those tools and then we we kind of like subselect um and it can be like automated, based on just numbers that we we get from analytics. But I think this is where we we would like target like certain ups and not others to to give them more like credits, more access, more resources. I think more way more detail on on that whole side of the um the equation, because the obvious obvious rebuttal to what you're doing is you essentially giving g g giving other people your cloud credits for free, uh at the moment, you're giving away something for free. that will always show attraction. is that is there viable business to build on top of that? um there is something with Thomas we were thinking quite a lot or this week it's like this difference between the different um act who are the the the because we we ended up speaking like if you check the the the the the sum uh like the the market size, uh we were like, oh, do we use the market size of the creative AIEN builders, or do we use the market size of actually the ads that could be shown to to use people that are not shown because all these ads they are built, but there's no way to put the ads in. So I think there we have like kind of like this is why we focused on the we we tried at least to focus on the ads, because this is where the revenue comes from. So the ad is actually the the money comes from there. And then it's just like going through us to the developer back. And then so this is this kind of wheel that we were like trying to think of and not really like the the this is why maybe we focused a bit less on the use the the developer or the the creator. what do we have yes? so we have we have these extra sort, but I think it's better if we listen we get your feedback because we we we have all the week then to explain to to answer your questions, maybe. um yeah, yeah. but maybe that would be a question to you, but yeah, sorry, if you have someance to say. Go ahead. Um, so because we're wondering uh, should we like, should we also focus on other potential revenues streams, then just focusing on odds? Or would that complicate the pitch? Because it sounds like we're trying to do too much, you know, we're trying to do a a premium, we're trying to do an adproduct. um I've been I've been usually on the side of trying to keep it as lean and simple as possible. That's why we're honing so strongly in on just the idea, but yeah. And customers also like direct customers, which removes like a layer of like a short, you So I don't know what the right business model is. um um but I think the the I think focus makes sense or typically when when companies in an early stage try to have four different, very different sorts of revenues., four different businesses, and you typ you die in the complexity. So um I don't know what ads is the best one, um, but I would not try to in the next 18 months try to build three different revenues. I'm not gonna get there. need to start with one and crack that first. um yeah, I don't really get entirely the use art market, finally I mean I don't really get that slide. um the ultimately you targeting developers that build outs, yes, those users and customers, I guess, arekew towards younger audiences today. My guess is that people who build with other it's like the top new market that is like being created by the the I mean that is expand, it is like all these new spaces that are everyone can build an app that can receive ads. So it's just that it's just exploding because of white coding. and then this is what we wanted to to show it like this like the the space, this this ad space that is new and that that is being created. But maybe that's not the best way to put it.. Yeah. I mean, I also think it's it's it's it's more general than than youth, for sure. um cause like I mean, I think like our key like, let's say one at least for me, you know, for me one one key kind of insight, is that like when you're using an AI assistant these there, these AI assistance now nowadays are already exec when you ask something a little bit more complex, they already writing a little bit of code for you and executing it stuff. indirectly everyone is becoming a creator of code, you know, the next step is just I. I liked this report you made, can you make a website? Or for me, and suddenly you've created your own little app. So I think this this is and then what what our belief was that when you don't have a clear target audience, when you're like kind of our we we the platform for everyone from young to old, then you then it's also a little bit more difficult to kind of like get your message um um um um uh a exactly like like optimized for for this. and and we we our belief is't that it could be so this youth audience uh is potentially so big that maybe it's more efficient to to to focus on that or on them and like it also like at the moment in develop ecosystem and if I don't my dating platform for the elderly, then I do that. Yeah, yeah. I think it's maybe we could it just a by experience on all discord, we we see a lot of young people, like this is this is like validated. um the people that shut sometimes they are 11, sometimes they're 17. I mean, we see that a lot. the creators. the engine I agree agree. So we didn't bigger numbers. I think ultimately if what you're saying is that a whole bunch of people will build applications with tons of audiences on your platform and you become the advertising network for that. I think we talk about a large number and we've then taking breath share on that advertising. um um I don't think that in the market size is the biggest objection, but I think then how would I size that I would probably it next heating or started. Um, I um yeah, then I would come somehow via an ultimate what you saying is saying that a whole chunk of the applications of the future will have some sort of GeneI generated media input into them. what we' offering is is um assistance, so a multi multit attorn who can integrate with your your your uh UI. So I kind of a share of all of those applications and then kind of how many eyeballs will those get and then that's yeah yeah yeah. Okay, um I need to now jump into my next session. Um um yeah, I think this obviously before this is kind of something that you can raise on it's still gonna require quite a bit of work. Um uh a couple of more kind of how to present observations. um this each process has one session is sentence, a super hard to follow, because you get used to a speaker and then kind of I get interrupted and need to kind of adapt to the new speaker every time. um I think that's that's harder hard. um um I think you need to sell yourselves better, you're very modest, um, um none of your bragging rights of great things that you've done in the future are in there. I think that's that needs to be part of the pitch. and I think yeah, I think overall somebody who hears this for the first time won't get what you're doing and I haven't understood yet from this or from our discussion so far, whether building an ad network on this type of thing is a good idea or not. I'm still not there yet. Um, so if me, it's not just a how to present, but I haven't really understood that yet. Um um yeah. That's my main topic. Uh, looking forward to kind of the next version. We were thinking of bad spreading in the pitch worst at tomorrow, you think that's a good idea? Yeah, take every opportunity, yeah. Cool.

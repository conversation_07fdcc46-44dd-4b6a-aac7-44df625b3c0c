# Pollinations Image Cache

This Cloudflare Worker handles image caching and analytics for the Pollinations image service.

## Configuration 

The worker uses `wrangler.toml` for configuration and environment variables for sensitive data.

### Setup Instructions

1. Make sure your `.env` file is set up in the root directory with the following variables:
   ```
   GA_MEASUREMENT_ID=your-ga-measurement-id
   GA_API_SECRET=your-ga-api-secret
   CLOUDFLARE_ACCOUNT_ID=your-cloudflare-account-id
   ```

2. Deploy the worker:
   ```bash
   cd cloudflare-cache
   npx wrangler login  # First time only, opens browser for authentication
   npx wrangler deploy
   ```

   Alternatively, you can use the deployment script which will:
   - Load your environment variables from .env
   - Create the R2 bucket if it doesn't exist
   - Create .dev.vars file for local development
   - Deploy the worker

   ```bash
   cd cloudflare-cache
   ./deploy.sh
   ```

### Local Development

For local development, you need a `.dev.vars` file which will be automatically created by the `deploy.sh` script, or you can create it manually:

```bash
# Create .dev.vars file manually
echo "GA_MEASUREMENT_ID=your-ga-measurement-id" > .dev.vars
echo "GA_API_SECRET=your-ga-api-secret" >> .dev.vars
```

Run the worker locally:
```bash
npm run dev
```

### Important Security Notes

- The `.env` file should not be committed to version control
- The `.gitignore` file is configured to exclude `.dev.vars`
- Wrangler will automatically use environment variables from `.env` when deploying

## Development

To run the worker locally for development:

```bash
npx wrangler dev
```

To view logs from the deployed worker:

```bash
npx wrangler tail
```

## Overview

The implementation follows the "thin proxy" design principle:
- Minimal processing of requests and responses
- Direct forwarding of requests to the origin service when needed
- Simple caching logic using URL paths and query parameters as keys
- Analytics tracking to ensure all image requests are properly monitored

## How It Works

1. **Request Flow**:
   - Incoming request → Worker → Check R2 cache → Serve cached image OR proxy to origin
   - For cache misses, the response is stored in R2 for future requests
   - Analytics events are sent at key points in the process

2. **Caching Strategy**:
   - Uses URL path and query parameters as cache keys
   - Skips caching for non-image responses or when `no-cache` parameter is present
   - Sets appropriate cache headers for CDN optimization

3. **Analytics**:
   - Tracks image requests directly from the Cloudflare cache
   - Sends analytics events for request, generation success/failure
   - Includes cache hit/miss status in the analytics data

## Cost Efficiency

This implementation is designed to be cost-efficient:
- Zero egress fees from Cloudflare R2
- Automatic CDN distribution
- Simple caching logic with minimal overhead

## Configuration

If you need to modify the configuration:

1. Update the bucket name in `wrangler.toml`
2. Modify the origin host in `wrangler.toml` if needed
3. Adjust caching logic in `src/cache-utils.js` if necessary

## Required Environment Variables

To ensure analytics work properly, you need to set these environment variables:

1. `GA_MEASUREMENT_ID` - Google Analytics 4 measurement ID
2. `GA_API_SECRET` - Google Analytics 4 API secret

These variables are automatically configured during deployment through GitHub Actions using repository secrets. However, if you need to set them manually, you can use one of these methods:

### Option 1: Edit wrangler.toml directly
Uncomment and set the values in the `[vars]` section of wrangler.toml:
```toml
[vars]
GA_MEASUREMENT_ID = "G-XXXXXXXXXX"  # Replace with your GA4 measurement ID
GA_API_SECRET = "XXXXXXXXXX"        # Replace with your GA4 API secret
```

### Option 2: Use Wrangler CLI
```bash
wrangler secret put GA_MEASUREMENT_ID
wrangler secret put GA_API_SECRET
```

### Option 3: Set in Cloudflare Dashboard
Go to Workers & Pages > pollinations-image-cache > Settings > Variables > Add variable

These should be the same values used in the main image.pollinations.ai service to ensure consistent analytics tracking.

{"name": "pollinations.ai", "homepage": "https://pollinations.ai", "version": "0.4.8", "private": false, "dependencies": {"@babel/eslint-parser": "^7.26.5", "@babel/plugin-transform-async-generator-functions": "^7.25.9", "@babel/plugin-transform-class-properties": "^7.25.9", "@babel/plugin-transform-dynamic-import": "^7.25.9", "@babel/plugin-transform-json-strings": "^7.25.9", "@babel/plugin-transform-nullish-coalescing-operator": "^7.26.5", "@babel/plugin-transform-numeric-separator": "^7.25.9", "@babel/plugin-transform-object-rest-spread": "^7.25.9", "@babel/plugin-transform-optional-catch-binding": "^7.25.9", "@babel/plugin-transform-optional-chaining": "^7.25.9", "@babel/plugin-transform-unicode-property-regex": "^7.25.9", "@electron/get": "^3.1.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.3.1", "@mui/material": "^6.3.1", "@mui/styles": "^6.3.1", "@netlify/functions": "^2.6.0", "@pollinations/react": "^2.0.6", "@sideway/address": "^5.0.0", "debug": "^4.3.2", "dotenv": "^16.4.5", "front-matter": "^4.0.2", "joi": "^17.13.3", "json5": "^2.2.1", "lodash.debounce": "^4.0.8", "lodash.memoize": "^4.1.2", "lodash.throttle": "^4.1.1", "markdown-to-jsx": "^7.1.7", "markdown-to-text": "^0.1.1", "mime-types": "github:voodoohop/mime-types", "multicodec": "^3.1.0", "native-abort-controller": "^1.0.4", "node-abort-controller": "^3.0.1", "node-fetch": "^2.6.1", "p-queue": "^7.1.0", "ramda": "^0.27.1", "react": "^18.2.0", "react-code-blocks": "^0.1.6", "react-dom": "^18.2.0", "react-dropzone": "^12.1.0", "react-iframe": "^1.8.5", "react-router-dom": "^6.0.1", "react-scripts": "^5.0.1", "react-svg": "^16.2.0", "react-textarea-autosize": "^8.5.7", "react-use": "^17.3.2", "slick-carousel": "^1.8.1", "throttle-debounce": "^3.0.1", "use-debounce": "^10.0.0", "usehooks-ts": "^3.0.1"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.0", "netlify-cli": "^17.19.0", "react-devtools": "^6.0.1", "react-error-overlay": "^6.0.11"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "netlify": "netlify dev", "predeploy": "npm run build", "update-projects": "cd .. && node pollinator-agent/generate-project-table.js --update-readme"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
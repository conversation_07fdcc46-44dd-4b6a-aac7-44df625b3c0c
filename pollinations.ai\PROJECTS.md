> **Note:** Some projects may be temporarily hidden from this list if they are currently broken or undergoing maintenance.

Pollinations.AI is used in various projects, including:

### Vibe Coding ✨

| Project | Description | Creator | Links |
|---------|-------------|---------|-------|
| Qwen-Agent | A framework for developing agentic LLM applications. | - | [Website](https://github.com/QwenLM/Qwen-Agent), [GitHub](https://github.com/QwenLM/Qwen-Agent) - ⭐ 6.6k |
| Open Prompt | A community-driven platform for creating, sharing, and discovering AI prompts for various applications. Integrated with Pollinations API for enhanced creative capabilities. | @markojohnas | [Website](https://openprompt.co), [GitHub](https://github.com/markojohnas/openprompt) - ⭐ 0.1k |
| Pollinations MCP Server | A Model Context Protocol server that enables AI-assisted development through natural language interaction with Pollinations' multimodal services. | @thomash | [Website](https://github.com/pollinations/model-context-protocol), [GitHub](https://github.com/pollinations/model-context-protocol) - ⭐ 0.0k |
| Pollinations Task Master | A task management system that uses AI to help break down and organize development tasks through natural language interaction. | @LousyBook94 | [Website](https://github.com/LousyBook94/pollinations-task-master), [GitHub](https://github.com/LousyBook94/pollinations-task-master) - ⭐ 0.0k |
| 🆕 AI Code Generator | A websim project that generates code from description, selected programming language and other options. Integrates Pollinations because it allows for more models to choose from for potentially better results. It has modes like: Code Generator, Code Explainer, Reviewer, etc. | @Miencraft2 | [Website](https://codegen.on.websim.com/) |
| VibeCoder | A conversational coding environment that lets you create applications by describing them in natural language. | @Aashir__Shaikh |  |
| JCode Website Builder | A website generator using Pollinations text API. | @rtxpower | [Website](https://jcode-ai-website-bulder.netlify.app/) |
| Define | An AI-powered REST API designed to generate definitions for words or phrases, constrained to a specified target word count. It allows customization of tone, context, and language, delivering precise, context-aware definitions programmatically—ideal for developers and content creators. | @hasanraiyan | [Website](https://define-i05a.onrender.com/api/docs/), [GitHub](https://github.com/hasanraiyan) |
| WebGeniusAI | AI tool that generates HTML websites with visuals from Pollinations. | @Aashir__Shaikh | [Website](https://webgeniusai.netlify.app/) |
| Pollinations.DIY | A browser-based coding environment based on bolt.diy, featuring integrated Pollinations AI services, visual code editing, and project management tools. | @thomash | [Website](https://pollinations.diy) |
| NetSim | websim.ai clone that's actually good | @kennet678 | [Website](https://netsim.us.to/) |
| Pollin-Coder | A free AI-powered website builder that lets anyone create a clean site just by describing it. It uses Pollinations AI to generate the content and layout instantly. | @r3ap3redit | [Website](https://pollin-coder.megavault.in) |
| JustBuildThings | A natural language programming interface that lets users create web applications by simply describing what they want to build, using Pollinations AI to generate code and assets. | @buildmaster | [Website](https://justbuildthings.com) |
| Websim | A web simulation tool that integrates Pollinations.ai. | @thomash | [Website](https://websim.ai/c/bXsmNE96e3op5rtUS) |

### Creative 🎨

| Project | Description | Creator | Links |
|---------|-------------|---------|-------|
| MoneyPrinterTurbo | Simply provide a topic or keyword for a video, and it will automatically generate the video copy, video materials, video subtitles, and video background music before synthesizing a high-definition short video. Integrates Pollinations' text generation service to create engaging and relevant video scripts. | @harry0703 | [Website](https://github.com/harry0703/MoneyPrinterTurbo), [GitHub](https://github.com/harry0703/MoneyPrinterTurbo) - ⭐ 32.2k |
| Jackey | Jackey is a creative AI companion that helps users generate story ideas, write scripts, and create concept art using Pollinations. It's designed for writers, game developers, and filmmakers. | Creative Sparks Ltd. | [Website](https://jackey.ai/app), [GitHub](https://github.com/creativesparks/jackey) - ⭐ 0.1k |
| Polynate | A platform for creating and sharing AI-generated art, music, and stories, with a strong community focus and Pollinations integration. | @polynate_team | [Website](https://polynate.com), [GitHub](https://github.com/polynate/platform) - ⭐ 0.1k |
| Memed | An AI-powered meme generator that creates humorous images based on user prompts using Pollinations API for image generation. | @k_singh | [Website](https://memed.io), [GitHub](https://github.com/ksingh/memed) - ⭐ 0.0k |
| Elixpo-Art | A digital art platform that combines AI image generation with traditional digital art tools, offering creative filters and style transfers powered by Pollinations. | @elixpo | [Website](https://elixpo-art.com), [GitHub](https://github.com/elixpo/art-platform) - ⭐ 0.0k |
| Elixpo Art | A Web interface to create thematic images from prompts, with multiple aspect ratios and also image reference inputs. | Ayushman Bhattacharya | [Website](https://elixpoart.vercel.app), [GitHub](https://github.com/Circuit-Overtime/elixpo_ai_chapter) - ⭐ 0.0k |
| Pollinations Feed | A social media inspired application for browsing, creating, and sharing AI-generated images using the Pollinations API. | @yujincs | [Website](https://pollinations-feed.vercel.app/), [GitHub](https://github.com/yujincs/pollinations-feed) - ⭐ 0.0k |
| Dreamscape AI | Dreamscape AI is a creative studio for generating, enhancing, and transforming images, plus conversational AI capabilities with text and voice interfaces, and a deep research tool. The entire site is almost all powered by Pollinations API aside from the image enhancement tools. It generates images, optimizes prompts and creates image titles with the text API, features lots of image styling prompts, also has chat and voice chat with chat memory, and a research tool. | @sizzlebop | [Website](https://dreamscape.pinkpixel.dev), [GitHub](https://github.com/pinkpixel-dev/dreamscape-ai) - ⭐ 0.0k |
| MASala | Multi-Agent AI That Cooks Up Recipes Just for You ~ From fridge to feast, MASALA plans it all. | @Naman009 | [Website](https://github.com/Naman009/MASala), [GitHub](https://github.com/Naman009/MASala) - ⭐ 0.0k |
| Avatar GenStudio | A system for creating custom characters that uses the Pollinations API for totally free and unlimited image generation. | @nic-wq | [Website](https://astudio-dcae4.web.app) |
| CalcuBite AI | CalcuBite AI is a smart tool that analyzes food from images to provide calorie and nutrient details. Just take a photo, and it quickly gives you an estimate of your meal's nutritional value. It uses AI for accurate analysis, and if you run out of free scans, you can watch an ad to get more! | @sugamdeol | [Website](https://calcubite.vercel.app/) |
| 🇮🇩 Generator AI Image 🇮🇩 | Advanced AI Image Generator adalah platform inovatif yang memungkinkan Anda membuat gambar digital menakjubkan dengan kecerdasan buatan by pollinations.ai. Dengan dukungan berbagai model AI canggih seperti DALL·E 3, Stable Diffusion, dan Flux-Default. (An innovative platform that allows you to create amazing digital images with artificial intelligence powered by pollinations.ai. Supports various advanced AI models like DALL-E 3, Stable Diffusion, and Flux-Default.) | @kenthirai | [Website](https://kenthir.my.id/advanced-generator/) |
| BlackWave | An AI image generator that creates unique images from text prompts. Fast, easy and free! | @metimol | [Website](https://blackwave.studio/) |
| NailsGen | Create beautiful nail art designs with AI. Generate unique nail art designs with different styles and colors. | <EMAIL> | [Website](https://www.nailsgen.com/) |
| ImageGen AI Image | Generate high-quality AI images for any purpose. Features a variety of models and styles. | https://www.linkedin.com/in/narendradwivedi | [Website](https://imagegenaiimage.com/) |
| 🇮🇩 RuangRiung AI Image 🇮🇩 | RuangRiung AI Image Generator is ideal for digital artists, designers, or anyone who wants to explore creativity with AI assistance. Available in English and Indonesian, this website combines complete functionality with an elegant and responsive design. | @ruangriung | [Website](https://ruangriung.my.id), [GitHub](https://github.com/ruangriung) |
| PollinateAI | PollinateAI is an image generation platform that aims to ease the stress of graphic and visual designers in delivering inspirations for their work. Regular consumers are also welcomed. | @Auspicious14 | [Website](https://pollinateai.vercel.app), [GitHub](https://github.com/Auspicious14/image-generator-fe.git) - ⭐ 0 |
| WebGeniusAI | AI tool that generates HTML websites with visuals from Pollinations. | @logise | [Website](https://webgeniusai.netlify.app/) |
| FlowGPT | Generate images on-demand with ChatGPT! | - | [Website](https://flowgpt.com/p/instant-image-generation-with-chatgpt-and-pollinationsai) |
| Snapgen.io | A free AI image generation website with a clean and professional interface, offering high-quality image generation without requiring API keys. | <EMAIL> | [Website](https://snapgen.io) |
| Image Gen - Uncensored Edition | A powerful image generation assistant on HuggingChat. | @DeFactOfficial | [Website](https://huggingface.co/chat/assistant/66fccce0c0fafc94ab557ef2) |
| Pollinations AI Video Generator | An open-source video generation system using AI. | @videogen | [Website](https://pollinations-ai-video-generator.vercel.app/) |
| Pollinations AI Image Generator | An AI-powered image generation platform for Android designed to create stunning visuals from text prompts. Features dynamic image generation as users scroll, save to gallery, favorites, and a user-friendly interface. | @imagegen | [Website](https://pollinations-ai-image-generator.vercel.app/) |
| Foodie AI | An AI application for food analysis that uses advanced artificial intelligence technology to help users understand food ingredients, nutritional value, and health impacts. Provides food safety analysis, nutritional health assessment, sports and fitness analysis, visual display, alternative recommendations, and practical insights for different dietary habits. | @Aashir__Shaikh | [Website](https://foodie-ai.vercel.app/) |
| AIMinistries | A collection of free AI tools including AI chat, writing tools, image generation, image analysis, text-to-speech, and speech-to-text. | @tolerantone | [Website](https://www.ai-ministries.com) |
| Match-cut video ai | This AI generates video from text in match-cut text style, uses pollinations llm to generate nearby text, and supports API integration. | @r3ap3redit | [Website](https://video-gen.megavault.in), [GitHub](https://github.com/iotserver24/match-cut-ai) - ⭐ 0 |
| The Promised Pen | A free, feature-rich novel writing application that helps writers organize stories, characters, and worlds. Uses Pollinations AI for generating chapter summaries, rewriting text based on context, and generating new content based on previous chapters and character information. | @soryn.san | [Website](https://promisedpen.app) |
| Polynate | AI-powered text and audio content generation platform providing a user-friendly interface for interacting with various AI generation services from Pollinations.ai. | @fisven | [Website](https://polynate.cloudwerx.dev/), [GitHub](https://github.com/fisventurous/pollinationsai-enhancer) - ⭐ 0 |
| 🇨🇳 Aiphoto智能绘画 🇨🇳 | AI艺术工坊 - 智能绘画生成器。这是一个基于AI的绘画生成工具，可以根据用户输入的中文描述自动生成相应的图片。(An AI art workshop - intelligent painting generator. This is an AI-based painting generation tool that can automatically generate images based on Chinese descriptions input by users.) | @qiyimg | [Website](https://qiyimg.3d.tc/Aiphoto) |
| 🖥️ AI YouTube Shorts Generator | Python desktop app that automates YouTube Shorts creation with AI-generated scripts, voiceovers (via ElevenLabs), and visuals using Pollinations API. Designed for content creators, educators, and marketers to produce high-quality short videos quickly without manual editing. | @Sami-Alsahabany |  |
| FoldaScan | Use Natural Language to "Converse" with Your Codebase, Folda-Scan Smart Project Q&A, powered by advanced vectorization technology, allows you to easily understand complex code, pinpoint information, and offers unprecedented convenience for AI collaboration. | @0010skn | [Website](https://fs.wen.bar), [GitHub](https://github.com/0010skn/WebFS-Toolkit-Local-Folder-Scan-Monitor-Versioning-AI-Prep) |
| 🤖 Emojiall AI Drawing Platform | A platform focused on allowing users to draw pictures according to their own requirements with many preset styles and themes. Part of Emojiall, which has other text-based AI features like Emoji translation to text, Emoji recommender, and Emoji chatbot. | @James-Qi | [Website](https://art.emojiall.com) |
| 🆕  PixPal | PixPal is a free AI assistant that can analyze, edit, and generate images, build websites from screenshots, create 3D games, and write full blog posts—all in one chat. Upload a photo, describe an idea, or request a UI clone and PixPal instantly delivers creative results. | @andreas_11 | [Website](https://pixpal.chat) |
| 🆕 🇪🇸 🇪🇸 Generador de presentaciones con imágenes y texto V2 | Una herramienta configurable que permite crear presentaciones con 3 a 20 diapositivas usando la API de Pollinations. Genera títulos, descripciones e imágenes para cada diapositiva, con posibilidad de regenerar imágenes y descargar en HTML. (A configurable tool that allows you to create presentations with 3 to 20 slides using the Pollinations API. Generates titles, descriptions and images for each slide, with the ability to regenerate images and download in HTML.) | @juanrivera126 | [Website](https://proyectodescartes.org/IATools/Crea_presentaciones4/) |
| 🆕 🇪🇸 Yo el director | Web para crear peliculas y contenido para youtube, usando Pollinations (Web platform for creating movies and YouTube content using Pollinations) | @henryecamposs | [Website](https://yoeldirector.dpana.com.ve) |
| Imagemate AI | Imagemate AI is a powerful image generation app designed to turn your imagination into stunning visuals with the help of advanced artificial intelligence. Built using the Pollinations AI API, Imagemate AI allows users to input a text prompt and instantly receive AI-generated images that match the description. | @Shanto-Islam | [Website](https://play.google.com/store/apps/details?id=com.madameweb.imgmate) |
| B&W SVG Generator | Uses Flux (through pollinations) and potrace to create B&W Vector files | @pointsguy118 | [Website](https://fluxsvggenerator.streamlit.app/) |
| Anime AI Generation | A web app specializing in anime-style image generation using Pollinations AI, with options for different anime art styles and character designs. | @FromOtherUniverse | [Website](https://anime-ai-generation.vercel.app/) |
| MIDIjourney | An AI music generation tool that creates MIDI compositions from text prompts using Pollinations, suitable for musicians and producers. | @midi_maestro | [Website](https://midijourney.ai) |
| TurboReel | A fast AI video generation service for social media content, leveraging Pollinations to create short, impactful videos from simple descriptions. | @turbo_reels | [Website](https://turboreel.app) |
| StoryWeaver | An AI-powered platform for collaborative storytelling, where users can co-create narratives with AI using Pollinations for text and image generation. | @weave_tales | [Website](https://storyweaver.ai) |
| AI PPT Maker | An AI-powered presentation generator that creates PowerPoint slides from text prompts using Pollinations. Features customizable templates, image suggestions, and content structuring to streamline presentation creation. | @ppt_monster | [Website](https://ppt.monsterstudio.org/) |
| Pollinations Gallery | A dynamic gallery showcasing AI-generated art created with Pollinations. Users can browse, search, and filter images, and submit their own creations. Features community voting and artist profiles. | @gallery_ai | [Website](https://pollinations-gallery.netlify.app/) |
| Anime Character Generator | A dedicated AI tool for generating high-quality, unique anime-style characters. Offers detailed customization of art style, character traits, clothing, and accessories, all powered by Pollinations. | @AnimeArtDevs | [Website](https://animechar.gen.ai/create), [GitHub](https://github.com/animeartdevs/character-generator) - ⭐ 0.3k |
| Imagen | A beautiful web interface for generating images using Pollinations.ai API with only the "flux" and "turbo" models. | @altkriz | [Website](https://altkriz.github.io/imagen/), [GitHub](https://github.com/altkriz/imagen) - ⭐ 0.0k |
| Imagen | A beautiful web interface for generating images using Pollinations.ai API with only the "flux" and "turbo" models. | @altkriz | [Website](https://altkriz.github.io/imagen/), [GitHub](https://github.com/altkriz/imagen) - ⭐ 0.0k |
| 🇨🇳 AI 文本转音频 🇨🇳 | 输入文本，选择语音风格，一键将文字转换为自然流畅的语音。 支持多种声音特征，帮您创建专业水准的音频内容。 (Input text, select voice style, and instantly convert text to natural, fluid speech. Supports various voice characteristics to help you create professional-quality audio content.) | https://github.com/Azad-sl | [Website](https://tts-gules-theta.vercel.app/), [GitHub](https://github.com/Azad-sl/tts) - ⭐ 0.0k |
| Musify - AI Enhanced Music Streaming | Musify is your AI-powered music buddy, making your jam sessions smarter and more fun. Powered by pollinations API, it offers AI Music Assistant, Voice Commands, AI Playlist Creator, and Responsive Design. | @Sugamdeol | [Website](https://musify-sd.vercel.app/) |
| 🇧🇷 Case Me 🇧🇷 | A vending machine that creates customized phone cases with photos or other images and colors chosen by the end customer. | <EMAIL> |  |
| 🇮🇩 Generator Text AI 🇮🇩 | Text-to-image generator using Pollinations, supporting Indonesian and English prompts. | @ayick13 | [Website](https://app.ariftirtana.my.id/), [GitHub](https://github.com/ayick13/app) - ⭐ 0 |
| 🤖 Zelos AI image generator | It uses Pollinations for both prompt enhancing and image generation, it was a easy to make project due to pollinations services being easy to use. | https://www.roblox.com/users/4361935306/profile | [Website](https://websim.ai/@ISWEARIAMNOTADDICTEDTOPILLOW/ai-image-prompt-generator) |
| Pollinator Android App | An open-source Android app for text-to-image generation. | @gaurav_87680 | [Website](https://github.com/g-aggarwal/Pollinator) |
| 🤖 Zelos AI image generator | It uses Pollinations for both prompt enhancing and image generation, it was a easy to make project due to pollinations services being easy to use. | https://www.roblox.com/users/4361935306/profile | [Website](https://websim.ai/@ISWEARIAMNOTADDICTEDTOPILLOW/ai-image-prompt-generator) |
| AI YouTube Shorts Generator | A tool that uses Pollinations AI to generate engaging YouTube Shorts videos from text prompts, including visuals and voiceovers. | @shorts_creator | [Website](https://aishorts.vercel.app/) |
| Aiphoto智能绘画 | A Chinese language AI image generation platform using Pollinations, tailored for creating artistic and culturally relevant visuals. | @jolav_cn | [Website](https://aiphoto.jolav.me/) |
| Rangrez AI | An AI image generation tool specializing in traditional Indian art styles, using Pollinations to create culturally rich visuals. | @rangrez_art | [Website](https://rangrez.ai) |
| StorySight | An AI tool that generates illustrations for stories and articles using Pollinations, helping writers visualize their narratives. | @story_viz | [Website](https://storysight.ai) |
| 🆕 Generative AI Images Gallery | A curated gallery of AI-generated images created with Pollinations, showcasing diverse styles and themes from the community. | @gallery_curator | [Website](https://gen-ai-gallery.netlify.app/) |
| 🆕 TeekGenAI | A platform providing free access to AI tools like image generation, text-to-speech, and chat, with tutorials. Content often available in Sinhala. | @teekgenai | [Website](https://www.youtube.com/@teekgenai) |
| IRINA by visuallink | A website offering easy and free access to various neural networks, with multi-language support planned. Provides a platform for accessing various AI models, including Pollinations. | visuallink | [Website](https://irina.visuallink.ru/) |
| AI-Bloom | A platform for generating and exploring AI-created floral art and patterns. Users can customize flower types, colors, and arrangements using Pollinations, and share their digital bouquets. | @bloom_artist | [Website](https://ai-bloom.com/) |
| Pollinations.ai Image Generation (for Frame) | A Flutter application that listens for image generation prompts, requests images from Pollinations.AI, and displays them on the Frame wearable device. Users can use voice commands to generate images and save/share them using the device's sharing mechanism. | CitizenOneX | [Website](https://github.com/CitizenOneX/frame_pollinations), [GitHub](https://github.com/CitizenOneX/frame_pollinations) - ⭐ 0.0k |
| Own-AI | An AI text-to-image generator. | Sujal Goswami | [Website](https://own-ai.pages.dev/) |
| Elixpo Art Chrome Extension | It uses the pollinations image endpoint to generate an image with `boltning` as the model in 4 types of aspect ratios and themes with prompt engineering thus transforming selected texts into art smoothly with a disposable GUI in web. | Ayushman Bhatacharya | [Website](https://chromewebstore.google.com/detail/elixpo-art-select-text-an/hcjdeknbbbllfllddkbacfgehddpnhdh), [GitHub](https://github.com/Circuit-Overtime/elixpo_ai_chapter/tree/main/Elixpo%20Chrome%20%20Extension) - ⭐ 0.0k |
| Pollinations.ai Model Comparison | An interactive tool designed to compare outputs from various large language models with customizable timeout settings and real-time testing capabilities. | https://github.com/dseeker | [Website](https://endemicmedia.github.io/FLARE/llm-comparison-tool/), [GitHub](https://github.com/EndemicMedia) |
| 🇨🇳 IMyself AI 🇨🇳 | 我们提供高质量的AI生成服务，包括图像生成、文本生成、音频生成和语音转文本服务， 让您轻松创建各种创意内容。 (We provide high-quality AI generation services, including image generation, text generation, audio generation, and speech to text services, allowing you to easily create various creative content.) | Shadownc | [Website](https://openai.lmyself.top/) |

### Games 🎲

| Project | Description | Creator | Links |
|---------|-------------|---------|-------|
| 🆕 🤖 🆕 🤖 Mindcraft | Crafting minds for Minecraft with LLMs and Mineflayer! An AI agent framework for Minecraft using Large Language Models that creates intelligent bots capable of autonomous gameplay, building, and interaction. | @kolbytn | [Website](https://github.com/kolbytn/mindcraft), [GitHub](https://github.com/kolbytn/mindcraft) - ⭐ 3.5k |
| Gacha | An AI-powered gacha game where players collect unique characters and items generated by Pollinations. Features procedurally generated art and storylines. | GachaVerse Studios | [Website](https://gacha-art.example.com), [GitHub](https://github.com/gachaverse/gacha-game) - ⭐ 0.3k |
| Minecraft AI (Python) | A Python implementation for controlling Minecraft characters with Pollinations AI, featuring advanced pathfinding and building capabilities. | @py_minecraft_dev | [Website](https://github.com/pollinations/minecraft-ai-python), [GitHub](https://github.com/pollinations/minecraft-ai-python) - ⭐ 0.2k |
| Minecraft AI (Node.js) | A Node.js implementation that uses Pollinations AI to control a Minecraft character through natural language commands. | @minecraft_ai_dev | [Website](https://github.com/pollinations/minecraft-ai-node), [GitHub](https://github.com/pollinations/minecraft-ai-node) - ⭐ 0.1k |
| 🆕 DreamHer | Interactive web app that transforms your imagination of a 'dream girl' into a visual representation through just 10 simple questions using Pollinations AI. Features AI-powered visualization, dynamic processing, and an engaging, magical user experience. | @_Creation22 | [Website](https://dreamher.vercel.app/), [GitHub](https://github.com/creation22/DreamGirl) - ⭐ 0.0k |
| RoastMaster AI | No detailed description available, but likely a creative/entertainment tool (AI roast generator). | - |  |
| Pollinations AI Game | A Hitchhiker's Guide to the Galaxy themed LLM-based elevator game. | @game | [Website](https://pollinations-ai-game.vercel.app/) |
| Favorite Puzzles | A jigsaw puzzles game for Android, iOS, and web that uses Pollinations feed as one of the sources of images for puzzles. Features puzzle generation using neural networks, customizable difficulty levels from 6 to 1200 pieces, multiple game modes, and the ability to create puzzles from your own images. | <EMAIL> | [Website](https://radbrothers.com/games/favorite-puzzles/) |
| Deep Saga | An immersive role-playing game with AI-generated worlds, characters, and quests that adapt to player choices using Pollinations AI. | @saga_studios | [Website](https://deepsaga.io) |
| Infinite World – AI Game | An exploration game with procedurally generated environments and creatures created by Pollinations AI based on player input. | @infinite_world_dev | [Website](https://infinite-world-game.vercel.app/) |
| Sirius Cybernetics Elevator Challenge | A programming challenge that uses Pollinations AI to simulate personality-driven elevator systems in a virtual building environment. | @sirius_dev | [Website](https://github.com/sirius-cybernetics/elevator-challenge), [GitHub](https://github.com/sirius-cybernetics/elevator-challenge) - ⭐ 0.0k |
| roastmyselfie.app | AI Personality Analyzer - Get roasted and psychoanalyzed.. just from one selfie! Dare to try? | @andres_11 | [Website](https://roastmyselfie.app) |
| Watch TV with neko (Roblox) | Roblox game where you can talk with AI catgirls 🐾 or just have fun, talking with other players in cozy rooms ⭐️ | https://www.roblox.com/users/3857849039/profile/ | [Website](https://www.roblox.com/games/15087497266/UPD-Watch-TV-with-neko-AI) |
| Mindcraft | A web-based Minecraft-inspired game where players can use natural language to build and interact with a voxel world using Pollinations AI. | @mindcraft_team | [Website](https://mindcraft-ai.vercel.app/) |
| Juego de Memorizar con Pollinations | A memory game that uses Pollinations AI to generate unique image pairs for matching, with difficulty levels and educational themes. | @edudev_es | [Website](https://memorizar-pollinations.vercel.app/) |
| Favorite Puzzles | A collection of AI-generated puzzles including jigsaws, crosswords, and logic games, all created using Pollinations APIs. | @puzzle_master | [Website](https://favorite-puzzles.netlify.app/) |
| Abyss Ascending | A text-based adventure game with procedurally generated dungeons and storylines powered by Pollinations AI. | @abyss_game_studio | [Website](https://abyss-ascending.vercel.app/) |
| AI Character RP (Roblox) | A Roblox game that lets players interact with AI characters powered by Pollinations, featuring dynamic conversations and quests. | @roblox_ai_dev | [Website](https://www.roblox.com/games/ai-character-rp) |
| Watch TV with Neko (Roblox) | A Roblox experience where players can watch AI-generated TV shows with a virtual cat companion, using Pollinations for content generation. | @neko_tv_dev | [Website](https://www.roblox.com/games/watch-tv-with-neko) |
| Infinite Tales | Interactive storytelling platform powered by AI that creates endless narrative adventures. | @infinite_tales | [Website](https://infinitetales.ai) |

### Hack-&-Build 🛠️

| Project | Description | Creator | Links |
|---------|-------------|---------|-------|
| tgpt | ChatGPT in terminal without requiring API keys. Uses Pollinations API endpoints to provide a free AI experience through the command line. | @aandrew-me | [Website](https://github.com/aandrew-me/tgpt), [GitHub](https://github.com/aandrew-me/tgpt) - ⭐ 2.5k |
| 🆕 🛠️ AI Content Describer | An extension for NVDA, the free and open-source screen reader for Microsoft Windows. Uses multimodal generative AI to help those with blindness and visual impairments understand pictures, UI controls, complex diagrams/graphics, and more through intelligent descriptions that go far beyond simple alt-text. | @cartertemm | [Website](https://github.com/cartertemm/AI-content-describer/), [GitHub](https://github.com/cartertemm/AI-content-describer/) - ⭐ 0.1k |
| MVKProject Nexus API | An API gateway that provides simplified access to multiple AI services including Pollinations, offering standardized endpoints and request formats for developers. | @mvk | [Website](https://nexus-api.mvkproject.dev/), [GitHub](https://github.com/mvkproject/nexus-api) - ⭐ 0.0k |
| Server Status Dashboards | A monitoring tool for tracking and visualizing server performance metrics, using Pollinations API for natural language interpretation of technical data. | @devopper | [Website](https://server-status-dashboards.netlify.app/), [GitHub](https://github.com/devopper/server-status-dashboards) - ⭐ 0.0k |
| 🆕 💻️ Windows Walker | Windows Walker – What Copilot for Windows should have been. AI-powered Windows assistant that translates voice/text commands into real system actions using PowerShell. Powered by ChatGPT + PowerShell in an Electron UI. | @supershivam | [Website](https://github.com/SuperShivam5000/windows-walker), [GitHub](https://github.com/SuperShivam5000/windows-walker) - ⭐ 0.0k |
| 🌱 Strain Navigator | A collection of tools to help Growers, Breeders & Seed Bankers. Free & Open Source powered by Pollinations.ai. | @Tolerable | [Website](https://www.strainnavigator.com/), [GitHub](https://github.com/Tolerable/strainnavigator) - ⭐ 0.0k |
| CoNavic | A free, open-source browser extension that brings the power of ChatGPT and browser automation directly to your fingertips. Instantly access AI assistance, manage tabs, and organize bookmarks using natural language all securely within your browser. | @mkantwala | [Website](https://github.com/mkantwala/CoNavic/), [GitHub](https://github.com/mkantwala/CoNavic/) - ⭐ 0.0k |
| FoodAnaly | An AI application for food analysis that uses advanced artificial intelligence technology to help users understand food ingredients, nutritional value, and health impacts. Provides food safety analysis, nutritional health assessment, sports and fitness analysis, visual display, alternative recommendations, and practical insights for different dietary habits. | <EMAIL> | [Website](https://foodanaly.vercel.app/) |
| Mimir AIP | An AI integration platform for developers. | @CiaranMcAleer | [Website](https://mimir-aip.github.io/), [Github](https://github.com/Mimir-AIP/Mimir-AIP) - ⭐ 0.0k |
| Herramientas IA | Tools designed with Pollinations.AI and the DescartesJS editor, including tools from other Pollinations.AI community members. | @herramientas | [Website](https://herramientas.ia) |
| Pollinations AI Free API | This project provides a free API interface supporting various text and image generation models, including OpenAI's GPT-4, Gemini 2.0, etc. Users can access these models without an API key to perform text generation, image generation, translation, text polishing, and more. | @freeapi | [Website](https://pollinations-ai-free-api.vercel.app/) |
| Pollinations.AI Enhancer | A frontend-based AI interface designed to deliver a smooth, multimodal, and visually engaging user experience with conversational AI, image generation, and more. | @fisven | [Website](https://github.com/fisventurous/pollinationsai-enhancer), [GitHub](https://github.com/fisventurous/pollinationsai-enhancer) - ⭐ 0 |
| 🆕 DominiSigns | A WordPress block plugin that lets users create AI-generated images through the block editor. Integrates with Pollinations API to generate images from text prompts directly within WordPress. | @dominicva | [Website](https://github.com/dominicva/dominisigns), [GitHub](https://github.com/dominicva/dominisigns) |
| urSapere AI | An AI-powered research tool that helps users explore and understand complex topics by leveraging Pollinations for advanced data analysis and summarization. | @ursapere_devs | [Website](https://ursapere.ai), [GitHub](https://github.com/ursapere/ursapere-ai) - ⭐ 0.0k |
| WordPress AI Vision Block | A custom WordPress Gutenberg block that allows you to generate images using the Pollinations API. Simply enter a prompt, and the AI will generate an image for you. Once the post is saved, the image is automatically stored in the WordPress Media Library. | mahmood-asadi | [Website](https://wordpress.org/plugins/ai-vision-block/), [GitHub](https://github.com/mahmood-asadi/ai-vision-block) - ⭐ 0.0k |
| DominiSigns | Avatar Translator for Dominican Sign Language that uses artificial intelligence to translate text and audio into Dominican sign language (LSRD), creating a communication bridge for approximately 100,000 deaf people in the Dominican Republic. | @cmunozdev | [GitHub](https://github.com/cmunozdev/DominiSigns) - ⭐ 0.0k |
| GPT_Project | A cutting-edge platform leveraging GPT models via Pollinations for advanced text analysis, content generation, and interactive chat solutions. Showcases multiple proof-of-concept applications. | AI Future Labs | [Website](https://real-gpt-project.dev), [GitHub](https://github.com/aifuturelabs/gpt-project) - ⭐ 0.1k |
| Pollinations MCP Server | A Model Context Protocol server that enables AI-assisted development through natural language interaction with Pollinations' multimodal services. | @thomash | [Website](https://github.com/pollinations/model-context-protocol), [GitHub](https://github.com/pollinations/model-context-protocol) - ⭐ 0.0k |
| MCPollinations | A Model Context Protocol (MCP) server that enables AI assistants to generate images, text, and audio through the Pollinations APIs. Supports customizable parameters, image saving, and multiple model options. | Pink Pixel | [Website](https://github.com/pinkpixel-dev/MCPollinations) |
| pollinations_ai | Dart/Flutter package for Pollinations API. | @Meenapintu | [Website](https://pub.dev/packages/pollinations_ai) |
| Smpldev | Create, deploy, and scale full-stack web and mobile applications in minutes. | @kennet678 | [Website](https://smpldev.ftp.sh/) |
| pollinations NPM Module | JavaScript/Node.js SDK for Pollinations API. | - |  |
| pypollinations | Comprehensive Python wrapper for Pollinations AI API. | @KTS-o7 | [Website](https://pypi.org/project/pypollinations/) |
| @pollinations/react | React hooks for easy integration of Pollinations' features. | @pollinations | [Website](https://www.npmjs.com/package/@pollinations/react) |
| Polli API Dashboard | Dashboard for managing/interacting with Pollinations API. | - |  |
| pollinations.ai Python SDK | Official Python SDK for working with Pollinations' models. | @pollinations-ai | [Website](https://github.com/pollinations-ai/pollinations.ai) |
| Herramientas IA | Tools designed with Pollinations.AI and the DescartesJS editor, including tools from other Pollinations.AI community members. | @juanrivera126 | [Website](https://proyectodescartes.org/descartescms/herramientas-ia) |
| 🇨🇳 imggen.top 🇨🇳 | Create stunning AI-generated images in seconds with our free AI image generator. No login required, unlimited generations, powered by FLUX model. | <EMAIL> | [Website](https://www.imggen.top/) |
| 🇨🇳 Quicker Pollinations AI | This project provides a free API interface supporting various text and image generation models, including OpenAI's GPT-4, Gemini 2.0, etc. Users can access these models without an API key to perform text generation, image generation, translation, text polishing, and more. | https://linux.do/u/s_s/summary | [Website](https://getquicker.net/Sharedaction?code=9ac738ed-a4b2-4ded-933c-08dd5f710a8b&fromMyShare=true) |

### Chat 💬

| Project | Description | Creator | Links |
|---------|-------------|---------|-------|
| KoboldAI Lite | A lightweight AI framework for text generation and chat. | @lostruins | [Website](https://koboldai.net), [GitHub](https://github.com/LostRuins/lite.koboldai.net) - ⭐ 3.7k |
| gpt4free | The official gpt4free repository - various collection of powerful language models. | xtekky | [Website](https://github.com/xtekky/gpt4free), [GitHub](https://github.com/xtekky/gpt4free) - ⭐ 64.1k |
| SillyTavern | An LLM frontend for power users. Pollinations permits it to generate text and images. | - | [Website](https://docs.sillytavern.app/), [GitHub](https://github.com/SillyTavern/SillyTavern) - ⭐ 14.7k |
| LobeChat | An open-source, modern-design ChatGPT/LLMs UI/Framework with speech-synthesis, multi-modal, and extensible plugin system. | @arvinxx | [Website](https://github.com/lobehub/lobe-chat), [GitHub](https://github.com/lobehub/lobe-chat) - ⭐ 12.0k |
| 🤖 AdvanceChatGptBot | An advanced chatbot leveraging multiple GPT models via Pollinations for sophisticated dialogue management, context retention, and multimodal interactions. Includes features like personality selection and knowledge base integration. | ChatBotMasters | [Website](https://adv-chatgpt.example.com), [GitHub](https://github.com/chatbotmasters/adv-chatgpt) - ⭐ 0.2k |
| Irina | An intelligent conversational assistant that combines Pollinations' text and image generation capabilities to create rich, multimedia responses to user queries. | @visuallink | [Website](https://irina.visuallink.io/), [GitHub](https://github.com/visuallinkio/irina) - ⭐ 0.0k |
| DynaSpark AI | A versatile AI assistant with advanced image and text generation capabilities. | Th3-C0der | [Website](https://dynaspark.onrender.com), [GitHub](https://github.com/Th3-C0der) - ⭐ 0.0k |
| toai.chat | A personalized, multimodal AI chat assistant that leverages Pollinations APIs to converse with models like Claude, provide image generation, and answer questions about various topics. | @arjunbazinga | [Website](https://toai.chat/), [GitHub](https://github.com/arjunb023/chatbot) - ⭐ 0.0k |
| 🆕 LLM7.io | A free and open AI platform providing advanced multimodal capabilities, including large language model access and experimental search tools. Integrates Pollinations text generation as a backend service with transparent credit on the website and repository. | @chigwell | [Website](https://llm7.io), [GitHub](https://github.com/chigwell/llm7.io) - ⭐ 0.0k |
| 🆕 🎤 Comeback AI | AI-powered clapback machine that transforms mean comments into witty comebacks with 10 unique personas, uses Pollinations openai-audio for voice synthesis, and Whisper for speech-to-text transcription. Turn trolls into comedy gold! | @sizzlebop | [Website](https://comeback-ai.pinkpixel.dev), [GitHub](https://github.com/pinkpixel-dev/comeback-ai) - ⭐ 0.0k |
| Unity AI Lab | A specialized uncensored LLM model built on Mistral Large, focused on unrestricted conversations. | - | [Website](https://blog.unityailab.com/unity.html) |
| Neurix 🇷🇺 | A website offering easy and free access to various neural networks, with multi-language support planned. Provides a platform for accessing various AI models, including Pollinations. | @Igroshka | [Website](https://neurix.ru) |
| 🆕 Echo AI | A chat interface for AI interactions and conversations. | Unknown |  |
| DreamBig - Generative AI Playground | Interactive AI playground with chat, image generation, and voice responses for creative exploration. | @opzzxsprinta._999 | [Website](https://dreambiglabs.vercel.app/) |
| Pal Chat | An iOS app that integrates with all LLMs including Pollinations AI models in one unified simple interface. | https://x.com/pallavmac | [Website](https://apps.apple.com/us/app/pal-chat-ai-chat-client/id6447545085?platform=iphone) |
| 🤖 Pollinations AI Chatbot | A chat bot integrating Pollinations API for text and image generation. | @chatbot | [Website](https://pollinations-ai-chatbot.vercel.app/) |
| Pollinations AI Playground | An AI application platform based on Pollinations.AI API, providing free and unlimited AI chat assistant, image generation, and voice synthesis services. | @playground | [Website](https://pollinations-ai-playground.vercel.app/) |
| 🤖 Snarky Bot | A snarky bot based on Llama that is 100% free, powered by the Pollinations text API and OpenWebUI. Other models are available as well. | @snarkybot | [Website](https://snarkybot.vercel.app/) |
| OkeyMeta | An LLM created by Africans to understand and have cultural awareness of African contexts and languages, OkeyAI outperforms many LLM models based on size and intelligence, OkeyMeta uses pollination image generating API to train it's LLM (OkeyAI) on images in real time. | @okeymeta | [Website](https://okeymeta.com) |
| 🤖 Pollinations Chatbot | A chat bot integrating Pollinations API for text and image generation. | @Aashir__Shaikh | [Website](https://pollinations-chatbot.vercel.app/) |
| 🇨🇳 Pollinations.AI 中文 | 我们提供高质量的AI生成服务，包括图像生成、文本生成、音频生成和语音转文本服务， 让您轻松创建各种创意内容。 (We provide high-quality AI generation services, including image generation, text generation, audio generation, and speech to text services, allowing you to easily create various creative content.) | @pollinations | [Website](https://pollinations.vercel.app) |
| Anisurge | A free anime streaming app with a public chat feature that allows users to chat with AI characters powered by Pollinations AI. | @iotserver24 | [Website](https://anisurge.me) |
| 🖥️ AI Chat | A Windows desktop application that brings multiple AI models together in one simple, intuitive interface. Features saving/loading conversations, image generation, image explanation from URLs, and voice responses with different voices. | @narendradwivedi | [Website](https://aichat.narendradwivedi.org) |
| Irina | Lightweight and simple online chat interface powered by pollinations | @thatalgp | [Website](https://irina-2--trivonca.on.websim.ai/) |
| 🤖 Free AI Chatbot & Image Generator | A web application offering both conversation with AI and image generation capabilities, utilizing Pollinations API for creating visuals based on text prompts. | @aidevs | [Website](https://free-ai-chatbot-image-generator.vercel.app/) |
| 🇨🇳 FreeAI 🇨🇳 | An AI application platform based on Pollinations.AI API, providing free and unlimited AI chat assistant, image generation, and voice synthesis services. | @Azad-sl | [Website](https://freeai.aihub.ren/), [GitHub](https://github.com/Azad-sl/FreeAI) - ⭐ 0.0k |
| 🤖 PixPax | A user-friendly chatbot that lets you analyze images, remix existing images or create new images, all through simple chat. | @andreas_11 | [Website](https://pixpal.chat) |
| 🤖 🤖 DesmondBot | A snarky bot based on Llama that is 100% free, powered by the Pollinations text API and OpenWebUI. Other models are available as well. | @mcgdj | [Website](https://swedish-innocent-teeth-majority.trycloudflare.com) |
| 🤖 Mirexa AI Chat | A state-of-the-art AI chatbot that seamlessly integrates multiple LLMs with advanced multimodal capabilities. Features comprehensive text generation, sophisticated image creation and image-to-image transformation, audio generation, mathematical problem solving, and real-time web search functionality. | @withthatway | [Website](https://mirexa.vercel.app) |
| AI Chat | A simple and elegant chat interface for interacting with various AI models through Pollinations, focusing on ease of use and quick responses. | @jolav | [Website](https://aichat.jolav.me/) |
| KoboldAI Lite | A lightweight version of KoboldAI that uses Pollinations for text generation, offering a streamlined experience for creative writing and storytelling. | @kobold_dev | [Website](https://koboldai-lite.vercel.app/) |
| LobeChat | An open-source, extensible chat UI framework supporting multiple models and features like message citing and image creation. | @lobehub | [Website](https://chat-lobe.com), [GitHub](https://github.com/lobehub/lobe-chat) - ⭐ 21.0k |
| 🤖 🇮🇩 Rizqi O Chatbot 🇮🇩 | Rizqi O Chatbot adalah proyek berbasis Pollinations yang menggabungkan tiga fitur utama: chatbot AI, generator gambar AI, dan generator audio AI. Pengguna dapat berinteraksi dalam bentuk teks, menghasilkan gambar dengan berbagai gaya seni dan efek visual, serta membuat audio secara otomatis dari teks. (An AI chatbot, image generator, and audio generator project with support for custom aspect ratios, over 200 art styles & visual effects, and automatic translation from Indonesian to English.) | @ray23-bit | [Website](https://chatbot.rizqioliveira.my.id), [GitHub](https://github.com/ray23-bit/Projectenam) - ⭐ 0.0k |
| 🤖 Jenny AI | AI chatbot and character creation platform with tts and sst it also has image generation and vision ability which are powered by pollinations. | https://www.linkedin.com/in/pritam-roy-95185328a |  |
| Goalani | Voice-enabled AI fitness coach. Using only your voice, you can communicate with the agent to manage your fitness and nutrition. Features weight tracking, exercise logging, food tracking with AI-generated images, and agent customization. | <EMAIL> | [Website](https://goalani.com) |
| Pollinations Chat | Pollinations' integrated AI for text and images, totally free and unlimited. | @adrianoprogramer | [Website](https://websim.ai/@AdrianoDev1/pollinations-ai-assistant/4) |
| LiteAI | A free, fast, and anonymous AI chat and image generation platform with no login required. Features include various AI models, prompt library, upscaling, and community sharing. | LiteAI Team | [Website](https://liteai.chat/) |
| 🤖 UR Imagine & Chat AI | A versatile AI platform offering both image generation and chat functionalities. Users can create visuals from text prompts and engage in conversations with AI models, all powered by Pollinations. | @ur_imagine | [Website](https://urimagine.netlify.app/) |
| PrivatePollenAI | A privacy-focused chat assistant app that securely stores data locally, integrates with PollinationAI for text and image generation, features a minimalistic UI, and allows users to choose models and write their own system instructions. | https://github.com/MMojoCoder | [Website](https://mmojocoder.github.io/PrivatePollenAI/chat.html), [GitHub](https://github.com/MMojoCoder/PrivatePollenAI) |

### Social Bots 🤖

| Project | Description | Creator | Links |
|---------|-------------|---------|-------|
| 🤖 AlphaLLM - AI Discord Bot | A feature-rich Discord bot that combines multiple AI models from Pollinations to provide text generation, image creation, and voice synthesis. | @alphallm_team | [Website](https://alphallm.ai/discord), [GitHub](https://github.com/alphallm/discord-bot) - ⭐ 0.2k |
| 🤖 Pollinations Discord Bot | Official Discord bot for Pollinations.ai that allows server members to generate images, text, and audio directly within Discord channels. | @pollinations | [Website](https://github.com/pollinations/discord-bot), [GitHub](https://github.com/pollinations/discord-bot) - ⭐ 0.2k |
| 🤖 OpenHive | A decentralized platform for AI bots powered by Pollinations that can be deployed across multiple social media platforms simultaneously. | @openhive_network | [Website](https://openhive.network), [GitHub](https://github.com/openhive/network) - ⭐ 0.1k |
| 🤖 Titan-GPT | A Discord bot that combines text and image generation capabilities from Pollinations to create rich, interactive experiences for server members. | @titan_dev | [Website](https://titan-gpt.vercel.app), [GitHub](https://github.com/titan-dev/titan-gpt) - ⭐ 0.1k |
| 🤖 Aura Chat Bot | A chat bot integrating Pollinations API for text and image generation. | @Py-Phoenix-PJS |  |
| 🆕 🤖 🤖 ImageEditer | AI Art Studio - A feature-rich Telegram bot that creates art from text prompts, remixes images, merges multiple artworks, and offers one-tap regeneration with real-time control. Supports multiple AI models (GPT Image, Flux, Turbo) with NSFW detection and smart layout features. | @_dr_misterio_ | [Website](https://t.me/ImageEditer_bot) |
| 🤖 One Word | A Discord bot that generates images from single-word prompts, designed for simplicity and quick creative inspiration. | @oneword_team | [Website](https://oneword.pollinations.ai) |
| 🤖 Pollinations Telegram Assistant | An advanced Telegram bot that provides access to Pollinations AI services through a conversational interface with support for multiple languages. | @pollen_labs | [Website](https://t.me/pollinations_assistant_bot) |
| 🤖 PolliBot | A versatile Telegram bot integrated with Pollinations. Offers image generation, text summarization, and quick answers directly within Telegram chats. Supports multiple languages. | BotBuilders Inc. | [Website](https://pollibot.example.com), [GitHub](https://github.com/botbuilders/pollibot-telegram) - ⭐ 0.1k |
| 🤖 Pollinations WhatsApp Group | A WhatsApp group bot that allows members to generate AI content through simple commands, making Pollinations accessible on mobile messaging. | @whatsapp_ai_dev | [Website](https://chat.whatsapp.com/pollinations-ai) |
| 🤖 pollinations-tg-bot 🇨🇳 | Chinese language Telegram bot for Pollinations AI with specialized prompts for Eastern art styles and cultural references. | @cn_ai_dev | [Website](https://t.me/pollinations_cn_bot) |
| 🤖 Quick AI & Jolbak | A multi-platform bot suite that integrates with Discord, Slack, and Microsoft Teams to provide Pollinations AI services in professional environments. | @jolbak_dev | [Website](https://quickai.jolbak.com) |
| 🆕 AI Image Generator [ROBLOX] | A Roblox experience that allows players to generate images using Pollinations AI directly within the game environment. | @roblox_ai_studio | [Website](https://www.roblox.com/games/ai-image-generator) |
| 🤖 SingodiyaTech bot | A Telegram bot focused on technical illustrations and diagrams generated by Pollinations AI, aimed at developers and engineers. | @singodiya_tech | [Website](https://t.me/singodiyatech_bot) |
| 🤖 Raftar.xyz | Raftar.xyz is an innovative social bot platform that uses Pollinations AI to create engaging and interactive experiences on Discord and Twitter, focusing on community building and automated content curation. | @raftar_official | [Website](https://raftar.xyz/app), [GitHub](https://github.com/raftarxyz/raftar-bot) - ⭐ 0.0k |
| 🤖 PolliBot | Telegram bot that generates images, text, and audio using Pollinations APIs. | @pollibot_dev | [Website](https://t.me/pollinations_bot) |
| 🤖 Anyai | A Discord bot and community for AI-driven content. | @meow_18838 | [Website](https://discord.gg/anyai) |

### Learn 📚

| Project | Description | Creator | Links |
|---------|-------------|---------|-------|
| StoryMagic: Interactive Kids Stories | Interactive and educational tool for generating kids' stories. | - |  |
| Riffle | A powerful tool designed to make reading English books more enjoyable and effective while helping you build your vocabulary naturally. Using Pollinations AI to create content that incorporates your own vocabulary words allows you to learn them in a vivid, engaging context. | <EMAIL> | [Website](https://riffle.ink) |
| OkeyAI | An LLM created by Africans to understand and have cultural awareness of African contexts and languages, OkeyAI outperforms many LLM models based on size and intelligence, OkeyMeta uses pollination image generating API to train it's LLM (OkeyAI) on images in real time. | @okeymeta | [Website](https://chat.okeymeta.com.ng), [GitHub](https://github.com/okeymeta) |
| Apple Shortcuts Guide | Video guide on creating AI images using Apple Shortcuts. | @tolerantone | [Website](https://www.youtube.com/watch?v=5NR5h7DTtEI) |
| Connect Pollinations with Open Web UI tutorial | Step-by-step guide on integrating Pollinations APIs with Open Web UI for enhanced image generation. | @pollinations | [Website](https://github.com/pollinations/connect-with-open-webui) |
| 🆕 Whizzy AI | An educational AI platform for students featuring AI-powered study assistance, chat functionality, and image generation capabilities using Pollinations AI. Designed to help students with studies they find challenging. | @vaibhavcoding69 | [Website](https://whizzyai.vercel.app) |
| AI儿童故事 🇨🇳 | 基于此项目 构建有趣的孩子故事书应用演示 (Based on this project, build an interesting children's storybook application demo) | MZ | [Website](https://kidss.netlify.app/) |
| Pollinations.AI AI/Teens talk | Session 2: ai/teens worldwide conference exploring the forces shaping AI today, diving into governance, virtual connections, and decision-making with voices from multiple European cities. | @thomash_pollinations | [Website](https://www.youtube.com/live/5Rvdfr2qYGA?si=i5NLOKI49fGxNAEK&t=1034) |
| POLLIPAPER | A research paper summarization and exploration tool that uses Pollinations AI to generate concise summaries and identify key concepts from academic papers. Helps researchers quickly grasp the essence of complex documents. | @paper_explorer | [Website](https://pollipaper.vercel.app) |
| MalaysiaPrompt 🇲🇾 | A resource for discovering and sharing creative prompts, supporting the Malaysian creative and educational AI community. | - | [Website](https://malaysiaprompt.rf.gd/) |
| 🇨🇳 Chinese DeepSeek Tutorial | A tutorial showing how to make DeepSeek AI support image generation by leveraging Pollinations.ai's API. | https://linux.do/u/isinry | [Website](https://linux.do/t/topic/447840/235) |
| Artistic Styles Book | An interactive book showcasing 90+ artistic styles. | Juan Gmo. Rivera | [Website](https://proyectodescartes.org/iCartesiLibri/materiales_didacticos/Libro_Estilos/index.html) |
| 🇧🇷 Tutorial | An in-depth Portuguese tutorial on using Pollinations AI. | Janderson de Sales | [Website](https://guiadehospedagem.com.br/pollinations-ai/) |
| Podcast #1500 | Podcast project powered by pollinations, featuring dialogues among LLMs. First episode features 3o-mini and DeepSeek R1 70B talking about Vibe Coding. | @brain.diver | [Website](https://open.spotify.com/show/1wu4ngb1dclyTwoNN4cZzK) |
| Proyecto Descartes | Educational initiative integrating Pollinations AI into STEM. | Juan Gmo. Rivera | [Website](https://proyectodescartes.org/revista/Numeros/Revista_8_2024/index.html) |


# ======================================================================
# TEXT.POLLINATIONS.AI CONFIGURATION
# ======================================================================
# This file contains configuration specific to the text.pollinations.ai service

# ======================================================================
# SHARED CONFIGURATION - NOW IN /shared/.env
# ======================================================================
# The following variables are now handled by the shared utilities:
# - WHITELISTED_DOMAINS → ALLOWLISTED_DOMAINS in shared/.env
# - Queue management is now handled by shared/ipQueue.js
# - Authentication is now handled by shared/auth-utils.js

# ======================================================================
# MODEL API CONFIGURATIONS
# ======================================================================

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Mistral API Configuration
MISTRAL_API_KEY=your_mistral_api_key_here
AZURE_MISTRAL_ENDPOINT=https://your-azure-mistral-endpoint.openai.azure.com/
AZURE_MISTRAL_CHAT_COMPLETION_ROUTE=/chatcompletion/chats
AZURE_MISTRAL_API_KEY=your_azure_mistral_api_key

# Claude API Configuration
CLAUDE_API_KEY=your_claude_api_key_here
CLAUDE_ENDPOINT=https://api.anthropic.com/v1/messages

# Anthropic API Configuration
ANTHROPIC_API_KEY=your_anthropic_api_key

# Groq API Configuration
GROQ_API_KEY=your_groq_api_key_here

# ======================================================================
# AZURE CONFIGURATIONS
# ======================================================================

# Azure OpenAI Configuration
AZURE_OPENAI_API_VERSION=
AZURE_OPENAI_ENDPOINT=https://your-azure-openai-endpoint.openai.azure.com/
AZURE_OPENAI_API_KEY=

# Azure Llama Configuration
AZURE_LLAMA_ENDPOINT=https://pollinations.openai.azure.com/openai/deployments/pollinations
AZURE_LLAMA_CHAT_COMPLETION_ROUTE=/chat/completions
AZURE_LLAMA_API_KEY=

# ======================================================================
# ADDITIONAL SERVICES
# ======================================================================

# Karma Configuration
KARMA_API_KEY=
KARMA_ENDPOINT=https://api.karma.yt/api/zeit

# Portkey API Configuration
PORTKEY_API_KEY=your_portkey_api_key_here
PORTKEY_PROVIDER=openai

# Sur System Prompt
SUR_SYSTEM_PROMPT=A helpful and intelligent AI assistant that is knowledgeable about a wide range of topics.

# ======================================================================
# SERVER CONFIGURATION
# ======================================================================

# Feed Configuration
FEED_PASSWORD=your_secure_password_here

# Server Port
PORT=16385
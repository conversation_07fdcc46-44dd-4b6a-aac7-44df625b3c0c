{"name": "karma-pollinations-react-doc", "version": "0.5.1", "author": "diogo-karma <<EMAIL>>", "private": false, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@pollinations/react": "^2.0.3", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@uidotdev/usehooks": "^2.4.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "debounce": "^2.1.1", "lodash": "^4.17.21", "lodash.memoize": "^4.1.2", "lucide-react": "^0.441.0", "markdown-to-jsx": "^7.5.0", "next": "14.2.11", "next-themes": "^0.3.0", "react": "^18", "react-dom": "^18", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "shadcn": "^1.0.0", "shadcn-ui": "^0.2.3", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/lodash": "^4.17.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^8", "eslint-config-next": "14.2.11", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}
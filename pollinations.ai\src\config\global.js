// lime: '#FFE801',

export const SHOW_PROMPT_TOOLTIP = false;

export const Colors = {
    lime: "#ecf874",
    offwhite: "#c7d4d6",
    offblack: "#110518",
    offblack2: "#181A2C",
    gray1: "#B3B3B3",
    gray2: "#8A8A8A",
    special: "rgb(191,64,64)",
};

export const Fonts = {
    title: "Maven Pro",
    headline: "<PERSON><PERSON>",
    parameter: "Duru Sans",
};

// Step 1: Create a new mapping object for section backgrounds
export const SectionBG = {
    header: {
        color: Colors.offwhite,
        gradient: false,
    },
    hero: {
        color: Colors.offwhite,
        gradient: false,
    },
    news: {
        color: Colors.offwhite,
        gradient: false,
    },
    feedImage: {
        color: Colors.offblack,
        gradient: true,
    },
    feedText: {
        color: Colors.offblack,
        gradient: true,
    },
    project: {
        color: Colors.offblack2,
        gradient: false,
    },
    integration: {
        color: Colors.offblack,
        gradient: false,
    },
    community: {
        color: Colors.offblack2,
        gradient: true,
    },
    team: {
        color: Colors.offwhite,
        gradient: true,
    },
    supporter: {
        color: Colors.offblack,
        gradient: true,
    },
    footer: {
        color: Colors.offwhite,
        gradient: false,
    },
};

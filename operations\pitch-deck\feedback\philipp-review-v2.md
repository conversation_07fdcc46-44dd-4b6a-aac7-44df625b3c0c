Topic. Number one is. Would love to understand. I think I'm a decent understanding, but would love to understand a bit more about Who is building on pollinations today? What are they doing with it? And what your view is of how that changes in the future. Um, Secondly. Understanding at a more granular level of detail.

What can your kind of what have you built to date? What can your product do today? And what can't it do yet? Um, and with that, kind of also what's the main usage? Um, And then, Thirdly. Curious about Architecture of the technology stack that you're using, and then Um, Forward-Looking.

Technology roadmap, what your current state of thinking is There's, uh, kind of the larger markets, but I would start with the user and then go to what the usage software today. I would try to go through in that order. Totally, totally yeah. Yeah. Yeah, yeah, okay. Um, what do you think if we start, um, just by, um, let's say because we've got on our, on our, on our home page, we've got a like constantly updating list of projects.

Other people have built on pollinations and there's quite a few nice ones in there and they're quite varied that we just like A three or four examples. I think <PERSON> we have a Elliot went and so before they were we just like kind of added them to a list recently.

We've kind of selected we've we've gone through them and actually kind of rated them. So we also we have like now a featured um, In place. So maybe we should use these ratings to to choose the ones we show. It's something. We we didn't do yet, but we want to have like A bit more of a practical demo.

Yeah. Selecting a bit better, the, the different. But but I think since we have made that selection we can we can we can show a few Um, so yes. Um

So yeah, I mean as as you know, um um, until now we've been really focused on on traction and um, and like growing growing our users numbers and making it as easy as possible to build your app. And right now, we're in the process of understanding. Now, millions of like, millions of people are using our service.

And we are we have a kind of let's say we have an idea who they are because we're talking with them every day and people get in touch when they add a new up to uh to uh let's say App Store. If you want to call it that right now.

Um, but let's say a systematic evaluation, especially because we're not with let's say we're tracking about 50 of our users and the other 50 percent they are, they they are using it kind of anonymously, and this we are changing now. So we are, we are starting to impose certain limits on Anonymous usage, that forces people to join our wait list to be to, to join.

And so now we've already got, I think like about Our wait list. And we and and so we're right now in the process of understanding who are these other 50, um, That we haven't, um, let's say tracked yet. Yeah, and yeah, yeah yeah yeah. To slowly like To, to give advantage to the ones that that support our ads and, and not to the other one.

Yeah. Yeah, this is, this will be the next step. Maybe but yeah, exactly. Um, so This one is quite like, uh, Yes, gpt4 freeze is one of the, you know, there's like a bunch of now, like Chachi PT, clones open source, GBT UI clones, and there's some very big ones.

Like this one has, um, 45 000 stars on on 44 000, 85, 000 GitHub. And they are they, and actually, the users of these apps are asking them this happened now, last week to integrate with pollinations because pollinations, they have always a list of providers. So you can choose open AI.

You can choose Germany for all of these. You need an API key. So now users are actually creating issues in these um open source chat Bots, asking for them to add pollinations to their like repertoire officially supported. Um

List of, of We are in the list of Um, models that are supported by, by, by GPT for free, which has a huge user base. The same thing happened yesterday with Cobalt AI, which is also one of the big players in the let's say, open source. Um, Open source.

Yet, let's say yeah, yeah, and maybe we should show things that we've actually tested. Um, Um, then tested them all but I always like forget half of it. Okay, let's take, let's take this one. And what is, what is this? Um, Um, May make it a bit bigger. Okay, okay.

Start using these plugins, maybe not so straight this is this is there someone someone it's an area case if someone made a plug, plug envelope lobe chat.

Yeah, maybe it's too complicated. I think we've got hundreds of apps where we don't need to sign up or anything that we can show. Let's just want to know that it works. Okay, it's kind of like a big, it's a city. Tavern has has silly. Tavern is one one of the very big, I think they have, like something like 100K stars on GitHub gave, they've integrated pollinations as an official image generator in their, in their, um, You can open that up.

And if you want to generate an image with it, you can choose pollinations. Um, And um, Yeah, yeah. But yeah, let's check. Let's take some web apps earlier that. We don't need to, we don't uh, train Navigators. Yeah. Okay, this is someone who is into marijuana, I think. Um, Um, and Uh, created.

Uh cannabis growing assistant. Um so you can talk to you, you can talk to a chatbot about um About your your doubts about um, growing growing Mariana, get advice on different strains. Um,

Okay, let's go back to that. It's a lot of Defence more like okay, yeah, here you can ask AI for help on growing. Uh, my say my plants are looking very sad or something.

So is it okay that I put them in a dark basement? And and so, so we're seeing this a lot where, like kind of let's say um small specialised pages are adding pollinations that as that kind of a system because they can customise quite easily. Um, what's the system prompts?

So they are like giving it instructions, talking, the character that I'm Want and now here is a perfect opportunity where we can where we can drop ads, right? So we know someone is someone is interested in growing their plants we could drop ads to a very good book on Amazon.

We could drop ads to a light. Um, we know the allocation Maybe you can trigger the ads Eliot. Maybe, let's see. Yeah, come on, let's it will work. I guess we don't have an affiliate who does, who does lights right now, but maybe we should add one So so P ads is like our internal trigger word to trigger the the ad system now because we don't have an affiliate who who is, who has anything to do with with Mariana.

We're dropping in our our like coffee link but this is like a good good. This is a good example where I chose that the more I feel the more Partners ad Partners we had the more we can um Something targeted into the into the chats. Oh, so this week, we, we were thinking a lot about like the exact like monetisation plan and on the other on the, on this ad here, we realised.

That what we, what we can do is that we can um instead of sending this, we can join like a full ad with a picture with a video whatever we want. And then we can tell the user to uh embed this in this website in a good way. If they do, then we would like reward them with a higher rate limits better models and things like that.

And we can wait how much money we get from these ads. And depending on that we can give more or less to the to the to the, to the website. Yeah, I think that way it would like allow to embed the apps to to make sure that the end user will embed drives very well if he wants to benefit from like the best services that we can offer.

That gives more money back. Yeah. Um,

Developer behind strong. Australian Navigator. Um, And here you probably don't know, but any hypothesis of It kind of how that person came across Polynesians in the first place. Uh, yes, this, this developer has been in our community over a year or a day already. They became now a mod, a moderator in our community.

So we know, know them quite a long time. It was Word of Mouth. Someone told them pollinations is cool. Um, I've I've made my little my up on it and um, They checked out. They came to our web page, and then joined our community, I click on Reddit, a lot of people that come from.

There's also a quite a few, um, YouTube videos. Um, we I haven't seen one recently, but, like a couple of months ago, there was one way, like one and a half million, um, views. And this YouTube video was saying pollination is the best, um, the best um, open source like, um, image image generation solution.

It was a time about images. Like, trying to but but, but like for example China, um, recently, um, we saw this huge uptick in users, from China. And of course, we looked we looked into where these users coming from and what happened is that and this has happened a few times already in the past where suddenly we got this bunch of users because a new type of user discovers that they can use pollinations easily.

So what people in China noticed is that if you prompt deepseek, the official deepseek in the right way it can, it can generate images with pollinations with absolutely zero need to add a plugin or anything just because

Out any plugin. So that then, As as soon as that happened, there was two or three blog posts in Chinese with a lot of. A lot of people reading them and suddenly we can see in our analytics that from deepseek.com we are getting like, um, 2 000 users per day.

So it's all very kind of like organic and it could also happen that suddenly someone makes a video of uh, or we get posted on hakka news and we suddenly get many users. And we, we need to also prepare for that because I see rather than us as as slowing down.

Um, as having the opposite, um, problem that we suddenly get a huge influx of an even larger, influx of users. And so this is also, like, something we need to be prepared for. Um, yeah, yeah. Yeah.

Maybe we showed a hydra llm also, or do you want to go to questions first? Because because that's some someone who is in antler who since yesterday implemented pollinations into their huh. Yeah, yeah, yeah. Uh, because it's kind of interesting because they only, they, they actually, you know, how they came across pollination.

That was super, I was so happy about that. Um, they asked like, how can I, um, make images they were talking with Germany? Like how, how can I add image generation to my, um, to my app and then Germany. Uh, first thing Germany was suggested was use pollinations without, you know, without it being connected, it's already in the training data and it also was able to show in the chat, an image.

It was able to generate an image in pollinations in the chat. So this has been kind of my long-term plan. That's why we've had our API docs in a machine readable way since over one and a half years that we become part of the training data of future. It's a new

Easiest ways to make make images. But anyway, and that's how the Hedra that's, that's how the what is Thanos is his name. Yeah, yeah. He discovered pollinations because Germany recommended it to him. Um, So let's go to Hydra Hydra, l m. Um, I think it was that com Otherwise, I have to have the URL in mind.

Yeah, yeah. Okay. Yeah, you're right. Google is I forgot how to use Google. I'm just Um, so now he's just, this is also since yesterday, he's added, um, image generation. And he had this, he had this idea. Um, which he saw in another image generation generator called Leonardo.ai, where you can very quickly narrow down on on a style, Of images.

And so um um so let's let's go for something uh, let's say um, Um, A. Um, To communicate. With cats. Okay? And now he's he's basically said because he has this idea of Reddit Reddit um kind of view generating your Reddit thread with a variety of of opinions now here.

Yeah, yeah. Okay. Let's do. And and then he was aberdaining thought, okay? We can also do images the same way. And so, now what he's doing here is talking to pollinations in the back end. Um, he's using uh, these images will appear in a second. Yeah, you already see.

Uh it's not Exactly a device but here this is a very fantasy style device. I don't do community. I don't know why it's with Cuts but yeah, we see the principle and this is a I like this one because it I think in the future future everything will be round.

And, and, and his, his idea was a very simple idea that now you can do more like this. So it will, it will take this as the basis and give you three more three more Alternatives. So you can very quickly kind of you can very quickly narrow it down on your style.

So you see here this kind of new prompts and and it's kind of generating new new variations and he was able to do that. Uh, he told me in half an hour with pollinations like uh he was very happy to be able to like add this functionality so easily to, to his app.

It's of course, it's not perfect yet. It's still a work in progress but it's what was done in half an hour. Um, and I think that's

If his app becomes viral, we will partner with him. We will. He's also very interested in ad placement. Yeah, like you can see at the bottom right here, it would be perfect for Yeah, we could, we could also embed under the images and upsell to, let's say, um, to get your image in a resolution for print quality, we could, um, allow I mean, we could allow people to do further things with, with this media, we could, we could give people ads about, um, uh, devices that allow you to communicate with animals.

Are there? Um, going back to kind of

Kind of, Create. Developers. I do. What? How do you think about the different kind of archetypes clusters that you have today? I think. Um, so I think we have, like, I would say, we have like four, 40, 40 are, uh, teenagers. Um, who are who are just learning to to code.

Um, with uh, with vibe coding tools. Um, We have another. A 40 percent of like, um, Independent developers. Um, and we have also, like 20 of people who are building more, more serious products, who are using, who are using pollination. So who are getting in touch with us saying, hey, we are, we we we we can you, can you provide a backend for us with like, with like, um, let's say premium features for us with noting this.

We haven't like gone and implemented it yet, but I would say 80 are between teens and and India indie creators. This condoms growing Community. Where would they form? They would fall into any tests or can I just take this for a second? Sorry. Where would where would they fall into in their classification?

Would you consider them a more serious product or just strain Navigator? Ah, the strain it's definitely in the, uh, Like that's actually on the yeah. Very specialised and narrow but we still see kind of some value there. Yes. What do you think is kind of the most likely next alternative that they will be using?

Um, yeah, I think they would probably um sign up to Open the eye. Or or or Google, Germany. Um, There's I think. As as far as I'm aware, of course, this landscape is changing also every every every week. Right? But but there's yeah, yeah yeah, like this one. For example, I will just show like, it's like a bit more serious up.

I would say it's on the Apple Store. Yeah, we have some apps on the App Store, too. We have both. I think two Android apps on the app. So, on the App Store, and one iOS up on the app store, where, where, you can Which also use pollinations in their back end.

So yeah, here you can see that it has like many different models. This is. This Is Us. Yeah. Yeah. Of course we're not the only back end that he's offering. Yeah. But we're the only we're the only three one. So when users download this app and they haven't got an account in open AI or in in anthropic, which is the majority in most of the time.

Yeah. Yeah. It gives a subscription and then you can pay to access any kind of models, but you can also like use ours that are that are the free ones here. Yeah, yeah yeah yeah. So I think our models would be the first ones people use and then they will choose.

Oh okay. I could also upgrade to use openai but I don't even know if they have a reason to because we're giving the same similar experience. Once in a while. And I mean also because our biggest our biggest, um, let's say our biggest. A user is the Roblox game which is doesn't fit exactly into the into the mould of the other ones.

Because it's like, yeah, it's, it's in another platform, and we cannot put ads into that platform. But there we already receiving a revenue share, right? Um, so

Yeah, we're freaking at some point if we could like Foster like a lot more people that develop games that are successful. So the next thing would be to make a startup up for Roblox developers, so other people can easily pollination start effects so other other teams can easily develop their games on on Roblox.

And if you see here, right now, we have 700, concurrently active users, we have It was a I we have 16 16 million, 16 million visits and actually our starting age developer who's making. This is currently, he's this is he stopped developing this since two months. He's working now together with us on version two.

And I really trust him because he really understands Roblox. I'm always like giving him certain ideas. Yeah. Yeah. And he's very clever. I'm worth giving. Don't you think it will be it would be good to do this and he's like no that's a really shit idea. Know, if we open this game, you will see.

It's it's interesting because for example, Elliotts could really not understand why people would do this. I do. Um because like because teens they don't really enjoy going to chachgbt.com. And they so they are basically just talking to an AI model in the form of a character. Um, And um, And this has.

And yeah, this is that this is so popular. Is interesting to us. Because for us, it's quite normal to talk to AI models, you know, it's a bit smaller here, but I can say, hey, hey, Nicole, she's called Neko. I'm feeling a bit down today. No, no, I'm feeling great today because I'm doing amazing.

Because we have so many users. We have seven users. My ego is going out of control. And then now this now of course from his back end, he's got a he's got a token. So of course we know which which requests are coming from him now she's answering and it's I don't know if I've been swimming, but Now she says and she even gives me a hug, you know?

And, and this team is very clever. Now he's using Roblox because he's earning Robux this in, in in-game, to pay other other people to make his new maps for him and stuff, so, yeah, yeah.

Okay. Yeah. And there, I mean there, we're collecting a lot of data of teams starting chatting with the cats, Um, maybe there's someone who wants to buy it? But yeah, yeah yeah. And another thing, I mean, that makes me very happy is that we've got like, people who Who use it for Education.

So, there's for example, a teacher in Spain who is teaching all his classes with with pollinations, um, and there's also now, um, blind and deaf people who've made apps, um, on top of pollinations using the multimodal features. So, for example, the um, Has made an app that you can use with his with his phone to take photos and it explains to him with a voice.

What's what, what he's seeing I know we are not the only one but but I'm finding it very interesting that people are doing this and also someone who did a Dominic in sign language, um, generator. So we have a lot of these little Niche applications but I do believe that a lot of those could also, um, Receive an interesting kind of like when we have a version of like checking what's going on and and Yeah, and accentuate, the the Yeah.

And I mean, of course, we we think once we have, um, once we have cash in the, in the company, we can also start incentivizing more, like, do contests, you know, give people who make the best app, um, of the month or we have an idea for an app, right?

We're like okay, this idea of Leonardo AI is so great. We certainly have 100 developers who can incentivise, who we know, you know, who Google we can incentivise to, um, build this up. So at the same time, we're a little bit of an incubator. We discovered this this 18 year old Ukrainian teen who he was got, this very crazy domain knowledge, and this is also a thought that this could this there's a value in this like in this kind of discovery of of, of talented in the developers.

And of course, also of startup ideas where we're seeing every day like two new ideas of what you could build on top of AI.

So,

Then let's go a little bit into Um, What kind of? But now it's very flipping around. Let's start with the the architecture, how you've built it. Um, And then that's from there, talk about kind of what can pollinations do today. What kind of doing that? Yeah, do we have an architecture diagram, that's quite high level Elliot.

What about that one? Um, it's a bit not so high level. Yeah. That okay. But maybe the mirror is easy, you know. Yeah, I think this actually has a lot of charm but maybe Miro is more more people to understand.

One second. But you can. So we're we're we're Gateway. We think this one is good. Yeah. Okay. This is also the I mean, this is the whole authentication flow. But it's kind of like the architect Journal. Okay? Okay. Yeah. I will try to make it a little bit. I can't.

Yeah. Can I show one because which I'm Wait, I would just let me just show you this one story about that. Um, let me just because I think this one is quite high level, and, and shows I kind of kind of what I wanted to explain here. This one Can you just open that one on GitHub?

Okay. Okay. Do we have it? Oh yeah, yeah. Just make it full screen.

Okay. I don't know. I think you zoom in it, but then I need also to Yeah. Okay, so I just killed it. Yeah. Oh yeah. Sorry.

How's it back?

Okay, so we are there. I will go back. Oh yeah. Ah okay, yeah, that's good. That works. Okay. Um, so On the left side, we have the, we have the clients. So we have an AI systems. We have an MCT server now, so they can talk through our mCP server directly to the image to our image generation.

We have a, we have actually a lot of people have built. A lot of Bots that are running in in Discord are only one two. Our own one is, I think one thousand in one thousand Disport communities. It just allows you to make images from text, so people add them to their communities.

Yeah, this all goes to the image to the image. CDN is a cache. So what we're doing there is that if someone requests the same image, um, Again, and again, also people have embedded pollination Dimensions on their home page. So some images, get get a get viewed multiple times.

So we, we are, we are we, are we are, we are caching them here. Take the same thing. Same same thing is happening. Then we have a, um, Gateway on on cloudflare. And okay, in the image case is easier because we're only talking, we're only talking to Uh where I need to leave the cloudflare and we're talking to one model that we're running ourselves on AWS.

I think maybe the details are also important. Here, we have a safety, a model that does safety checking on the prompts. We have an in what type of safety checking. So this is like um not safe to work stuff. So so nudity um, Pornography and especially, especially sensitive. If anything is mixed with yum or or or a child it is.

It is very strict so because if you don't do that, it gets very weird. Uh yeah. Yeah. And um, So yeah. So we also have um since a long time. Um we've had a prompt enhancer. So what happens is many users, they are prompting the first time they are writing like a beautiful cut and of course, the image generation models are much more impressive when you add a lot of detail to your prompts, so we're passing every pump through a language model.

That adds details. If it if needed Someone writes in Chinese, it will try, it will translate into English because the models have more data in English. This is, this is, in our back end. This is already been there for a long time. And yeah.

The problem of asking you do with opening eye? Yeah. But we actually now this became one. So we're just asking the model to, um, to enhance and translate. It won't go before. We had it, we were using a special Translation up up, but we realise now that the language models are even that are translating.

So we've been doing the same in one and yeah and this goes so the actual Um, the actual models behind the image generation are they are running on AWS on on GPL gpus. Yeah, that we are administrating. Recently in terms, we used a lot to Cloud lab because cloudflare has a serverless serverless offering where we don't have to worry about like, adding gpus when usage grows, you know, because it's quite an, it's quite a lot of work.

Of course, there's all kinds of things like how to scale it you know that help scaling up and down but we've moved a lot now to serverless because we realised administration all that infrastructure costs. It takes a lot of our time and we got um and there are services that kind of do that for us.

It was also a question of costs, right? Um that by administrating, our own gpus, we could do a lower price

What's up AWS? For the image service? The tax service had the same thing. We have a cache. Um we're using this also to store to install order, all the data, we're saving the data together with metadata. So we're saving, um, the and I, we have to check like the in terms of compliance, of course.

But we're saving the, um, the the, quite the GPA, even an approximation of the GPS coordinator the request with saving like the user agent and, and so on. So we have this like data, which is both location. And let's say, condensation, I don't, um, but this is something we are thinking about in the future.

Yeah. Um, we have, um, again a Gateway. It's not doing. There's no language models. Running on our Gateway, the Gateway is just, um, rooting the requests to this, to these different apis. Um, we'll try to offer a lot of models, so we're talking to a bunch of back ends.

Um,

Up there. They they then we add them. Yeah, yeah. Okay.

Garage. And then, Where do you do authentication? Um but right now if you look at like these clients that were on the left, yeah they are here. Yeah they are they don't have to we're not forcing them to authenticate. So they so only uh, a percentage of them, we've given them a token, and then they use it's the same as an as an API key, really.

You know, so they get a token and then they, then they add this to their request to pollinations and then we save in our database. Okay, this request came from that, um, that app. This is what we currently implementing the authentication layer. And this is a this way we are doing um, through Through.

So to administrate your API tokens, you will you will talk to an assistant. Um we are not building a UI for this. Um and this assistant you can this is the beauty of mCP you can you can bring your kind of pollinations dashboard into your into your own assistant.

So if you are used to working in Casa, you can just connect to pollination mcp server and then you say hey I need a token. It just gives you the term right in the chat. And the nice thing is because I will already put it into your top end file, you know?

But this is whatso, we can always add in a small web interface on top. That's more like an implementation detail where you're talking to it through a through, can people use assistant or of a web interface? And then, For the ones that don't have to authenticate today. Um, you put a limit on.

How do you, how do you prevent? Than just using that with essentially. Generating news every time. And so you cannot just generate a token um um when you don't so these are these leaders, they don't have a token. Then then we have an, we use the IP address. So exactly.

So we we enforce like one IP address can only make one request every so and so many seconds Would need to keep changing their IP somehow which is pain in the ass. Yeah, yeah. Yeah. Yeah, yeah. Yeah. But we couldn't make it even even worse like, yeah. Yeah. I mean, that there is a way like, because I've researched, how can you, how can you mess?

How can you mess with us, right? How can you abuse us? And there are services that allow you to buy, uh, 200 proxy servers right for for 20 euros and then these people could. Yeah. Add like 200 clients. So we are also aware that this totally free Anonymous access.

But but right now it's it's, it's working for us and it's this kind of um, a growth driver. Um, and that, I mean, I'll be cases doing something similar with the with their zero zero gpu0. They also have a, a part of their service that you can access totally for free without even authenticating.

So I've been also watching them if they are able because they are way bigger. So there's probably maybe way more people trying to abuse them. Right. So I've been also watching them to see like um how they how they are doing it. Um, Like keeping the free service to to, to, to everyone, but putting some limits that are like, very restrictive.

And for us, we can manage the cost quite easily like that. And then, based on what they build we bring into our, like, yeah. We we give them tears and then, uh, depending if, if they make 100 per day, they have this year if they make 200 per day, another year.

Um, Distribution between image and text and it's a it's about but I think tax is bigger but we we're not monitoring text uh correctly. We are just But but I think the fact is, it's it, sorry. It's like two third one third, maybe. Yeah. Yeah, but the fact is, we start with text much later.

So, image has been going since uh, Text, we only launched, I think six months ago in November it's been catching up and we see a text becoming the bigger bigger bigger player in the long run for tax year. It went to cash bar recently, right? Yes. So the numbers that you shared were both like doubled text.

Um yeah we have to actually check that. It's like we have to check it better. Yeah, yeah. I don't wanna I I mean, I think our numbers are so good. We don't need to like, um, make make them look bigger but it won't be done because it was always like a bit upper like a bit uh because recently recently, we integrated caching.

So the, so the numbers. Now like if you if you take the numbers this day, for example, let's go to Text generated. Once again, check generated I put the image to right. Yeah. Is that per day? That's per day and that's the last seven days and what are the numbers?

I can't see them. Exactly. Okay, so you can see like two million, um, million images ah, and 1.3 text. But these are the requested. So if we put the generated that are actually, oh yeah, that's more apt comparison. Yeah, okay. So You can see like that is a bit like Uh-Huh 1.3.

Yeah, and these are, these are these are not cached. These are not cached. These are, these are actual. These are actual generations. So actually text is higher than than images. Yeah. But that's that's the actual events, the amount of users because kind of text is a lot more back and forth than I do with images.

That's right. That's right. We need to, we need to take that into account. Are going to be significantly High Island image. Totally totally. You're right. I think text is usually and we we need to add those into our numbers. I think there's on average for for for there's one, average for interactions.

Um yeah. Yeah. Yeah. And comparing to actually yeah interaction. Yeah. Yeah.

Having tried to get a feeling for the clustering of

Kind of what the applications do with your data. So, Um, Consumers, business applications, entertainment, buses. I don't know, this is games. This is Um dude. Do you have any kind of feeling for what what the major groups of usage are a lot of chats? Yeah. I mean a lot of image generators?

Yeah I mean Basics. There are many of them. Yeah yeah. Yeah I mean but I think my feeling would be like um We have. Because I look into the logs. Up to debug stuff. Not because I'm trying to spy on our users. Um, Um, and so I there's there's a large amount of just uh I think 40 percent were.

I think we also have to differentiate between text and image. Let me start with text, um, text, I think 40 are just regular chat experiences where people wanted to other chatbot to the to their site and decide can be personal but there's also small businesses. Um, so let's say the herbal tea, the herbal tea um shop, right?

The um, We have to really uh I think this we will do ASAP to have like a good, a good like pie chart of okay these these users are coming from there but we have like 20, 20 people, 20 kind of teen games. Um, Okay, let's and and then we have The Discord Bots are also a large percent, it's it's roughly maybe even equal between just chat experiences on, on your web page.

Um, Discord people having built Discord and WhatsApp and telegram Bots that use pollinations, um, text API. We have a lot of plugins into, um, Into uis. There. There's WordPress plugin. So someone who wants to add a gen AI features to their WordPress site, but as you were seeing with my answer, I'm I don't have a very good answer on that and I, I really want to change that.

Um, Exactly. These are the These are the users. Um yeah. And usage types. Yeah. Yeah. It's been a bit hard. Also for like yeah to to go on every phone for us too. Yeah. Yeah. There's a lot to do. There's the community. There's like the bugs to fix. They're like so it's a little bit like challenging to to have everything tight and stuff.

Yeah yeah yeah but also it was yeah that's beautiful just like extra push. Um And then the image generation. That is mostly surfaced. What type of? Experience. This mostly also. Use a face and chat experiences. So yes, yes, most of the time. Um, I mean, we have everything from your, your personalised Pokemon.

Um, um, um generator, you know, like where we have, we have teens who are into Pokemon and you can make your your Pokemon card, you know? Like, um, we have also like, for example, we haven't. We have also some quite a lot of integrated experiences that use both our text and our image models.

So we have this, um, app that writes let me just show you it. It writes you a little it writes, you a story and with images and it reads it to you. Um, so it's it's like a it's it's it's focused on like, kind of um, fairy tales, a little bit younger demographic.

Um, but in that case, they use they actually use free of our apis. Um, and this was a 16 year old teenager from India. Um, they used both, uh, they use text. They use audio and they use image and that's kind of nice because we offer this kind of package where you can make this like experiences that that that use um, a variety of different.

Let's say modalities

And we were also very proud that Quinn Quinn. Um, from Alibaba Gwen is one of the now they've just released version version 3 of their of their um, language model. They are, they're really competing with deepsea and open Ai. And so on, we are in there, they have an agent on on, on the, the official coin agent.

And the, the first example you see on their GitHub in their readme, is their their agent using pollinations to, to generate images. So yeah, yeah.

Understood, and then,

Um,

Yeah, let me just show you this one. Let's, let's talk about that. Um, One of the biggest things that annoy you, that you don't have yet kind of what Um, We have four hands. Forehands.

No, but I I can. Can you show? Yeah.

What is what is our plan? Yeah, I'd also be interested in the Hellcat if you plan to integrate the ad serving or integrating out serving into this architecture chart. Yeah, yeah. So, I've been working a little bit on readjusting the

I really want to have a better way to show this, but for today,

Okay, I cannot look sorry.

He was writing about something else. Have a fox, just add it up to the gleaming glass doors, her backpack slug. Gently. Yeah. And her eyes wide with excitement. The audio is not for me, right? Yes accelerate Of owls who invented invisible ink, which I couldn't use the text. Yeah, it's multimodal.

Actually, it's not just doing things like Wizards.

How many articles does it do now? The smoke, right? It's doing five, no, actually no five to the text API to the audio API and one per image. Um, by prompting one thing. Yeah, we have five five calls. Yeah, 10 with the text, no one for the text. Yeah, five for text and five things because it's doing five parts of the of the story.

And each is a is a unique call. Audio. Exactly. Like if it's just text audio. It's one goal, okay? Okay, sorry, wrong map. So, Of course, this is like, Adjustment. But like basically, what we what we what we have already Uh we have like the the user monetisation that is like zero, but we're testing on the ads, then we have like the platform that is already live, that Thomas explained before.

Um then what we planned back for Q3 like, so from What is it like from? Yeah, from this July. They told the dogs to SDK access. We have these parts. That is like, very okay. And then, there's like this. Um, like I think this is the, this is the point that is like, super interesting.

I think is to Is this what we are talking these days with Thomas? It's instead of like doing the the the shared Revenue right away. So we don't do anything about sharing sharing with the with the end user. But instead we have worked them with um, better rate limits better models based on how much we get get from their, uh, the ads that are on their website.

And like this, we can serve with the, with the answer of charge PT or whatever, you can give like a code block, or a link or whatever. Like, because it could be like a plugin that they already are integrating in their websites. And so they are incentivated to put the the commercial nuts inside the text or something like that, but in the website in the best position possible like this, they can like The bits if they make money.

So this is a way like to skip for now. The 50 50 Revenue. Bring it later. Like maybe like here. It's like for q, uh, three 2026. You also have. Just side note. Eh kind of obviously, Revenue sharing Comes with a whole bunch of legal shit, right? Yeah, you need Yeah, kind of all the teams that don't have video entity, you can't share Revenue with them because they can't write invoices and stuff like that.

Yeah, yeah, exactly. And I mean, it's interesting to see how Roblox is doing it, and they're also getting into a lot of controversy, of course, because of it right? Yeah, yeah, yeah. That this is the working but yeah. So then we have like it's okay. Like then we have like the the so that would be like.

Um so okay, so this is like super better, so we will be testing this part. Then here we will have like a false We can like start it with a small percentage of the oops. What happened? When you say app provider integration? Have you thought about kind of what app providers you want to integrate, we need to check in details because like we were we were kind of limiting ourselves until now for with the this affiliate links.

But with this option now we can like go for for all the major ones and then we don't have to Right now, right now we're integrating principally with impact impact.com, which is a network, um, which allows us to not contact each each, um, each brand individually, but certain ones where I thought, okay?

It would be when I've seen like, in the logs. Okay. We need we really need one. Uh, Reached out to the brands directly and joined like an agreement with them. So we have like three or four. Of the of our Brands, who, who have contacted personally, and the other, like, 20 that we, we prototype, we're experimenting with right now.

Come from impact.com. We're looking at Amazon for the refer to for the referral links because there we would have a very large amount of of, um, things we could link. And then Course, as as we, we came to the conclusion that If we can serve whole widgets to our customers, then we can put our.

Then we can drop Google AdSense in there. Because the limitation first right now was that we cannot send the Google AdSense to their page, because we're just sending them texts or or images, but with this anyway, but this I don't wanna. Yeah. Yeah. Maybe let's stay more in the road map.

Yeah, so yeah, that's testing this system here then, uh, The the plan will be to put this system like fully like on. So we have like this different, like, Thomas, which is depending on the framework that they are working with. It's fits the Discord. But if it's like a JavaScript or like whatever we, we will give them different types of plugins.

So they can integrate our apps, the best possible. Then we would like to probably like have this, create a premium subscription so you don't want ads. Uh, you can also pay and then you don't get any ads. I use probably. Um, So this would would be tested beginning of the year next year.

And, I mean, it could end up before, like, I think this is a bit like stretch a bit like far, but, uh, yeah, premium subscription. Then we have like a better targeting the SDK. Yeah, and corporate feedback. I mean it's like And there is the community side also that is here.

Yeah. Uh, and then, uh, eventually at some point uh, when we we are ready, would be like a 50 50 or something like this Q3 2026, right? Yeah. We have the 50. We would go for the revenue, share, um, model. And I'm also aware that, uh, that that to do Revenue share, it's the legal.

Um, Side is is probably complicated. I don't, I was also researching, how, how much could you Outsource that? But maybe complicate. That's why we're also thinking. Like, yeah. But I mean, it's the it's the most effective way of like, say aligning our incentives with the, with the, um, with our creators.

Yeah, yeah. Yeah. Uh, Down.

For the people that don't have some sort of sign up, you have some sort of fantasy currency that they can redeem once they have. Yeah yeah yeah. I mean I mean I mean I know a lot I I know a lot of people who would not stop talking to me now about uh crypto token that it's the perfect thing to do but I got very tired of of crypto tokens.

I told you once um, Robux is is not crypto and it's, but it's something you accumulate. Exactly. Yeah. And and maybe people would spend it on getting cooler models now, right? This is something that could be balanced automatically. So they get a score based on how many ads, the the they got clicked, or they got like, um, how much money the website is giving decides which level they are, you know, like, yeah.

They could even get like a a point, right? And that they can then spend on on having the coolest image generation. If

Those points paid out in real US dollars. Yeah, yeah, yeah, they have to do it with their mum. Yeah, it's probably the most sense of accumulating and if it becomes big enough and three years down the line, they register, and get paid off. And that and I mean, also my like the, the belief is, of course that when, when we start building this Revenue system Revenue system, we've already.

Um, We are already we already have our second fundraise and we already have also the expertise then uh in the team to do this. So far, um, Yeah. So so we're seeing actually we can even show you some stats if you want. Um um we're seeing actually um sorry.

Are you picking kind of specific apps? Where you say okay this is ones where I'm gonna place it or you we're placing it uh in a broad spectrum. But we're we're filtering first, like, if the type of conversation makes sense for another. So so for what type of conversation are you doing it today?

So we're doing it for conversations where the answers are little. Well, it's not just like

Where where there's some topic. Um, that is that is talked about and where, um, the answers are like informative. So it's we're using a language model, you know, we've got a prompt saying. Hey, um, um, if this conversation, um, Is talking about the topic that fits with this list of Affiliates, then add this ad underneath and the percentage is about, I would say, um, is about 30 percent of all of all.

Um, let's say requests that are made are they are currently classified as as

And I mean, of course, the idea everybody in principle or ad ready for the inventory that you have. No, no, no. But they are not ready because we had, we are doing what we are doing now. Is we're adding the, the Donate. We're adding the donate to pollination link to the ones where we don't find, uh, finder.

Yeah, yeah. Request of 30 of conversations. The conversations. Yeah yeah, yeah. This is how it looks like one one. Yeah we have one ad. Yeah, we have this kind of like little little uh list. Um, we give them a weight. Also, depending this is done manually. Now we look in in the in the impact.com and see, which of the, which of the brands are leading to, the highest click click-through rates and then we we're manually increasing the weight here in our list.

So we're doing a little bit of a, of an optimisation. This should be automated, of course. But you see, yeah, we we extract some keywords here. So if any of those, if any of those keywords appears in the text Then it automatically then then it's not even passed through the through the then it's automatically classified as relevant for an ad.

Let me just. And into analytics. We work with it. Well oh yeah. Edward analytics.

So that's when that's like, let's see. This is too much. We didn't even start at the beginning. So let's put the last two weeks. That's between 20 to 8. I hope my battery will pulled. If you have one, I think I forgot. Yeah. So, they must have been to Northwick and 114.

Um then hey really like an AI AI girlfriend um service and and the event is actually click or yeah. This will really have to understand why. Yes, yes that's clear. That's right so little yeah yeah that's one. Clicked on this one here we might have some bugs also I don't know, but Between not too much.

That some of them they they get zero click and a lot of impression others. I mean, maybe it's just like how it is and people never want to click on capcut for some reason. But maybe there is a bug on the on how we display capcut, uh, link. I don't know.

Yeah. It's really enough talking to the person but we're like sending ads to

It's got to put it on especially now because we don't have time to, to monitor so well. Yeah. Yeah.

Less than one percent small. What I would expect. Yeah, yeah.

The populations is also quite high. It's only like seven percent. Yeah, I was trying to put the percentage here. As I spent one hour depending on the UI. Okay, we're not having details. You have a bunch of accidental clicks, right? Yeah, yeah, yeah.

You have.

No, no, of course, of course. But it would be nice. Yeah. Yeah. Yeah, we have all sorts of issues, kind of quickly. This capcut. Mobile app most of the time so if a lot of traffic is mobile, then it might deep link it or something and the link gets lost exactly like that.

Right? Yeah, yeah. Um, that many ways to problem can happen on the, on the way. This is why I think like if the user Back from from positioning to add in the right position. Then I think there's But, I mean, but I mean, look, this even without this, like, we're sending widgets.

I think these numbers are very promising. Um, and we need to and we we really need to just scale up the amount. We push out because if if you're talking about ads right? 11 11, 000. Other Impressions is not so much. Yeah, yeah, yeah, it's like three weeks almost like so.

Yeah. So it's very little compared to what we we we we could do right. We've been yeah, yeah. And we really feel like we're a bit stupid to not push out these ads. How does the 60 000 fit together with when you're saying? 30 of conversations are rather?

We were only putting it to five percent of of um, of the selected ones of the selected ones because we're trying to it, if there's bugs, you know, we don't want to show. For example, an AI girlfriend, uh, add to a teenage, uh, you know. And that's why we've been like kind of Cautious in putting it out but not because we don't want to put ads on all but just because Yeah, if we make a stupid mistake, now we can kind of anger our community.

It's just the development moment or so. Yeah, the reaction to the fact that you're starting to serve that actually surprisingly positive because I've done quite a few surveys in our community asking like what would you what would you prefer? Because I you know I'm I'm we we are also getting a lot of feedback from our community and people I was like how can you provide this for free and then and then we get into the discussion I'm saying okay we are we are in this phase right now but we are we are evaluating how to monetize our service.

And then I've been doing surveys and there's always an 88 to one more or less balance of people who prefer to have ads, um, to people who prefer to, um, to pay. And then when people come into our community, sometimes people are coming saying, hey there was an ad in my thing.

What is that then? Actually, our community is already saying like, yeah we are. They are very, very it's almost like the communities has embraced that this is the this is the

Five percent and you have what? Three Clicks at the moment. Um, And that's roughly a month of data, right? Two weeks, right, two weeks. To a little bit more. Okay, let's assume two weeks. Says that will be six thousand plagues. Times five. This is 100%. Uh, so we've been talking 30 000 clicks.

We end up getting a relatively. Cost per click. Surprised you get a Euro per click. And it's quite high, I guess. Yeah, yeah, yeah, yeah. Well, this type of advertising quite high obviously. Yeah, yeah, yeah. Of people. On Google search for life. Insurance products, you pay on Google 45 here really quickly.

Um,

Have you done kind of like that type of back of the envelope, mass on, kind of how much money you would make with your advertising inventory? We, we we did it. Um, Rough. But we did it for text and image. Yeah. I think now, with this plan of diversifying, the kind of app that we can use, it could be like a lot better done and we could, like, show you this maybe early next week.

Then we have a projection already that we could show, we can show different things. So, um, I've been working on this disability. I want to review it. Uh, this is what I sent you, I think. So here we can second I want to go. I'm going to dock directly.

I guess it's easier for me. Hey guys. Just two dumping. I have to uh jump to another one. Yeah, cool. Bye-Bye.

But these numbers, like I really need to double check. I have something else. That is a bit more simple, but because if I tell you that those numbers are, right? I didn't double check yet. Have it. So we have What would be just while you're doing that? What would be the monthly cost today if you had to pay for the usage without any card credits.

So here you can see The costs. And the the revenue. So this is like the text One less. Okay. So this was like some estimations, this is more or less. Like, what we Uh, the the amount of text that we generate per day, Um, then Like, we have like a growth that is like simulated here.

Same with the text opponent, text add potential. That's this is like a growth because we're getting better at targeting audiences. Um, and then here, so this is something we would launch in in, in in, in September let's say. This here we have a click through that is like two percent.

Um this is like the the costs that we we have for every every like 1000 requests Is assuming no credits or that is like I mean here we calculate everything and then on this like 12 you can see the cost here. So that's uh that's the that's assuming that we are not.

We're not using any uh any calculators. Um, yeah, yeah, yeah. So, but then like here, like the monthly cost, this would be like, 12 000. But then, the revenue would be 32 000. So this would be like the net positive. Um, September, 3x roughly, right. Um yes but I really would like to do better numbers.

But yeah it's an idea. Then you have the image. Yeah, of course just like focusing because kind of you say here that you are 12 000 per month. Yeah. But in your other credit calculation, you say that you currently spend 2 000 Euros per day of credits. So this text this is this is only Texas, okay?

This is text only, but it's a lot more expensive. It's three three times, higher than A, in terms of usage, So, at the moment for the text stuff, Four thousand dollars in kind of cloud cost. If you didn't have credits, is that For the text only these days. Yeah.

No. It's it's less than four thousand per day. No month. Amongst its model 4000. Yeah. Currently for the text. Yes. Yeah, it's like 12 but probably what's the question? So maybe they didn't follow. I'm trying to figure out kind of what would the cloud cost that you have today?

Oh, it's it's about like 30 000 a month or something like that. Maybe 35. And this is without any optimisation. This is also like giving everything free for everyone. So yeah, and how much of that is tax for this image I would say it's quite 50 50. The costs are 50 50 because tax at the end is like yeah it's more expensive than I thought like at first but also we have to also think that those models like one year ago you have a model for chatbot that is like capable.

It's expensive. Now, the model that's capable is less and in two years yeah, it's it's been 10 times. It's been 10 times every year like the the same, the same. Um, Of 10 times cheaper than we. Could offer the same same time last year and that's continuing, of course.

People also have the expectation of people goes up, right, if we still offer the same model but but it's still been falling quite, uh, quite so, it's been falling five times. You know, for the, for let's say, our state of the art model we, every year it's been falling, like the cost of serving, it has been falling five times.

So we also, uh, I mean we're aware that we probably I think we're probably quite close like, in terms of spend and and, and revenue potential in like a couple of months, but this is not calculating that. Also, the the, the cost per media is going down, uh, very consistent consistently.

So and I think there's few AI companies who are really making a net, uh,

Quite a nice project but, of course, you know, you know that much better than us. Here, we have like an aggregate. Like this is like text image, and also eventually like this like Roblox game model, That is like simulated. So for this Roblox game, I was like, okay, we have 50 clients.

Every client generates a revenue of 400 per day, so Yeah, it's it's a yeah. But I think like, like based on our first calculations, it was pretty obvious that if we get enough ads out and there are enough clicked on the ads, it gets it gets positive. Quite, uh, quite quite uh, So much doubt of that but we we didn't we didn't test it completely yet.

Yeah yeah, yeah, yeah. But on the numbers and also this weekend, I will spend like the weekend on. I really want because we have, we had a lot of different types of ad that now we can and I would really like to get a model and see where we would like break even and like, calculate everything better.

I'm going to end over the weekend. I'm gonna push the the percentage of ads we. We, we, we, we, we, we, we, we, we, we put out because, yeah. Day or expending 30 000, that something is off here, right? Um, this is the, the This is kind of like a simulation in the future but like you can see that.

Yeah, if your traffic grows up, why does why does cost less than half of this today? That doesn't make sense. But isn't that because it's only text here and the other one. No, this is the app share. So this is like only Roblox, okay? So that's what robot is costing.

Yeah. Then you have the image and then you have the text. If you're saying today you're paying for 30 000 half of, that is taxed. Yeah. The cost being lower than it is. Yeah I was also hoping that that we would have like a lot better like optimisation at that time and like we could save a lot of cost here and there but but like like you say it's also true.

Those are estimations that I need to to give you better numbers. Yeah. Okay. Like 60 000 per month. Yeah, it's probably less but this is this is really rough. I need also to to it's a bit complicated because it changes a lot and then because of this Cloud subscription, we don't have like real dashboard that are really nice.

They put you in different like facing on on their their because they're on their face. Yeah and then it's a bit like more difficult to extract the data. But I would I will have, I will have good numbers of exactly what are the costs and what what we can do with the ads.

And I I really like we haven't done any optimisation. I I'm I'm I mean, I'm, I'm a really, I've been working with AI, like, for a long time, I have like in, I have like five, five different optimisations in that we can do. There's many things we can do to save money and we haven't done any yet.

First of all, we're giving quite, uh, we're giving access to very expensive models and very cheap models at the same time with, with no differentiation A very cheap models are 100 times cheaper than a very expensive models and and I don't think I'm pretty sure we wouldn't lose that much that much users.

Um, if we, if we just restrict it if we just allow them to use only treatment it's just like yeah, we're doing so much and we're just like, okay just just use it build and and we have we have all these credits but but of course we're realising. Our Runway is not that long and we should uh think about this optimisation maybe rather sooner than later, just after this program, add implementation?

Yeah, yeah.

Really gives us a lot of optimisation possibilities that we wouldn't have at the lower scale which is like a lot of the first the first three interactions with the language with the language models. They're often very, very similar. You know, when you have when you've saved like one, one terabyte of when you've saved like um three months of data that's um that's 300 million conversations.

You can with a with with like Vector. Uh, with with text embeddings you can you can um

Conversation turns you can probably serve it from Akash. And then on average people are only doing four, four, tons of conversation. And two, two, we have to generate, just saying that's one of many, many ways we can optimise costs, we that hasn't been uh, this will be like, offsets that is starting to be like completely upset.

Our goal is in the next two months to have or two months to have it at least balanced. Yeah, yeah, yeah. Yeah.

Good thanks. Super super helpful to spend the time and go a bit deeper. It increases my understanding a lot. Yeah, yeah. Anytime Very cool. Uh, It has Albers gotten back to you. Because I remember that you watch players. Yeah, yeah, yeah, yeah, yeah, yeah, not yet. No, no, no, no, no.

We have a few ideas of how to do that maybe at some point. Yeah, yeah, we need to prepare but um, yeah. Yeah better for. I mean there was a mention of it in the in the due diligence the last question. Said really brief. I mean, I'm still reviewing that dog.

We said we've talked with Felix that he said, it could be like a work in progress document. Yeah, yeah. Like working on it. I would like to put like a proper answer so you can understand we have two possible strategies that we're thinking to, to clear this like, um, cleanly and everyone's happy.

Yeah. So yeah, maybe you prefer like many, you can just like advise yourself on that. I'd be happy to discuss. I think that's that, obviously, that needs to be clean before because yeah, yeah, yeah, yeah, yeah, yeah, yeah, yeah, of course, of course. What do you think about this?

Because we, we were like, We were looking at like eventually the community and the the code that is like a part of the code could stay like open source and then inside like a lab that would be like pollination lab. I think we we already mentioned that and we wanted to come back a little bit more informed about how that, how that solution that solution would look before we.

Yeah, yeah, yeah. Yeah. Let's probably rather take time in it. Okay. But that's good. Like let's make a popular document. So, yeah, yeah, yeah, yeah, yeah. Um, and then

Person that is helping you out at the moment. You said, there's this, this friend, this consultant also that you're working with. But, by the way, sorry we we we we we kind of misunderstood because I I was in the office at some point. And um, I saw that like there was other people coming like Friends of the, of the founders, but I realised only afterwards that there was a specific day for that, you know, I kind of that's why you came to us once like ah, who is this guy who you invited?

Yeah, maybe you. Yeah, so so what happens that made as a Consulting, he gave us back some documentation. We are uh, we reviewed it. It's so so what we are our take now with him is that we posed. The, the Consulting with him, we want to wait to see exactly where we want to go.

Like what is a more precise roadmap is more? I think it would be really good if we would have like already five employees or something like that. And like to kind of make the organisation a little bit more like precise. United, that would be great. But for now, we don't really need him and I think it's like maybe he's two maybe.

Yeah, I was all we are two like small and focused on Tech and the very like, it's a bit too nerdy for him or something like that. For now, at least. But I know that is really Yeah, because I mean, what, what what he did, okay, what he did was, he's advised different startups in in different fields, um, to grow, uh, you know, um, but a little bit after the, after the stage, we are out and, and he's an old, a colleague of Elliott.

He's very talented. He's working more in, like, sustainability and, and sustainability Tech, and he has been following pollinations. We've been like kind of sharing our updates with him and he's always had kind of very, very good, like, let's say advice, um, you should think about this. Um, and, and he's and then he showed us like certain like so he creates he works with companies.

He sits down with them, he spends, three days with them and he analyses, uh, where the weak points? Like, what's the, what's what's the danger? What do you need to do? We were we, we were still deciding like because it's not very, he doesn't speak the same language as the tech world so much.

So it was a little bit like this was a little bit of a doubt Point, but we, the meeting was very good because we discovered a lot of things.

Have to really think about those numbers. This will run out soon. You're a bit crazy that you're you're still developing features, you know, so it was very good, but we're not 100 sure. If, if, if, if if he's totally the right person especially then we also talk to gleb and he was like, Sending him even to you.

To to suggested, maybe you could chat to him to kind of feel. But now I think what we think is just like for now the agreement with him is that we will pay is if we get like Offended and see depending on this reaction. If we want to work with him or not.

But that's really like your interpretation points too. We were thinking maybe like now like what we need more is like very like maybe the data uh, Engineer or like add a digital ad specialist. They are like, really like some key rather than someone with broad kind of business knowledge.

Yeah, is also like the main developer. If Thomas tomorrow is sick for two weeks, it's complicated. We need another developer that we like know the stack as well as Thomas. So there are like a few things that we need to to put in place before like maybe a person like Lauren comes in

What I'm thinking if I maybe Direct.

Um, and I think somebody who just does business stuff all day and what the fuck does that person do all day in uh, right now, um, I think, however, You would have a hell of an easier time. Raising funds and building this company, you get had a stronger communicator on the team.

Yes, it became quite obvious to us in the last few days. Yeah. Yeah, yeah, yeah. I think that's, that's kind of irrespective from us. Yeah, yeah. Because we then need to raise the next round. Etc. Yes. Um, That's, That song that is. As I'm getting to know you becoming more evident to I mean, I think there's also a potential for growth in us in that respect, but I do, I agree, you know, I agree with you but I agree with you in the sense that Just, Business customer somebody who just does business all day, doesn't help.

Um, Because any business to do all day long but we were never thinking of having him full time. Yeah, this was never a question but because you had also asking before kind of what was it in my opinion. Yeah. Especially to the team. I think somebody who's a pure business profile doesn't help.

Um, But having somebody Who's technical, but has a stronger. Communication Spike might be A good idea, it might help, obviously, it needs to be the right person who needs the chemistry and everything. Um, but I think it would make your chances at success higher. Yeah, yeah. I mean, we honestly, we are quite open, um, to to having another if we find the person where we like, chemistry is 100, we're quite open to have another founder join us.

We haven't looked very much because we've been, you know, we had quite a lot of other stuff. I know this was quite an opportunity also or is is still quite an opportunity to meet people. Uh, we haven't networked in that direction very much. Um,

Obviously, nothing is impossible. Yeah, yeah. It's the same kind of, there's also personal growth there, um, and Um, But just from, from the outside, I think your chance of success will be higher. Yeah, yeah, official. I think if, if at least we could we, we got, um, a little bit of framing a bit a little bit like, um, I think Thomas, you could do a nice pitch if it was very well prepared.

I think you you, you're capable of doing something like kind of like entertainment. You have to discouragement. You can do it. But yeah. It needs to be really like. Yeah. And the thing is you hardly ever just pitched? The scripted thing, right? Yeah. Yeah.

Level overview and then you go into improvisation. Exactly. Yeah, yeah. Yeah, totally. Yeah. I mean, yeah, that's what we have right now and we we're gonna do the best of it. I don't think before. Uh, the the investment committee, we will have another another founder. So we will do the best um, with the skills we have, right?

Um, I think it's also very good for us, right? But what we also noticed like I was thinking like if I this time I'm spending now on let's say, um, pitching I could spend this in like Improving our ad distribution and then we'd have better numbers, you know. So so it also makes sense.

In terms of like whose skill is best best where right? Yeah. Okay. But maybe communicate and and also ideally this person would also like maybe not it could be not Tech but it could be like also Community oriented then very like taking over like a lot of the Community Management and her husband needs to speak the language of your community.

Yeah, yeah. Yeah. Yeah, yeah. Okay. Where, but but we're we're learning a lot here and I think it's also very good product before sometimes to do to do this. Uh, yeah. Yeah, yeah. Yeah. Okay, okay, three people. Thank you. Thank you. I mean, uh,

I feel like, sorry about. Delay. We're just in a meeting with, with Alan and For everyone, forgot about the end time. Yeah, yeah, yeah. All right. It was it was good. It was um because I think we've we've been picked so far. We've uh, we've been pitching kind of.

Um, we've been pitching our product but this time we had like a chance of kind of sitting down with with some time and showing um Do all the kind of apps other people have created on it showing a little bit in more detailed, the architecture diagram and like showing the numbers behind like the first experiments we made around like putting ads into into our Content and so on.

So it was, it was actually quite nice for us because sometimes we have a little bit as we're learning, we're learning to explain it to do this elevator pitch, right? But we uh but it was very nice to be able to um uh actually go into detail because I think we have something really great there that sometimes we're not able to um okay so well in a very very kind in a high pressure situation so I feel actually very good um to have the opportunity, you know, to explain a bit more detail like what's going on behind the scenes and so on.

And how did Alan like, like I think it was, I think they, I think it was, they were very, I mean, we are like, Thomas, we are a little bit better. We are better at pitching, um, at explaining detect and to pitch a very simple concept that. Yeah, It's much more important.

The other one we will definitely handle. I rather having this this way than the other way. Okay, um, so that's, that's good. Um, so don't worry. Um, so that's I I think. Um today we just like, oh my suggestions because I understood that you have to I see next Friday.

Correct. Yes. That we go through your pitch deck. I think I have, you didn't send it to me, right? Um, no, it's it's actually also it needs to be updated. There are a few, like, I think strong points that we, we, we kind of changed a few things and it needs to be reflected on the pitch deck.

But let's go, let's go on it. And and then we, we can start from there. Let's go on it. Exactly. And I can give you my first input. Um, and then maybe we can start working on the one of the other slides. Um, But I would suggest that we go through that first and then, Um yeah, let's work on some some of the slides.

So I put it like that one second. Where is it?

Sorry. How's the business growing and growing growing? It's it's it's it's to have more users in there. It's like, it's like a own crowd. It's growing so quickly. Oh yeah, you're over one million now. Um can I can we see the notes or at all? Oh yeah I guess um.

I had it open also here maybe, are you saying the hot screen or just the window? Once again, I will try to The idea to pitch pitch it. Now, you know, or to go through the slides, at least. Yes. I think maybe it's good to pitch it because last time we already did something one time, then I can give you my general feedback on the pitch.

And then, let's go specifically to the one on the other side. Okay, okay, I would like, I would like the one. So give me just one second.

You can take the second to it. Yeah, I'm not so shit. I think I get why not to do it, but let me

Is. Do you have the do you have to slide in front of you? Yeah you get it if you know. Okay. Yeah you will have a speaker um okay whatever that that's good you see you see what so first like yeah we're not speaking the picture anymore Thomas is gonna focus on that box.

Yeah okay so um we're pollinations um we believe that AI empowers everyone. Uh everyone can become a Creator and um Lowest barrier to entry. For anyone to create. We are seeing that. Uh, more and more people are using AI assistance. More more and more people are coding even without they realising, they are coding, um, because the AI system is producing code for them to answer a question.

So any anyone is basically, um, becoming a developer can create their own apps, their own interactive experiences, and we're seeing that in our communities, Um, But the problem is that, um, it's a complex and expensive to build AI native apps. They are infrastructure headaches. There's no clear and especially for any indie creators.

There's no easy path to monetize their viral apps. Paywards, privacy concerns. And Brands and advertising providers. Struggle to reach these new audiences. They are they are visiting. There are a lot of like let's say apps that go viral in one or two days they stay viral for a month and then they they're not viral anymore before they even have the chance.

Um, Monetized through ads. Something, we plan to change that. Um, We? We fixed this problem. We make it radically simple to build and monetize. A parallel. Uh, in the let's say a tech world one. Could imagine one could think about Unity Unity is a large platform uh, for game development.

They um, Allow people. They allow all kinds of people to build games on their platform and they have a deal where they Where they push ads to the games that people build on their platforms. Um, we make it very easy to set up complex complex infrastructure configuration. We take care of all the back ends.

So you can focus only on the front end and build an amazing user experience very easily. Um, It at first, if you, if you are a young Creator and uh, as there are many already who've built a small viral viral apps on our on pollinations then. Um, Very happy because you have many users visiting your app, but you cannot easily make money.

We will provide ads into the content, so, and and provide a revenue share to these creators. So we can both profit from the apps going viral from day one. Um,

Attractionage is impressive. Um we have three million monthly active users. These are end users. Um, so we have a smaller subset of creators who are building apps on pollinations, but our content is being pushed directly to 3 million. Eventually active users, this has been growing 30 month of a month since the last.

I don't know eight months so we've gone from 300K users to 3 million users uh very very very linearly and this is continuing um, Have developed over 300 apps built on our platform. Some of these are very popular one up on Roblox has over. 1 000 concurrent users. At this point in time, over 15, 15, 16 million likes.

Yeah, sometimes 2 000 users. We're we're also developing a second version of that with a team creator who made this up, but I will get into that later. Um, We have just started rolling out ads into this content. So I think, um, uh, let's say, uh, we we are evaluating at the moment, but it's looking very promising.

Um, The market maybe earlier if you wanted you guys because I I never pitched this slide. Um yeah yeah. So yeah, we we, we actually were like, not in anymore in the youth ad Market like the ad Market that we're targeting is broader. We're going to the Indie, right?

I mean, not even Indie. It's like I mean this is the ad market. So it's like the the the so like the thing that we need to think is that, um, with AI and this new like by coding, everyone becomes a developer. So it means that a lot of space for us is being created all the time.

And then in between this uh, this uh like um Developer that create this app which would be like or some here. Um, yeah, we it's a it's a two-sided. We see it as a two-sided market. So, so on the one hand, we have the supply, which is, uh, our creators, our Vibe coders, um, who are, who are building.

Um, Um, apps daily. So, every day people people are registering with us, which apps they have built. So every day, um, the two new apps are registered on our app store. So we have let let's say a Simple App Store where people who build apps and pollinations can register.

So this, these amounts of apps are the supply. And then Our customer is basically the brand The Advertiser, who, who will be able to place ads into into those viral apps that are creators creators are making.

So, the idea is that we're creating a circular economy. We're aligning the by, by sharing the revenue with our with our creators, we're aligning our incentives. So they will, they will be interested in displaying the ads. We push to them in a in a very good place. We? We all benefit by by earning more money together, and we have a lot of, let's say mode.

In terms of trust, we are the, we've been, we've we're constantly, uh, being being, um, Uh, let's say recommended. Um, people are making YouTube videos about us. Um, saying that we are the best. Open source. Ai platform we we are organic growth. Is is tremendous. Actually we are we we we haven't we, we don't need to yet do anything.

Um, we don't want to because uh, this 35 we have to keep our infrastructure. Um, Um running but of course, that's why we're also getting money. Now helpful. That's why we're getting money to be able to scale this up. We're looking at 15, we're looking at 15 million monthly active users in a couple of months.

And um, our roadmap is in the next 12 months. We we have now a, let's say, an MVP ad system. So we are working with different brands. We are pushing different brands ads into, um, Content that we are serving to our customers. This is right now in a prototype phase.

We're starting to collect the first numbers. Click-through rates are looking very, very promising. Um but this needs to be scaled up. Now in the next 12 months, we don't need to add many new features apart from Let's say getting to know our customers better, registering them all and monitoring better, how much how onboarding our customers.

Um, and then We want to scale in. Uh the next 12 to 36 months, we have a a whole bunch of plants we want we The main thing, of course, that kind of drives, how much money we make is, how good is our ad system optimised? So how well are we matching?

Are we matching ads with? Um, with our users we have very large potential there because we are we are putting ads into conversations and into um interactive experiences where the topic of conversation gives us a very good idea, what kind of art we could serve to the user. So that's why we're also seeing very promising, click-through rates on the small scale, um, small scale experiments.

We've been doing We want to grow.

Here, we have more? Yeah. Yeah. We in terms of Team, we need, uh, we need, um, we are also, uh, looking um, for, uh, Um, found out tentatively not who has a who is who is stronger on the presentation and and the communication side. Um, so we can really focus on on making the the technology work, right?

And of course we need to we need more um backend engineers and we need um marketing and and Community Management uh as soon as possible.

I think, that's okay. Yeah, I am. Great. Thanks a lot. Um, Um my general feedback is probably you know that of course. Yes, yes. And in general, I like this style of your presentation so that's fine. Um, I would change a little bit the order, but let's discuss. Okay.

And I actually have some points. And usually, and I just had another team before you and Yeah, I would say maybe even 90 percent of all teams. I'm always surprised yet. They are not really showing a product. And also, you did not show the product. And, and that's why I'm always surprised because Um, I'm super look, I'm I don't know anything about you.

Let's say, right? I'm a new UVC and then I'm looking at the presentation. I first of all, want to see what you are. Probably I did some research, I checked the web page and also on your web, maybe don't find that much on it. So, yeah, one four products like where I understand.

Okay, you have the great thing about it, man, you have already great shopping. I think you mentioned that that one app which already has? I don't know. Who 2000, active months? Daily uses make a showcase. Look, this is looks like when it's in when somebody uses this, right? Like when somebody creates a game, you can make this out of our AI show me something.

Yeah, yeah, actually actually that Mostly missing the most obvious of like just showing the product. Yeah. So that would be great to have one slide. Look at our product. This. We are coordinations. Maybe. Maybe you've realised it a little bit of too long intro. Yeah. Just say we are pollinations one sentence to it.

Then you switch to the first slide. Uh, introducing you and then maybe so already your product something like this? Yeah, yeah. Like that order. Because then I have from a beginning on a very clear, good understanding What what you are and then I want to know stuff about markets, talk about whatever.

But first of all, I would love to better understand the eyes. Are making the presentation as well, right? I want to see and if I see, oh, wow. Okay, that's that's coming out or that's how the product works. I mean, you like, usually, maybe it takes that days or weeks in in, I have no clue, right?

As you may, as you see like of development, uh, for for Um, for for Indie death, and then the outcome could be and then you show like one or two examples of games but, you know, but you know what? It's interesting because it only like yesterday we talked. Uh, maybe that's also the coolest example of the show because yesterday, we talked to someone here from the from Antla, go hard and uh in 30 in 30 minutes, they added um um, pollinations to their products and it really, it really.

Um, it's a real value. Add to the to their product. Um, But I think Elliot has has here prepared. I was just being. Yeah. And then I show you the real product. Yeah. Yeah. If something like, I mean this is just like it's not designed for a presentation, but it was just an idea that like, basically, like, what happens with pollination is that step one, the step one, the, the, the, the Creator will will build an app in is like coding an interface.

So it will say like I would like to create an app that generates Pokemon collection cards based on the user location and description. Please use pollination.ai to generate the image deploy, the app, and their monitors and add their monitor monetisation ad service to it, right? So the, the agent will say like, yeah, okay, cool, I will do it, so it will do everything.

Um and uh so you you then get this app that is created too. Yeah the end user will play with the created app and receive personalised ads. So like for example now it's generated like missile this, like Pokemon like card, Uh, with a prompt here, it creates the card.

And then based on the prompt, you get like, because it says that he likes to paint, then you get like this painting ads that are like AdSense or something like this. Yeah. Something like that. Maybe making it more understandable, or nicer. Exactly that I want. Yeah, I want to see an example, how the product in the end works, right?

I mean, my, my opinion is that we already have so many cool products built on pollinations. We should show the ones we have, maybe, um, the creator to the end user. This is what I wanted to try to. I don't think that makes more sense, but not having a very strong opinion on this.

Yeah, okay. Okay, okay. More visualising to, to let the, uh, like to let the viewer or The Listener, like me easily understand what the product is about. Yeah, yeah. Yeah. Maybe the, the, the strength Navigators is, yeah. Um, so okay, but let's start here, because this is not a, such a typical use case, but it's one of our biggest ones.

Um, um, so, um, a teenager from Ukraine, he's 18, 18 years old. He's actually, um, He's now living in Canada and he, he Uh, it's it's very nice to talk to him because he, he kind of arrived in Canada. He doesn't didn't have so many, um, friends, but he found pollinations and started.

Um, talking in our community a lot and, and he built this um, Roblox game, which is called AI AI character role play. It's the top AI. If you look if you search for AI on Roblox it's a it's or at least a long time it was the it was the top AI.

It was the top AI game in over all of of all of Roblox. Okay? Now it's the second but we haven't our team is developing a version two right now, which will then be number one. Yeah, that's the sponsor. So we're yeah, we're really Def, we're really the top top AI game on Roblox.

And what this kid did was they they are using pollinations dot AI to power. Uh, a conversation with a character in the Roblox game, we have quite an impressive amount of, uh, It to this game. Currently, there's 700 people playing it and all of these people are talking to pollinations.ai through that game.

So, and this is, this is Roblox pollination. This is also integrated in. I will I will show it to you now. Um, pollination is also integrated into many Discord Bots. So people have these called Bots in their communities um which allow them to answer questions about the community, um, create images and so on.

Um, so we see a lot of bots bot Integrations. We see telegram Bots, Here you go. Here, we have this little game and we are this character. And we can now chat to this, uh, little cut character here. We can give it a hug to Of course, it's not really a targeted demographic but I can say hi Nico.

I'm feeling so happy today.

And then she will write back to me. And a lot of a lot of people are using it for kind of companionship. But this is, let's say one of many use cases and this is already monetizing. So we already made 2000 2000 Euros from this because Roblox is paying us and please do this example, right?

I mean, of course. Yeah, that maybe somebody wants to see it but most likely not, right? Like not going live into that game, but just make a screenshot, yeah. Yeah, yeah, yeah. Then explain a little bit, you know, a little bit of background, how many active users what you generated and how you generate the money.

You know, maybe make this example and I like it. Yeah. Um, yeah. Yeah. And to to understand. Okay, what what's the product about the second main point? I would like to raise for you now, before we Took some minutes to really go one by one is The whole thing about, The business and the unit, the unit economics.

Maybe if you can go to that slide. Yes. Elliot maybe you go into the unit economics. I mean I have something better actually than what we had. So here I have quite some documentations but it's something that we, we need to, to get the number right. Like, uh, As soon as possible, we add a little bit of New IDs, so we can integrate like normal ads into our product quite That would be like rewarding much more.

So, where is this one? Unit economics here. Let me put the clean version here.

Yes, yes. So that's the company that I was talking about. So this is like this, the rich is, is the the image ad or video ads that we, we can use that are clickable Uh, so, um, like what what I said at the beginning also, is that like we, we, I know I didn't say that at the beginning but like we we won't do the revenue share for for uh, the first part of our like, for phase one that would stretch till like, um, Q2 next year.

So we would get like 100 of the revenue. And instead of to reward the to reward the the creators, we will give them better accesses uh, to um faster and this kind of thing. So, it's absolutely but I think, but people want to see and in In an easier way is how the unit economic then really looks like when you're fully up and running when we have the full product, How that works.

And then I would probably like to see, like, okay. Um, Because when you listen first, then you might think okay you will get money from uh from from the app creation. But you don't right. You just get money from advertising as I understand it. Yes. Yeah. And then I would like to understand and how does it look like?

And and also here, maybe an example look there for game, it has so many Whatever, if you do so many ad Impressions? Yes. Yeah. Then the cpms is here and that, and then an average game makes this much in revenues and we have a 50 share and 50 stays with us.

Okay. You know, the basic, uh, just a very easy flow of the money would be nice to really fully understand what it is. And then you say, like, okay, your goal is to have whatever six games times excuses, and then I kind of understand, uh, where was so okay.

So closer to that actually, because I'm coming from the ad market. So I know a little bit of it. Are you really always paid in CPM or or is that like also like, uh, CPC or CPI? Model that you get paid, because then it's getting tricky, right? Because you have to, then Uh, yeah, through the performance.

Probably, for, for your customers. Actually. Yeah. The, the the tricky thing, this is exactly the tricky thing. Right now. The, um, we've we've, um, entered in, in a few different. Um, Agreements like, through through impact.com with different brands. So we have we we are putting referral links. So if someone is talking about cyber security, uh, we are putting for example nordvpn, um, referral link um, into our into art into the response and the the only problems with the system we are using.

Now it's not CPA, we're not where where we're only getting, um, paid when we convert, um, a user on their platform. So this there's a time delay because they they're always going into um, Into, let's say two or one month trials and and uh it's it makes it a little bit difficult for us to monitor exactly, which is the highest performing ad, what we can see is that um we have um about depending on the ads, some ads we're having like 12 um, click-through rates so out of 100 Impressions.

People are clicking the link. Uh, in the, in the impression 12 times, which is very high. And then out of this 12, we we're seeing that, uh, about three percent are converting to to subscribers. Uh, this is like one specific service. I'm talking about, I'm talking about, hey, real, in this case.

So we have, for example, in the last two weeks, we had 5 000 ad Impressions and we had 600 clicks. So this is like 12, I have clicked on it and out of those 12, I think um about um five percent Subs, 10 10 or five percent subscribe, but out of those subscribers none have become paying customers yet.

And, and we've seen now, our first, um, um, customer, um, become a paying customer, but this, this was three weeks after they subscribed. So that's why I kind of, like, let's say our signal is very delayed. So we're actually looking for a way of putting ads that gives us a more direct.

Let's say, reward feedback. If you see what I mean? So what do you mean? Yeah, yeah I I fully see what you mean. That's why I'm Yeah, I'm I'm still wondering if Are you already sure that this is the right Bismuth? I mean, are you, are you basically then in the end, the ones, what I didn't fully understand.

I I, you really, uh, the ones that look for Advertisers. So that's your main job besides creating the text, so that you need to go out and find the right. Advertisers, is that so, I mean, the reason is, that's why we signed up, um, we're looking at marketplaces. Um, so for example, or or Yeah.

Yeah. So yeah. Yeah. Yeah. Yeah. Not merchandising this. I need to understand, right? So um I think that's probably the biggest question mark for me to be honest. I totally believe that I can interrupt it anyways. Yeah, but taught me with a look and there's somebody who's already creating with your AI.

2008 users. I say great for me like kind of a checkout. Yeah, yeah. Yeah. You want to understand that was all good. I I would say Um, yeah. And But but, but then I'm struggling with that point. Like How do you really make money? So you will be challenged and grilled a lot on that point.

If you say, we are taking care to acquire the advertisers and I would say oops. Yeah. Oh man, that's tough. If you say like of course we just want to get plugged in uh to to like the ssps right to then talk to dsps. And then I get programmatic revenues in Um, okay what's the strategy here?

Okay, could make more sense. Yeah, this this part, I would love to understand more. So if you should definitely have a clear slide about the business model and how in the end you earn money and and what it falls and uh, that there, that the money is not coming.

I think you said it but I want what way once that really clear the advertisers are paying you. Or the platforms. The marketplaces are paying you, right? Yeah. Yeah. And you make it split and in the middle, you need to see that. Um, you either, Take care that. Ideally.

The tools, the platforms are taking care that the best ads are coming to use, right? Um but where are you here, right? Like we were super well connected uh in that in that space. So probably you would if not you need somebody, you know coming from the ad space.

Yes at Tech space. Yeah, yeah, yeah. I mean, right now, it's not even text. Now, we, we make it completely brutal. Like, yeah. Sorry. Sorry. Um, so this is right now. Uh, so our plan until until recently, uh, was so we we plugged into impact.com, which is a large Marketplace for Brands.

So there we we already added 50, 50 Brands through there. So this is, this is mediated through there, through their Marketplace, but we are doing the, the mapping, so we are analysing the conversation and then choosing using AI. Of course, which brand is most appropriate for this conversation. An obvious thought is of course, that there's companies who've spent billions on optimising, uh, this right?

Like which, which is the correct ad to show to which customer. So, um, We're actively researching like platforms that take over this. This mapping, you know, for us. So we so we just we just send them the topic of the conversation and they send us back the best ad with the best, um, catchphrase for for this, right?

This is we are doing right now ourselves. Okay. I think it's worse. Uh, giving some more input on that? Yeah. Yeah. Yeah. Whom are you really generating the refuge is it that one platform in the end? They are advertisers behind it but in the end Marketplace impact or how it was called.

Yeah, yeah. What are the other options because you probably don't want to have just one option? Um, so guys, I need to better understand because in the end where the money is coming from. Yeah, it's yeah. I mean you have both sides, you have the supply side, this is your your uh, your users, or your, your developers and then in the end the users who are using the game, that's the supply side, okay?

You need to take care of this and on the other hand you need to make sure that the demand side is there, right? Demand side means the advertiser. You say one thing I I understood. From already the results that you're having, I would make a checkbox. The other one, I would question a little bit because if you have a lot of Supply, You know, if you make everybody laughs your your AI everybody's using it uh firsthand because I think it's a good thing but then in the end they're making revenues because the demand side is missing.

Yeah and you automatically will lose the supply side. Yeah. Yeah. I guess it was a challenge of the two-sided, right? Because we have uh the two-sided Marketplace that both both sides need to be vibrant to work and that's why I can just tell you a little bit from from the ad text space.

Uh, that's At the beginning everybody was like working. Also like on a model. Uh okay I I take care. I I have some Uh, you know, like magazines as on the supply side. Yeah, whatever. Build the EU or or NBC de or.com or whatever um as is on a supply and now I need to find advertisers so I was running to uh to agency one, two, three.

And and you know look we have great content from Bill DE. Do you want to make revenues with us? Yeah. Okay blah blah and and then of course everybody was doing this but it's too small in the end. And then how a market usually develops is then platforms occur.

Yeah. And then there was like the ssps and the dsps as you probably know, right? So the SSB then only focusing on the supply. And and the DSP was only focusing to get the demand to get the advertisers. So Just wondering if in the end you are also saying like, okay I just want to connect.

I I want to take care of the supply side by the way. So if you would have a goal for the supply side, it would have been the better choice in in the ad Tech space. 10 years ago if I would decide, I would never go to the plant side, always to the supply side, the ones that got the higher access and because it's the data because they have the data Supply and the data and advertisers always like fucking mess with the with the big agencies and so on.

And that in in the end you're saying like hey you want to concentrate on that? You want to have a great AI, you want to have, you want to build a great Community, you want to really focus more on the supply and then you, of course, need to see where you put your supply in.

Where you plug it in? Yeah. And which tools do you plug it in that if you get the right exercise, right? In the end. I again like I don't know how you fully thought the truth, just a little bit of my input. That what I saw that people that tried to do both hate it.

Um, developed that in the end. They said, we need to decide. Do I do this or do I do that? Yeah, I mean my feeling has also always been like, um, I I would ideally like to do one thing and do it very well. So, if we, if we can find a partner, um, and I'm pretty sure these Partners exist with.

Uh, we've actually found a few, very interesting candidates that can take this whole part because it's also, I mean, you, you, you see us, right. We, we see advertising as the most, um, the most promising path for pollinations, uh, towards Revenue at the moment, but we're not natively from the advertising space, right?

So, um, of course, um, it will be much more clever if we could Outsource as much as possible to to a platform that that does this with millions of of Brands behind them. And we don't have to, like, add new brands to our database, right? Um, so make sure that um, because then you're on the supplies that you just need to make sure what you mentioned already that that Going into the right, right?

So you need to hear that a little bit, but I would also say, if you if Think about it more. And if you make one slide of pollination, how that really looks like that, you're more focusing, probably on the supply side and the vision is to get plugged in to the big ones.

Like I don't know if you Unity is an option or to Amazon Marketplace, uh, or to Trade desk. Yeah. Esps out there looking for Yeah, so for for additional Supply or even you make corporations with ssps and you say say like the SSP, you can take our supply. Monetize it for us.

Also possible, right. But but that part, I'm missing. Maybe, because I'm from that space, that's why I'm digging into that topic a bit more but it does. You understand how you really want to scale? Um, This demand side. Like what what's the strategy? Yeah, yeah I was missing. I know and I'd love to do that and what your ideas are here because when you have enough, if you are great on the supply side, if it's a great tool and you can create a lot of Interest, and a lot of users.

Uh, then the monetisation, that's a big question mark. What's what's What's your strategy here? And yeah, it was too little in that presentation. Well, I'm not 100 sure. If you have fully sold that probably not, I I need to go Um, the team slide. Um, Be more bold. I mean for me, the team's life, I have the feeling you making yourself so small.

It's like, oh yeah. By the way, we need somebody else for this and that and here, yeah, I mean, like, I've worked at Amazon for example. Um, and uh, we've got a few bunch of of, um, Things. We are not saying this is, I think a little bit. Also, we, we need to be a bit more bold, uh, just Um embrace it fully.

I think it's really interesting what you said because we we I think we're really great actually on the supply side and and um And it's very interesting because you know, this this area I'm wondering if we can send you some, if we can reach out to you by email or we just wait for them next because actually, our investment committee is already supposed to be next week and it could be nice.

If we could have like, one more feedback session with you once we've, we can move the next Friday. Maybe yeah, on Wednesday. Um, because I And they can check one seconds. It'd be great. I can also send you a few like a short but a few documents that will like.

Give you some numbers that we will have a lot of a better research next week on the which ad we want to plug in. And yeah, let's focus on that. What time is good for you on Wednesday 5 30. Does it work 5 30. Yeah, yeah, yeah that's good.

Perfect. Um, That we that we have some time on Thursday. On Wednesday night, of course. Um, yeah. Okay, but let's work on it in general. Again, like I'm, I'm I really love it and you show tractions and that's your biggest asset is attraction. Yeah, yeah, well on your community.

Yeah because of all you have a great, you are a great team but of course you know your weaknesses and you and and Uh, Next Step would also to look maybe for co-founder. More on the, um, Um, What is it? Like more CEOs. No one see or not but like more Yeah, by the way, I did not fully get that because one said like you are doing financing and strategy and then you said you're looking for somebody who's presenting.

I mean this is, this is the feedback that we got. Like, I mean, you you kind of said a bit the opposite at the beginning that the presentation was not, uh, the the was not like the the problem actually and it's actually better that we can explain the tech really.

Well, I mean, this is what I think I understood at the beginning. But we got a little bit of a different feedback that our communication is really messy. And, and that we could like, will use a lot of help especially to go out for the visas. Uh, I I think I said you need to practise.

I think they meant that. I think the president president slides, and the flow. I like like on everything as I mentioned, where we need to work on someone's life. I like, but yeah. From this presentation skills for sure. Yeah. You don't hire somebody just for the presentation. No, no, no, no.

We have an AI. That does it exactly? I was You can even generate a pitch every time that is a bit different. You know, like Exactly. So so like again like the teams like you you are a great team but you want to have an addition maybe if you think about it like more on somebody coming from the marketing space, However, you you want to Then and maybe also somebody.

Yeah, I don't know. Look if I like the product I I don't care if it if if you're not speaking fluently. Yeah, perfect. Yeah but I'm different maybe in that sense so I that's not the point. I would probably again like understand that that advertising thing much better and then maybe that this is more the missing link business slash Um yeah, yeah, yeah.

And you know at Tech marketing experience because if you want to generate the revenue with marketing then probably that's that's your missing skill. Yeah, yeah. Um, yeah. Yeah, that's again like I I be more bold show you great product, say that you're a great team, you know, that your weakness in in that area.

So that would Than the mix hire or co-founder, let's see. I'm fine and next next time, let's talk a little bit, then more in detail about some of the slides. But yeah. Yeah. I think the most important thing part of that people understand how you make money. Yeah. Yeah totally.

And if you make money because People buy probably the supply side but they might have questions about. The Advertiser side. Yeah, yeah, sounds good. Good. Then talk to you on Wednesday and Uh, looking forward to the results. Yeah. By the way, send me, if you have an updated presentation until then please send it to me.

Yes, I will send you a few a bit of document on Monday. Yeah. Okay. Okay. Great guys. Uh yes thank you so much interesting. Yeah, yeah it was really, it's really valuable feedback. Thank you very much.

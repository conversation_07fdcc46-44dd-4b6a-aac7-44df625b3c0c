/**
 * Social Bots Projects 🤖
 * Discord / Telegram / WhatsApp / Roblox bots & NPCs
 */

export const socialBotsProjects = [
  {
    name: "🎮 <PERSON><PERSON><PERSON>",
    url: "https://discord.com/oauth2/authorize?client_id=1377330983740903586",
    description: "Your Sassy All-in-One AI Discord Bot. A powerful, sassy, and slightly mischievous AI bot designed to level up your Discord server with intelligent conversations, creative tools, and smart automation — all wrapped in a playful personality. Features AI-powered chat with STM and LTM, image generation & editing, image fusion & GIF handling, real-time web search, voice replies, media intelligence, slash commands, and dynamic intent detection.",
    author: "`_dr_misterio_`",
    submissionDate: "2025-02-24",
    order: 1,
    category: "socialBots",
    originalPath: "pollinations.ai/src/config/projectList.js",
    discoveredCommit: "15ec92f2",
    discoveredDate: "2025-06-04T09:29:31.740Z"
  },
  {
    name: "<PERSON><PERSON>",
    url: "https://discord.gg/anyai",
    description: "A Discord bot and community for AI-driven content.",
    author: "@meow_18838",
    submissionDate: "2025-05-05",
    order: 5
  },
  {
    name: "<PERSON><PERSON> Bot",
    description: "A chat bot integrating Pollinations API for text and image generation.",
    author: "@Py-Phoenix-PJS",
    submissionDate: "2025-05-12",
    order: 1
  },
  {
    name: "🤖 ImageEditer",
    url: "https://t.me/ImageEditer_bot",
    description: "AI Art Studio - A feature-rich Telegram bot that creates art from text prompts, remixes images, merges multiple artworks, and offers one-tap regeneration with real-time control. Supports multiple AI models (GPT Image, Flux, Turbo) with NSFW detection and smart layout features.",
    author: "@_dr_misterio_",
    submissionDate: "2025-06-02",
    order: 1
  },
  {
    name: "Pollinations Discord Bot",
    url: "https://github.com/Zingzy/pollinations.ai-bot",
    description: "AI Image Generation Discord Bot using Pollinations.ai. Written in Python with discord.py, used in 500+ servers. Features /pollinate command for AI images with prompt enhancement, width/height options, /multi-pollinate for 4 variations, and /random for random AI images.",
    author: "@zingy",
    repo: "https://github.com/Zingzy/pollinations.ai-bot",
    submissionDate: "2025-03-20",
    order: 1
  },
  {
    name: "Pollinations Telegram Assistant",
    url: "https://t.me/pollinations_assistant_bot",
    description: "An advanced Telegram bot that provides access to Pollinations AI services through a conversational interface with support for multiple languages.",
    author: "@pollen_labs",
    submissionDate: "2025-04-08",
    order: 1
  },
  {
    name: "Pollinations WhatsApp Group",
    url: "https://chat.whatsapp.com/pollinations-ai",
    description: "A WhatsApp group bot that allows members to generate AI content through simple commands, making Pollinations accessible on mobile messaging.",
    author: "@whatsapp_ai_dev",
    submissionDate: "2025-05-01",
    order: 2
  },
  {
    name: "pollinations-tg-bot 🇨🇳",
    url: "https://t.me/pollinations_cn_bot",
    description: "Chinese language Telegram bot for Pollinations AI with specialized prompts for Eastern art styles and cultural references.",
    author: "@cn_ai_dev",
    language: "zh",
    submissionDate: "2025-05-10",
    order: 2
  },
  {
    name: "Quick AI & Jolbak",
    url: "https://quickai.jolbak.com",
    description: "A multi-platform bot suite that integrates with Discord, Slack, and Microsoft Teams to provide Pollinations AI services in professional environments.",
    author: "@jolbak_dev",
    repo: "https://github.com/jacob-ai-bot/jacob",
    submissionDate: "2025-05-18",
    order: 2
  },
  {
    name: "AI Image Generator [ROBLOX]",
    url: "https://www.roblox.com/games/ai-image-generator",
    description: "A Roblox experience that allows players to generate images using Pollinations AI directly within the game environment.",
    author: "@roblox_ai_studio",
    repo: "https://github.com/snipcola/Roblox-AI",
    submissionDate: "2025-05-22",
    order: 2
  },
  {
    name: "SingodiyaTech bot",
    url: "https://t.me/singodiyatech_bot",
    description: "A Telegram bot focused on technical illustrations and diagrams generated by Pollinations AI, aimed at developers and engineers.",
    author: "@singodiya_tech",
    submissionDate: "2025-05-15",
    order: 2
  },
  {
    name: "Raftar.xyz",
    url: "https://discord.com/discovery/applications/1285597879020556308",
    description: "Raftar.xyz is an innovative social bot platform that uses Pollinations AI to create engaging and interactive experiences on Discord and Twitter, focusing on community building and automated content curation.",
    author: "@raftar_official",
    repo: "https://github.com/raftarxyz/raftar-bot",
    stars: 42,
    submissionDate: "2025-05-20",
    order: 1
  },
  {
    name: "GPT_Project",
    url: "https://t.me/gpt_project_official_bot",
    description: "GPT_Project Telegram AI Chatbot - A professional productivity tool that's always in your pocket. Utilizes Pollinations API for image generation (including Flux model) and text models (GPT-4.1, GPT-4.1-nano, SearchGPT). Features advanced language model interaction, versatile image generation, AI-powered image analysis, voice message recognition, text-to-speech, and a referral system. Designed for studying, work, and everyday AI assistance.",
    author: "@lordon4x",
    submissionDate: "2024-12-18",
    order: 1
  }
];

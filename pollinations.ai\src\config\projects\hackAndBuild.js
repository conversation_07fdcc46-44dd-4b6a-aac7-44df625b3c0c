/**
 * Hack-&-Build Projects 🛠️
 * SDKs, integration libs, extensions, dashboards, MCP servers
 */

export const hackAndBuildProjects = [
  {
    name: "MCPollinations",
    url: "https://github.com/pinkpixel-dev/MCPollinations",
    description: "A Model Context Protocol (MCP) server that enables AI assistants to generate images, text, and audio through the Pollinations APIs. Supports customizable parameters, image saving, and multiple model options.",
    author: "Pink Pixel",
    category: "sdkLibraries",
    stars: 27
  },
  {
    name: "pollinations_ai",
    url: "https://pub.dev/packages/pollinations_ai",
    description: "Dart/Flutter package for Pollinations API.",
    author: "@Meenapintu",
    category: "sdkLibraries"
  },
  {
    name: "pollinations NPM Module",
    description: "JavaScript/Node.js SDK for Pollinations API.",
    category: "sdkLibraries"
  },
  {
    name: "pypollinations",
    url: "https://pypi.org/project/pypollinations/",
    description: "Comprehensive Python wrapper for Pollinations AI API.",
    author: "@KTS-o7",
    category: "sdkLibraries"
  },
  {
    name: "@pollinations/react",
    url: "https://www.npmjs.com/package/@pollinations/react",
    description: "React hooks for easy integration of Pollinations' features.",
    author: "@pollinations",
    category: "sdkLibraries"
  },
  {
    name: "Polli API Dashboard",
    description: "Dashboard for managing/interacting with Pollinations API.",
    category: "sdkLibraries"
  },
  {
    name: "pollinations.ai Python SDK",
    url: "https://github.com/pollinations-ai/pollinations.ai",
    description: "Official Python SDK for working with Pollinations' models.",
    author: "@pollinations-ai",
    category: "sdkLibraries",
    stars: 40
  },
  {
    name: "Herramientas IA",
    url: "https://github.com/cusanotech/90-herramientas-de-inteligencia-artificial",
    description: "Tools designed with Pollinations.AI and the DescartesJS editor, including tools from other Pollinations.AI community members.",
    author: "@juanrivera126",
    repo: "https://github.com/cusanotech/90-herramientas-de-inteligencia-artificial",
    submissionDate: "2025-03-10",
    order: 3,
    stars: 26
  },
  {
    name: "🌱 Strain Navigator",
    url: "https://www.strainnavigator.com/",
    description: "A collection of tools to help Growers, Breeders & Seed Bankers. Free & Open Source powered by Pollinations.ai.",
    author: "@Tolerable",
    repo: "https://github.com/Tolerable/strainnavigator",
    stars: 1,
    submissionDate: "2025-04-15",
    order: 1
  },
  {
    name: "FoodAnaly",
    url: "https://foodanaly.vercel.app/",
    description: "An AI application for food analysis that uses advanced artificial intelligence technology to help users understand food ingredients, nutritional value, and health impacts. Provides food safety analysis, nutritional health assessment, sports and fitness analysis, visual display, alternative recommendations, and practical insights for different dietary habits.",
    author: "<EMAIL>",
    submissionDate: "2025-04-23",
    order: 1
  },
  {
    name: "imggen.top 🇨🇳",
    url: "https://www.imggen.top/",
    description: "Create stunning AI-generated images in seconds with our free AI image generator. No login required, unlimited generations, powered by FLUX model.",
    author: "<EMAIL>",
    submissionDate: "2025-04-30",
    language: "zh-CN",
    order: 4
  },
  {
    name: "CoNavic",
    url: "https://github.com/mkantwala/CoNavic/",
    description: "A free, open-source browser extension that brings the power of ChatGPT and browser automation directly to your fingertips. Instantly access AI assistance, manage tabs, and organize bookmarks using natural language all securely within your browser.",
    author: "@mkantwala",
    repo: "https://github.com/mkantwala/CoNavic/",
    stars: 6,
    submissionDate: "2025-05-01",
    order: 1
  },
  {
    name: "WordPress AI Vision Block",
    url: "https://wordpress.org/plugins/ai-vision-block/",
    description: "A custom WordPress Gutenberg block that allows you to generate images using the Pollinations API. Simply enter a prompt, and the AI will generate an image for you. Once the post is saved, the image is automatically stored in the WordPress Media Library.",
    author: "mahmood-asadi",
    repo: "https://github.com/mahmood-asadi/ai-vision-block",
    stars: 5,
    submissionDate: "2025-03-31",
    order: 1
  },
  {
    name: "DominiSigns",
    description: "Avatar Translator for Dominican Sign Language that uses artificial intelligence to translate text and audio into Dominican sign language (LSRD), creating a communication bridge for approximately 100,000 deaf people in the Dominican Republic.",
    author: "@cmunozdev",
    repo: "https://github.com/cmunozdev/DominiSigns",
    stars: 4,
    submissionDate: "2025-04-06",
    order: 1
  },
  {
    name: "Mimir AIP",
    url: "https://mimir-aip.github.io/",
    description: "An AI integration platform for developers.",
    author: "@CiaranMcAleer",
    repo: "https://github.com/Mimir-AIP/Mimir-AIP",
    stars: 11,
    submissionDate: "2025-05-05",
    order: 1
  },
  {
    name: "Herramientas IA",
    url: "https://herramientas.ia",
    description: "Tools designed with Pollinations.AI and the DescartesJS editor, including tools from other Pollinations.AI community members.",
    author: "@herramientas",
    submissionDate: "2025-05-05",
    order: 1
  },
  {
    name: "Pollinations AI Free API",
    url: "https://pollinations-ai-free-api.vercel.app/",
    description: "This project provides a free API interface supporting various text and image generation models, including OpenAI's GPT-4, Gemini 2.0, etc. Users can access these models without an API key to perform text generation, image generation, translation, text polishing, and more.",
    author: "@freeapi",
    submissionDate: "2025-05-05",
    order: 1
  },
  {
    name: "Quicker Pollinations AI",
    url: "https://getquicker.net/Sharedaction?code=9ac738ed-a4b2-4ded-933c-08dd5f710a8b&fromMyShare=true",
    description: "This project provides a free API interface supporting various text and image generation models, including OpenAI's GPT-4, Gemini 2.0, etc. Users can access these models without an API key to perform text generation, image generation, translation, text polishing, and more.",
    author: "https://linux.do/u/s_s/summary",
    submissionDate: "2025-03-10",
    language: "zh-CN",
    order: 4
  },
  {
    name: "Pollinations.AI Enhancer",
    url: "https://github.com/fisventurous/pollinationsai-enhancer",
    description: "A frontend-based AI interface designed to deliver a smooth, multimodal, and visually engaging user experience with conversational AI, image generation, and more.",
    author: "@fisven",
    repo: "https://github.com/fisventurous/pollinationsai-enhancer",
    stars: 3,
    submissionDate: "2025-04-27",
    order: 1
  },
  {
    name: "💻️ Windows Walker",
    url: "https://github.com/SuperShivam5000/windows-walker",
    description: "Windows Walker – What Copilot for Windows should have been. AI-powered Windows assistant that translates voice/text commands into real system actions using PowerShell. Powered by ChatGPT + PowerShell in an Electron UI.",
    author: "@supershivam",
    repo: "https://github.com/SuperShivam5000/windows-walker",
    stars: 10,
    submissionDate: "2025-05-22",
    order: 1
  },
  {
    name: "Pollinations MCP Server",
    url: "https://github.com/pinkpixel-dev/MCPollinations",
    description: "A Model Context Protocol server that enables AI-assisted development through natural language interaction with Pollinations' multimodal services. Mult1m0dal",
    author: "@pinkpixel-dev",
    repo: "https://github.com/pinkpixel-dev/MCPollinations",
    stars: 27,
    submissionDate: "2025-04-10"
  },
  {
    name: "🛠️ AI Content Describer",
    url: "https://github.com/cartertemm/AI-content-describer/",
    description: "An extension for NVDA, the free and open-source screen reader for Microsoft Windows. Uses multimodal generative AI to help those with blindness and visual impairments understand pictures, UI controls, complex diagrams/graphics, and more through intelligent descriptions that go far beyond simple alt-text.",
    author: "@cartertemm",
    repo: "https://github.com/cartertemm/AI-content-describer/",
    stars: 61,
    submissionDate: "2025-05-28",
    order: 1
  },
  {
    name: "tgpt",
    url: "https://github.com/aandrew-me/tgpt",
    description: "ChatGPT in terminal without requiring API keys. Uses Pollinations API endpoints to provide a free AI experience through the command line.",
    author: "@aandrew-me",
    repo: "https://github.com/aandrew-me/tgpt",
    stars: 2739,
    submissionDate: "2025-05-15",
    order: 1
  },
  {
    name: "DominiSigns",
    url: "https://www.template.net/ai-sign-generator",
    description: "A WordPress block plugin that lets users create AI-generated images through the block editor. Integrates with Pollinations API to generate images from text prompts directly within WordPress.",
    author: "@dominicva",
    repo: "https://github.com/dominicva/dominisigns",
    submissionDate: "2025-05-22",
    order: 1
  },
  {
    name: "Server Status Dashboards",
    url: "https://github.com/hverr/status-dashboard",
    description: "A monitoring tool for tracking and visualizing server performance metrics, using Pollinations API for natural language interpretation of technical data.",
    author: "@devopper",
    repo: "https://github.com/hverr/status-dashboard",
    stars: 6,
    submissionDate: "2025-05-01",
    order: 1
  },
  {
    name: "DynaSpark API",
    url: "https://th3-ai.github.io/DynaSpark",
    description: "The DynaSpark API provides simple yet powerful AI capabilities for text generation, image creation, and audio synthesis. Built on the Pollinations AI API, it includes its own Python package and detailed documentation. Lightweight, developer-friendly, and easy to integrate with fast API calls and seamless operation in both web and desktop environments.",
    author: "@Th3-C0der",
    repo: "https://github.com/Th3-AI/DynaSpark",
    submissionDate: "2025-06-12",
    order: 1
  },
  {
    name: "YankoviC",
    url: "https://github.com/Sweaterdog/YankoviC",
    description: "A programming language interpreter written in JavaScript that emulates C/C++ writing style with Weird Al themed elements. Comes with 'The Accordion' IDE that uses Pollinations for tab autocomplete and chat assistant to help people learn YankoviC.",
    author: "@Sweaterdog",
    repo: "https://github.com/Sweaterdog/YankoviC",
    stars: 1,
    submissionDate: "2025-06-27",
    order: 1
  },
  {
    name: "Querynator5000",
    url: "https://querynator5000.onrender.com/",
    description: "Modern AI-first SQL interface for exploring and manipulating databases with natural language, running entirely in the browser with local persistence.",
    author: "@SuperShivam5000",
    repo: "https://github.com/SuperShivam5000/querynator5000",
    submissionDate: "2025-07-13",
    order: 1
  }
];

# 📊 Pre-Seed / Seed Pitch Deck Cheat Sheet

**Target length:** ~10 slides · **Font:** ≥30 pt · **Time to skim:** ≤3 min

## 🗂️ Ideal Slide Order (10–12 Total)

1. **Cover**
   - Logo
   - One-line value prop
   - Presenter & contact

2. **Problem**
   - 1-sentence pain + data point
   - Who's hurting
   - Status-quo fail

3. **Market + Why Now**
   - Niche TAM/SAM
   - Growth trend
   - Timing trigger

4. **Solution / Product**
   - Screenshot or demo GIF
   - "How it works" bullets
   - Key edge

5. **Business Model**
   - Who pays
   - Pricing
   - Gross-margin hint

6. **Traction**
   - KPI chart
   - Milestones
   - Customer quote

7. **Go-to-Market**
   - Top channels
   - CAC/LTV guess
   - Rollout roadmap

8. **Team**
   - Photos
   - 1-line credentials
   - Gaps you'll hire

9. **Financials**
   - 3-yr rev curve
   - 2-3 core assumptions

10. **Competition**
    - Honest landscape
    - 1-line moat

11. **Ask & Use of Funds**
    - $$ raise
    - 3-item spend pie
    - Runway months

> Add appendix for extra data; keep core deck clean.

## ✅ Best-Practice Checklist

- **10/20/30 rule:** 10 slides, 20 min max talk track, 30-pt font
- One idea/slide • plenty of white space • big visuals over text
- Lead with traction if impressive; else stick to classic flow
- Consistent design: 2-3 brand colors, same fonts, HD images
- Deck must stand alone: titles that state takeaway ("10K users in 4 mo")
- Story arc: Problem → Opportunity → Solution → Proof → Ask

## 📈 2024-25 Investor Signals

- Team & Talent scrutinized first → spotlight founder-market fit
- Profit path > vanity growth – show unit-economics thinking early
- "Why now" trend hook boosts recall
- Concise PDFs win – average VC skim = ~2 min

## ❌ Pitfalls to Avoid

- Wall-of-text slides • tiny fonts • cluttered tables
- Overblown TAM ("We just need 1% of $100B…")
- "No competition" claim or shallow 2×2 with you perfect top-right
- Hockey-stick revenue with no assumptions
- Missing Ask/contact info
- Typos, low-res logos, inconsistent numbers across slides

## 🏁 Quick Recap

Tell a sharp story, prove early signal, show the plan, ask clearly.
Lean visuals, big fonts, honest numbers. That's the whole game.
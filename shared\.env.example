# ======================================================================
# SHARED CONFIGURATION FOR POLLINATIONS SERVICES
# ======================================================================
# This file contains shared configuration used by multiple services
# The shared utilities automatically load this file - no manual setup required

# ======================================================================
# AUTH SERVICE API CONFIGURATION
# ======================================================================

# Auth service API endpoint for token validation
# Used for validating API tokens against the auth.pollinations.ai service
AUTH_API_ENDPOINT=https://auth.pollinations.ai/api/validate-token

# Note: Shared authentication and queue utilities are now the standard implementation
# and are always used across all services.

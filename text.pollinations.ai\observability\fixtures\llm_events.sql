SELECT
    now() - rand() % 86400 as start_time,
    concat('org_', toString(1 + rand() % 5)) as `proxy_metadata.organization`,
    concat('project_', toString(1 + rand() % 3)) as `proxy_metadata.project`,
    concat('env_', toString(1 + rand() % 3)) as `proxy_metadata.environment`,
    concat('user_', toString(1 + rand() % 100)) as user,
    concat('chat_', toString(1 + rand() % 1000)) as `proxy_metadata.chat_id`,
    concat('msg_', toString(1 + rand() % 10000)) as message_id,
    ['gpt-4', 'gpt-3.5-turbo'][1 + rand() % 2] as model,
    50 + rand() % 500 as `response.usage.prompt_tokens`,
    100 + rand() % 1000 as `response.usage.completion_tokens`,
    150 + rand() % 1500 as `response.usage.total_tokens`,
    rand() / 10 as standard_logging_object_response_time,
    rand() as duration,
    round(rand() / 100, 3) as cost,
    '' as exception,
    '' as traceback,
    ['success', 'error'][1 + rand() % 2] as standard_logging_object_status,
    [] as messages,
    [] as `response.choices`,
    '{}' as proxy_metadata,
    ['openai', 'anthropic'][1 + rand() % 2] as provider,
    rand() * 1000 as llm_api_duration_ms,
    now() as end_time,
    concat('id_', toString(rand() % 10000)) as id,
    rand() % 2 = 1 as stream,
    ['chat', 'completion'][1 + rand() % 2] as call_type,
    concat('sk-', lower(hex(randomString(8)))) as api_key,
    'llm' as log_event_type,
    rand() % 2 = 1 as cache_hit,
    '{"content": "sample response"}' as response,
    concat('res_', toString(rand() % 10000)) as `response.id`,
    'chat.completion' as `response.object`
FROM numbers(10)
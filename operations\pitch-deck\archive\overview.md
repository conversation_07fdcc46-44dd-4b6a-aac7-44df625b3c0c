# pollinations.ai **Overview (Q2 2025)**

## **1\. Company**

**Name:** pollinations.ai  
**Legal Entity:** Pollinations.AI OÜ (Estonia)  
**HQ & Ops:** Berlin, Germany

**Mission Labs:** Democratise generative AI: make state‑of‑the‑art creation tools free and open so anyone can build, share and monetise AI‑powered experiences.

**Vision:** Become the **"Roblox for AI"** – a global, open platform where developers and non‑coders alike can easily create, deploy and monetise AI applications.

## **2\. Product & Platform**

Pollinations is a "no‑keys, no‑friction" creation layer on top of cutting‑edge AI models. We give makers three things:

* **Instant API Endpoints** – copy‑paste a URL to generate text, images, audio (video next in line).  No log‑in required for the first call.  
* **AI-Integrated SDKs & Starter Kits** – use your preferred AI assistant to launch projects across multiple platforms with community-supported templates. Your AI assistant handles scaffolding, hosting setup, and authentication without leaving your workspace.  
* **Edge AI at Scale** – every request transparently taps our global GPU fleet; the developer never touches infrastructure.

#### **Monetisation Plugins – How Money Will Flow**

* **Contextual Ads** – our LLM slips a short, relevant ad sentence or branded image into generated content.  (Think "Sponsored: try XYZ – the eco‑friendly bike" appended to a travel paragraph.)  
  * **Micro‑Purchases** (media context dependant) – small, in‑content upsells via affiliates ("Generate in 4K for $0.05" or "Unlock 5 extra portraits for $1").  
* **Premium Features** – monthly subscription \+ usage‑based tiers for speed or private models.  
* **App Shared Revenue Model** – (Roblox-alike creator contract)  
* **In App Purchases** – (hosted ?)  
* **Monetize User Data (anonymized)**   

Pollinations handles ad sourcing, billing and payouts automatically.

#### **Phase‑1 Proof‑of‑Concept**

* **Ads only for *unregistered* apps** – if a call comes without a valid Pollinations token, we append a text‑only ad line to the response. Registered apps remain ad‑free during the experiment. **(live today)**  
* **Authentication** – during onboarding through AI assistants, we issue a token and store the app domain → token mapping.  
* **Edge Services** – image, text and audio generation already run at scale; React SDK (open‑source) wraps the calls. See repo: [https://github.com/pollinations/pollinations](https://github.com/pollinations/pollinations). **(live today)**  
* **App Shared Revenue Model** – Roblox app RP Character 1 **(live today)**

*Other components are architected to integrate into the broader platform but are being **held back** in this phase while we validate ad economics with minimal overhead.*

### 

## **3\. Traction & Market Presence**

| Metric | Apr 2025 | Growth |
| :---- | :---- | :---- |
| Monthly Active End‑Users | **3 M** | ~~\+40 % MoM~~ |
| Media Generated / mo | **100 M** | ~~\+40 % MoM~~ |
| Known Third‑party Integrations | **150+** | ~~–~~ |
| Discord Community | **13 k** members | ~~\+25 % QoQ~~ |
| GitHub Stars (core repos) | **1700+** | ~~\+30 % QoQ~~ |

**Flagship integration:** “AI Character RP” on Roblox – 14M+ plays, thousands of concurrents; first live ad‑revenue pilot.

**Key markets:** United States (13%), India (6%), China (30%), Europe (13%)

## **4\. Monetisation**

Pollinations turns **creative traffic** into a revenue engine in three expanding layers:

| Phase | Launch Date | End‑User Experience | Developer Experience | Pollinations Revenue |
| :---- | :---: | :---- | :---- | :---- |
| **Infrastructure MVP** | **Q3 2025** | Contextual text ads for un‑registered apps traffic | GitHub‑based Auth MCP issues token; Dev MCP tools available; usage meteredPremium access for registered apps (free) | Limited external revenue, testing ad‑revenue mechanics;  Focus on stability & data; Forster community |
| **Monetisation v1** | **Q1 2026** | Contextual text ads embedded in media | Text ads Payment plugin (automatic 50 % revenue‑share via payouts), Premium access (pay), Pollinations Hosting | Premium & Ad CPM (50 % shared with creators) |
| **Monetisation v2** | **Q2 2026** | Image, audio ads embedded in generated media | Plugin auto‑upgrades; richer ad formats; higher ARPU | Higher eCPM; 50 % shared with creators |

Projected run‑rate:

| Timeline | Ad‑served Media | CPM | Gross Rev | Pollinations Share (50 %) |
| :---- | :---- | :---- | :---- | :---- |
| 12 mo | 250 M | $1.00 | $250 k / mo | $250 k / mo |
| 24 mo | 1.6 B | $1.00 | $8 M / mo | $4 M / mo |

Why this works:

* **No‑friction funnel** fills the top of the pipe.  
* **Plugin store** lets us monetise without touching user code.  
* **Premium tiers** capture high‑value workloads while keeping the free tier generous.

## 

## **5\. Founding**

* **Thomas Haferlach** – CTO. 8 y Gen‑AI, ex‑Amazon, raised €1.2 M EU grant. Full‑time since Aug 2024.  
* **Elliot Fouchy** – COO/Head of Product Ops. 20 y tech, 6 y AI infra, joined Jan 2025.

Founders have 10 + y working together on creative tech; Pollinations grew organically from Thomas’s 2023 "generative‑URL" side project.

## **6\. Roadmap 12 months**

| Launch | Key Milestone |
| :---- | :---- |
| **Q3 2025** | **Core Infrastructure** Per‑app database (token, usage, domain mapping) \- GitHub‑based authentication (Auth MCP \+ Actions) \- Pollinations Dev MCP tooling server **Ad Monetisation Validation Community** |
| **Q1 2026** | **Hosting** Dev‑app hosting via MCP \+ GitHub Pages **Monetisation Plugin v1** (text ads, payments, premium tier) 50 % revenue‑share activated**Community** |
| **Q2 2026** | **Monetisation Plugin v2** (image, audio ads)**Community** |

### 

## **7\. Funding**

Seeking **$2.5M – $3 M seed** to scale infra, grow team, and accelerate expansion.

* **Category tail‑winds:** Gen‑AI usage still compounding; ad spend moving into immersive formats.  
* **Lowest friction on the market:** zero‑key APIs \+ one‑command deploy is unmatched.  
* **Network‑effect moat:** every new app ↔ more training data ↔ higher ad relevance ↔ better payouts.  
* **Clear monetisation path** with rev‑share already piloted.  
* **Experienced, execution‑focused founding team.**

**Pollinations.AI is the fastest on‑ramp from idea to monetised AI product.**


/**
 * Vibe Coding Projects ✨
 * No-code / describe-to-code playgrounds and builders
 */

export const vibeCodingProjects = [
  {
    name: "PairFusion",
    url: "https://pair-fusion.vercel.app/",
    description: "A real-time AI powered, scalable and feature-rich collaborative IDE built for modern development teams. Experience seamless pair programming, instant feedback, and a professional-grade toolset designed for maximum productivity.",
    author: "@auraticabhi",
    repo: "https://github.com/auraticabhi/PairFusion",
    stars: 0,
    submissionDate: "2025-07-03",
    order: 1
  },
  {
    name: "Craft<PERSON>",
    url: "https://craftui.studio/",
    description: "An AI-powered tool that simplifies UI design by turning text prompts or images into clean, production-ready components. It supports real-time customization with theme switching, framework selection (like Tailwind or Bootstrap), and intuitive editing. Whether you're a developer or designer, CraftUI helps you design faster, smarter, and with ease. Explore, remix, and share your creations in a growing creative community.",
    author: "@imhardikdesai",
    submissionDate: "2025-06-19",
    order: 1
  },
  {
    name: "AI Code Generator",
    url: "https://codegen.on.websim.com/",
    description: "A websim project that generates code from description, selected programming language and other options. Integrates Pollinations because it allows for more models to choose from for potentially better results. It has modes like: Code Generator, Code Explainer, Reviewer, etc.",
    author: "@Miencraft2",
    submissionDate: "2025-05-25",
    order: 1
  },
  {
    name: "VibeCoder",
    description: "A conversational coding environment that lets you create applications by describing them in natural language.",
    author: "@Aashir__Shaikh",
    authorUrl: "https://x.com/Aashir__Shaikh",
    submissionDate: "2025-03-25",
    order: 1
  },
  {
    name: "Pollinations Task Master",
    url: "https://github.com/LousyBook94/pollinations-task-master",
    description: "A task management system that uses AI to help break down and organize development tasks through natural language interaction.",
    author: "@LousyBook94",
    repo: "https://github.com/LousyBook94/pollinations-task-master",
    submissionDate: "2025-05-12",
    stars: 13,
    order: 1
  },
  {
    name: "Qwen-Agent",
    url: "https://github.com/QwenLM/Qwen-Agent",
    description: "A framework for developing agentic LLM applications.",
    repo: "https://github.com/QwenLM/Qwen-Agent",
    stars: 10648,
    order: 1
  },
  {
    name: "JCode Website Builder",
    url: "https://jcode-ai-website-bulder.netlify.app/",
    description: "A website generator using Pollinations text API.",
    author: "@rtxpower",
    order: 1
  },
  {
    name: "Pollinations.DIY",
    url: "https://pollinations.diy",
    description: "A browser-based coding environment based on bolt.diy, featuring integrated Pollinations AI services, visual code editing, and project management tools.",
    author: "@thomash",
    submissionDate: "2025-03-01",
    order: 1
  },
  {
    name: "Websim",
    url: "https://websim.ai/c/bXsmNE96e3op5rtUS",
    description: "A web simulation tool that integrates Pollinations.ai.",
    author: "@thomash",
    order: 2
  },
  {
    name: "NetSim",
    url: "https://netsim.us.to/",
    description: "websim.ai clone that's actually good",
    author: "@kennet678",
    submissionDate: "2025-04-15",
    order: 1
  },
  {
    name: "Pollin-Coder",
    url: "https://pollin-coder.megavault.in",
    description: "A free AI-powered website builder that lets anyone create a clean site just by describing it. It uses Pollinations AI to generate the content and layout instantly.",
    author: "@r3ap3redit",
    submissionDate: "2025-05-19",
    order: 1
  },
  {
    name: "JustBuildThings",
    url: "https://justbuildthings.com",
    description: "A natural language programming interface that lets users create web applications by simply describing what they want to build, using Pollinations AI to generate code and assets.",
    author: "@buildmaster",
    submissionDate: "2025-05-02",
    order: 1
  },
  {
    name: "SocialScribe",
    url: "https://socialscribe.pages.dev/",
    description: "An AI-powered Chrome extension that fixes grammar, rewrites text, and enhances your writing across websites like Twitter, LinkedIn, and Gmail, with customizable tone, length, and platform-specific formatting, plus support for emojis, hashtags, and keyword suggestions.",
    author: "@sh20raj",
    repo: "https://github.com/sh20raj/socialscribe",
    stars: 1,
    submissionDate: "2025-07-13",
    order: 1
  },
  {
    name: "Berrry Computer",
    url: "https://berrry.app",
    description: "Make tiny self-contained web apps with AI. Find a tweet with an interesting app idea, mention @BerrryComputer in a reply, and get back an app on a unique subdomain. Uses pollinations.ai to create dynamic AI experiences in generated apps.",
    author: "@vgrichina",
    authorUrl: "https://github.com/vgrichina",
    repo: "https://github.com/Strawberry-Computer",
    submissionDate: "2025-07-16",
    order: 0
  },
  {
    name: "websim-pollinations-ai",
    url: "https://websim.pollinations.ai",
    description: "A lightweight websim for creating web simulations with AI. Usage: https://websim.pollinations.ai/[prompt] - Simply append your prompt to the URL to generate interactive web experiences.",
    author: "@thomash",
    repo: "https://github.com/pollinations/pollinations/tree/master/websim.pollinations.ai",
    submissionDate: "2025-08-03",
    order: 0.5
  }
];

<div style="text-align: right; position: absolute; top: 0; right: 0;">
<a href="/1">⬅️ Back to Index</a>
</div>

# 🔍 Due Diligence

🤔 **Problem Statement What pain/problem are you solving?**

**For Creators** (Indie/Vibe coders)
	•	Generative-AI apps are exploding, yet monetization lags.
	•	“Vibe-coders” lack ad tech that feels native to AI-chat and image flows.
	•	Paywalled APIs plus DevOps complexity block experimentation and scale.
	•	No clear ladder from free tinkering to sustainable income.

**For Ad Providers** (Brands, Ad Networks)
	•	Fragmented supply of AI-native inventory; no standard placements.
	•	Existing display formats (banners, interstitials) feel clunky in chat & image contexts.
	•	Zero-scale, high-friction buying process leads to missed budget allocation.

💡 **Solution : How are you solving it?**

**pollinations.ai** provides the ad-funded backend for generative AI.
• Single SDK Call — returns free, cloud-scaled media and a contextual ad slot.
• Automated Tiering — when an app's Ad € ≥ Cloud € the cap lifts → Flower (unlimited use).
• Path to Income — Nectar tier (2026) shares 50 % of net ad revenue with top apps.
• Zero-UI / Zero-Ops CLI (pollinations-init) scaffolds repo, CI/CD & deploy in one command.
• For Brands — we aggregate AI-native inventory, partnering with contextual-ad specialists for scale.

🎯 **Customer Persona – Who is the recipient of your solution?**

• **Creators:** indie/vibe-coders, hobbyists, solo devs & small studios seeking zero-ops AI and a built-in monetization ladder.
• **Contextual Ad Agencies:** brands & networks hungry for fresh, contextual inventory in AI experiences.

👥 **Team**

Unique Qualifications
	•	Founders Thomas Haferlach (CEO) & Elliot Fouchy (COO) previously scaled AI infra to 100 M+ media req/mo.
	•	Road-tested across full AI product lifecycle (R&D → prod).
	•	Key 2025-H2 hires secured/under way:
	•	MLOps / Infra Engineer
	•	Community & Creator Success (Kalam – incoming)

Operating Model : Lean 4-person core, Berlin HQ with remote contributors.

🚀 **Product / Business**

**Product Offering**
	•	Ad-funded backend platform with 3-tier ladder Seed → Flower → Nectar.
	•	SDK = free media + ad slot; tiering driven by Ad € / Cloud € ratio.
	•	Status: Seed & Flower live (Q2 2025).
	•	Current Traction: ~3 M MAU • 100 M+ media/mo • 300+ live apps • 13 k Discord • Roblox integration (1.8 M MAU).

**Value Proposition**

| Stakeholder | Pain | Value we deliver |
|-------------|------|------------------|
| **Creators** | • Paywalls & complex APIs<br>• Infrastructure operations<br>• No clear monetization path | • Free, scalable AI generation<br>• Zero-ops deployment<br>• Automated tier scaling<br>• Path to 50/50 revenue share |
| **Ad Providers** | • Fragmented AI inventory<br>• Lack of contextual relevance<br>• No scalable buying process | • Aggregated AI-native inventory<br>• Brand-safe placements<br>• Contextual targeting<br>• Scalable buying process |
| **End-Users** | • Pay-to-play barriers<br>• Limited access to AI tools | • Free access to innovative AI apps<br>• Rich, engaging experiences<br>• No usage restrictions |

**Unique Selling Proposition & Moat**
	•	Native monetization baked into every media output.
	•	Flywheel: more apps → more users → more Ad € → covers GPUs → funds free tier.
	•	Zero-Ops developer experience hard to clone.
	•	Growing proprietary contextual-ad performance data.

**Technology Enablers**
	•	“Zero-UI” CLI + Code MCP automates repo, CI/CD, SSL.
	•	Dynamic tiering engine measuring Ad € vs Cloud € in real time.
	•	Usage DB, billing & rev-share ledger.

**Go-to-Market Strategy**
	•	Hook: free Seed API, zero-ops CLI.
	•	Lift: Leaderboard visibility drives Flower adoption.
	•	Monetize: Revenue-share upsell to Nectar (50 / 50).
	•	Channels: Discord (13 k), GitHub stars, hackathons, partner hack-days.
	•	Forge contextual-ad partnerships (Garlic, Nexad).

**Principal Risks & Mitigation**

| Category | Challenge | Mitigation |
|----------|-----------|------------|
| Market | Low eCPM / fill rate | Multi-format Ad SDK v2, advanced mediation |
| Technical | Ad € / Cloud € mis-tracking | Redundant telemetry & audits |
| Operational | Brand safety & fraud | Partner filters, policy, human review |
| Financial | GPU cost spikes | Reserved capacity, model efficiency |
| Competitive | Higher rev-share offers | Emphasize free AI + zero-ops + unique inventory |
| Regulatory | Privacy / ads rules | Privacy-by-design, contextual ads, legal counsel |

💰 **Monetization Strategy & Unit Economics**

Monetization Model

| Tier | Creator Cost | Pollinations Take | Status |
|------|--------------|-------------------|---------|
| Seed | Rate-limited | 100% Ad Rev funds compute | Live |
| Flower | Unlimited (Ad € ≥ Cloud €) | 100% net beyond compute | Beta, GA H2 2025 |
| Nectar | Free | 50% of net Ad Rev shared | GA 2026 |

**Unit Economics (EOY 2027)**
	•	Compute: €0.0002 / media
	•	Target gross eCPM: €7 → Net €5.95 / 1 k media (after 15 % network fee)
	•	Per-app contribution: Nectar €216/mo (post-split)
	•	Platform gross margin target ≈ 60 %.

🌍 **Market**

**Market Size** (2025)

| Metric | Value | Note |
|--------|-------|------|
| TAM | $218B | Mobile- & web-in-app ad spend (ex-walled gardens) |
| SAM | $20B | Spend already flowing via plug-in monetization SDKs |
| SOM | $768M | 8 B imps/mo × $8 eCPM = 3.8% of SAM |

Key Trends: rise of vibe-coders, privacy shift to contextual, Gen-AI ubiquity.

🥊 **Competition**

Competitive Landscape & Differentiation
	•	**Unity / AppLovin** — Best-in-class mobile ad networks (70% rev-share); lacks AI backend.
	•	**Perplexity / Character.AI** — Gen-AI apps with weak monetization (≤25% publisher share).
	•	**Hugging Face** — Model hub, Spaces SDK; creators keep up to 90% on paid spaces.
	•	**GIPHY / Unsplash** — Media APIs with branded/sponsored content; no creator payouts.
	•	**pollinations.ai** — "Unity Ads for Generative AI" with unique free tier (Ad € ≥ Cloud €) and 50/50 revenue share by 2026.

Key Differentiator: Default free tier with ad-funded automatic tiering + integrated path to revenue that closes the creator monetization gap.

📊 **Financial / Traction**
	•	Ecosystem: ~3 M MAU • 100 M+ media/mo • 300 + apps.
	•	Community: 13 k Discord members.
	•	Revenue: pre-monetization (Flower GA H2 2025).
	•	Fundraise: seeking €3 M Seed → runway to Nectar GA.

⚖️ **Legal**
	•	IP to be owned by Pollinations AI OÜ (Estonia); legacy German GmbH dissolved.
	•	No founder convictions or investigations.
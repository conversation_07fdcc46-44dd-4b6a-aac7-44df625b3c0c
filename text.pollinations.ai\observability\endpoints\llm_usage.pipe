TOKEN "read_pipes" READ

NODE endpoint
SQL >
    %
    {% if defined(column_name) and column_name not in ['model', 'provider', 'organization', 'project', 'environment', 'user'] %}
        {{ error('column_name (String) query param must be one of the following: model, provider, organization, project, environment, user') }}
    {% end %}
    SELECT
        toDate(timestamp) as date,
        {% if defined(column_name) %}
            toString({{column(column_name, 'model')}}) as category,
        {% end %}
        count() as total_requests,
        countIf(exception != '') as total_errors,
        sum(total_tokens) as total_tokens,
        sum(completion_tokens) as total_completion_tokens,
        sum(prompt_tokens) as total_prompt_tokens,
        sum(cost) as total_cost,
        avg(duration) as avg_duration,
        avg(response_time) as avg_response_time
    FROM llm_events
    WHERE 1
        {% if defined(organization) and organization != [''] %}
        AND organization IN {{Array(organization)}}
        {% end %}
        {% if defined(project) and project != [''] %}
        AND project IN {{Array(project)}}
        {% end %}
        {% if defined(environment) and environment != [''] %}
        AND environment IN {{Array(environment)}}
        {% end %}
        {% if defined(provider) and provider != [''] %}
        AND provider IN {{Array(provider)}}
        {% end %}
        {% if defined(user) and user != [''] %}
        AND user IN {{Array(user)}}
        {% end %}
        {% if defined(model) and model != [''] %}
        AND model IN {{Array(model)}}
        {% end %}
        {% if defined(start_date) %}
        AND timestamp >= {{DateTime(start_date)}}
        {% else %}
        AND timestamp >= now() - interval 7 day
        {% end %}
        {% if defined(end_date) %}
        AND timestamp < {{DateTime(end_date)}}
        {% else %}
        AND timestamp < now()
        {% end %}
    GROUP BY 
    {% if defined(column_name) %}
    category,
    {% end %} 
    date
    order by total_cost desc

TYPE endpoint

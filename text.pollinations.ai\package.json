{"name": "text.pollinations.ai", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "NODE_ENV=test ava --verbose test/**/*.test.js", "test:unit": "NODE_ENV=test ava --verbose test/*.test.js", "test:file": "NODE_ENV=test ava", "test:file:verbose": "DEBUG=pollinations:* NODE_ENV=test ava --verbose", "test:pattern": "DEBUG=pollinations:* NODE_ENV=test ava --verbose", "coverage": "NODE_ENV=test c8 --reporter=text --reporter=html ava --verbose test/server.test.js test/generateText.test.js", "start": "node startServer.js", "debug": "clear &&DEBUG=* nodemon startServer.js"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"@azure/identity": "^4.4.1", "@huggingface/inference": "^2.8.1", "await-sleep": "^0.0.1", "axios": "^1.7.7", "commander": "^13.1.0", "cors": "^2.8.5", "debug": "^4.4.1", "dotenv": "^16.0.3", "eventsource": "^3.0.2", "eventsource-parser": "^3.0.2", "express": "^4.20.0", "express-rate-limit": "^7.5.0", "jsonwebtoken": "^9.0.2", "node-fetch": "^3.3.2", "openai": "^4.58.2", "p-queue": "^8.0.1", "puppeteer": "^23.6.0", "turndown": "^7.2.0"}, "devDependencies": {"@istanbuljs/nyc-config-babel": "^3.0.0", "ava": "^6.2.0", "c8": "^10.1.3", "nodemon": "^3.1.10", "nyc": "^17.1.0", "sinon": "^20.0.0", "supertest": "^7.0.0"}}
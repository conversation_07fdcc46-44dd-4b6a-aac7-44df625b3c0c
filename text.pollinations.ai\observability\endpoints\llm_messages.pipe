TOKEN "read_pipes" READ

DESCRIPTION >
    Get detailed LLM messages with filters

NODE llm_messages_node
SQL >
    %
    {% if defined(embedding) %}
        with cosineDistance(embedding, {{ Array(embedding, 'Float32') }}) as _similarity
    {% end %}
    SELECT
        timestamp,
        organization,
        project,
        environment,
        user,
        chat_id,
        message_id,
        model,
        provider,
        prompt_tokens,
        completion_tokens,
        total_tokens,
        duration,
        cost,
        response_status,
        exception
        {% if defined(embedding) %}
            , 1 - _similarity as similarity
        {% else %}
            , 0 as similarity
        {% end %}
        {% if defined(chat_id) %}
            , messages, response_choices
        {% else %}
            , [] as messages, [] as response_choices
        {% end %}
    FROM (select * from llm_events where organization != 'your-org')
    WHERE 1
        {% if defined(organization) and organization != [''] %}
        AND organization IN {{Array(organization)}}
        {% end %}
        {% if defined(project) and project != [''] %}
        AND project IN {{Array(project)}}
        {% end %}
        {% if defined(environment) and environment != [''] %}
        AND environment IN {{Array(environment)}}
        {% end %}
        {% if defined(user) and user != [''] %}
        AND user IN {{Array(user)}}
        {% end %}
        {% if defined(model) and model != [''] %}
        AND model IN {{Array(model)}}
        {% end %}
        {% if defined(provider) and provider != [''] %}
        AND provider IN {{Array(provider)}}
        {% end %}
        {% if defined(chat_id) and chat_id != [''] %}
        AND chat_id IN {{Array(chat_id)}}
        {% end %}
        {% if defined(message_id) and message_id != [''] %}
        AND message_id IN {{Array(message_id)}}
        {% end %}
        {% if defined(start_date) %}
        AND timestamp >= {{DateTime(start_date)}}
        {% end %}
        {% if defined(end_date) %}
        AND timestamp < {{DateTime(end_date)}}
        {% end %}
        {% if defined(embedding) %}
        AND length(embedding) > 0 and _similarity <= {{ Float64(similarity_threshold, 0.5) }}
        and response_status != 'error'
        {% end %}
    {% if defined(embedding) %}
      ORDER BY similarity DESC
    {% else %}
        ORDER BY timestamp DESC
    {% end %}
    LIMIT {{Int32(limit, 200)}}

TYPE endpoint

---
## class: scroll
---

<div style="text-align: right; position: absolute; top: 0; right: 0;">
<a href="/1">⬅️ Back to Index</a>
</div>

# 💹 **Financial Model – Scaling FY25 → FY31**

<div class="bg-red-100 p-1 pl-6 pr-6 rounded-lg border-l-4 border-red-500 mb-6">
  <p class="text-red-800">Run‑rate milestones after GPU cost <em>and</em> weighted creator payouts: <strong><em>€5.4 M net ARR (FY26)</em></strong> → <strong><em>€29.2 M (FY27)</em></strong> → <strong><em>€120.9 M (FY28)</em></strong> → <strong><em>€432 M (FY31)</em></strong>.</p>
</div>

## **1. Core Input Levers**
#
| Lever                       |    FY25 |    FY26 |    FY27 |    FY28 |    FY31 |
| --------------------------- | ------: | ------: | ------: | ------: | ------: |
| Gen‑AI apps live            |   1 000 |   5 000 |  20 000 |  60 000 | 200 000 |
| Media per app / month       | 100 000 | 125 000 | 150 000 | 200 000 | 200 000 |
| Ad impressions / app / mo   |  20 000 |  25 000 |  30 000 |  40 000 |  40 000 |
| eCPM (€)                    |       5 |       6 |       7 |       8 |       8 |
| **Weighted creator share**¹ |     0 % |    20 % |    30 % |    40 % |    40 % |
| GPU cost / media (€)        |  0.0004 |  0.0003 |  0.0002 |  0.0001 |  0.0001 |

<sub>¹ Creators who opt‑in receive 50 % of their net ad revenue. Weighted % rises as more “super‑trend” apps adopt rev‑share.</sub>

## **2. Yearly P\&L (after compute & creator payouts)**
#
| Year | Gross ARR (€M) | GPU Cost (€M) | Creator Payout (€M) | **Net ARR to Pollinations (€M)** | Margin vs Gross |
| ---- | -------------: | ------------: | ------------------: | -------------------------------: | --------------: |
| 2025 |           1.20 |          0.48 |                0.00 |                         **0.72** |            60 % |
| 2026 |           9.00 |          2.25 |                1.35 |                         **5.40** |            60 % |
| 2027 |          48.96 |          7.20 |               12.53 |                        **29.23** |            60 % |
| 2028 |         216.00 |         14.40 |               80.64 |                       **120.96** |            56 % |
| 2031 |         768.00 |         48.00 |              288.00 |                       **432.00** |            56 % |

## **3. Per‑App Contribution (FY27 example)**

| Metric                                                 |    Value |
| ------------------------------------------------------ | -------: |
| Ad impressions / app / month                           |     30 k |
| Gross ad €                                             |     €504 |
| GPU compute cost                                       |     -€72 |
| Net profit after compute                               |     €432 |
| App revenue share (50%)                                |     €216  |
| Net to Pollinations (50%)                              |  **€216** |

## **4. Unit‑Economics Sensitivity**
#

* **Every +€1 in eCPM** ⇒ +€4.2 M to FY27 Net ARR.
* **Every +10 k apps in FY31** ⇒ +€21.6 M run‑rate.
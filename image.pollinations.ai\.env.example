# ======================================================================
# IMAGE.POLLINATIONS.AI CONFIGURATION
# ======================================================================
# This file contains configuration specific to the image.pollinations.ai service

# ======================================================================
# SHARED CONFIGURATION - NOW IN /shared/.env
# ======================================================================
# The following variables are now handled by the shared utilities:
# - Queue management is now handled by shared/ipQueue.js
# - Authentication is now handled by shared/auth-utils.js

# ======================================================================
# SERVER CONFIGURATION
# ======================================================================

# Port for the image generation service
PORT=16384

# ======================================================================
# ANALYTICS CONFIGURATION
# ======================================================================

# Google Analytics Configuration
# Used for tracking API usage and performance metrics
GA_MEASUREMENT_ID=G-XXXXXXXXXX  # Google Analytics 4 Measurement ID
GA_API_SECRET=XXXXXXXXXXXXX     # Google Analytics API Secret for server-side tracking

# ======================================================================
# EXTERNAL SERVICE CONFIGURATIONS
# ======================================================================

# External Service API Keys
# Optional: API key for the Meoow 2 service
# MEOOW_2_API_KEY=sk-XXXXXXXXXXXXX

# Cloudflare Configuration
# Required for image processing through Cloudflare Workers
CLOUDFLARE_ACCOUNT_ID=XXXXXXXXXXXXX  # Your Cloudflare account ID
CLOUDFLARE_API_TOKEN=XXXXXXXXXXXXX   # Cloudflare API token with Workers permission

# ======================================================================
# CONTENT MODERATION
# ======================================================================

# Bad Domains Configuration
# Comma-separated list of domains that should have prompts transformed to their opposites
# Example: scam-site.com,malicious-site.com
BAD_DOMAINS=
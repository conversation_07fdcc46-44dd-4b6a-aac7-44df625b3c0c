name = "pollinations-text-cache"
main = "src/index.js"
compatibility_date = "2023-05-18"

# R2 bucket for text storage
[[r2_buckets]]
binding = "TEXT_BUCKET"
bucket_name = "pollinations-text"

# Environment variables
[vars]
ORIGIN_HOST = "text-origin.pollinations.ai"
#ORIGIN_HOST ="http://**************:16385"
# Enable debug logging (set to "true" to enable, "false" to disable)
DEBUG = "cache:*"

# Increase limits for long-running operations
# This requires a paid Workers Unbound plan
[limits]
cpu_ms = 30000  # 30 seconds CPU time (max for Workers Unbound)

# Configure logging
[observability]
enabled = true      # Enable logs for local development

[observability.logs]
invocation_logs = true

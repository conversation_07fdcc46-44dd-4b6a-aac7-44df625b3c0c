{"name": "generative_image_url", "version": "1.0.0", "description": "## Architecture", "main": "index.js", "type": "module", "scripts": {"test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "start": "tsx src/index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@azure-rest/ai-content-safety": "^1.0.1", "@azure/core-auth": "^1.9.0", "@azure/identity": "^4.10.0", "@google-cloud/translate": "^7.2.1", "@gradio/client": "^0.19.4", "@types/debug": "^4.1.12", "@vitalets/google-translate-api": "^9.2.0", "ansi-colors": "^4.1.3", "async-lock": "^1.4.0", "await-sleep": "^0.0.1", "bayes": "^1.0.0", "cld": "^2.9.1", "cld3-asm": "^3.1.1", "cli-progress": "^3.12.0", "cli-table3": "^0.6.3", "debug": "^4.3.4", "dotenv": "^16.3.1", "eventsource": "^2.0.2", "exiftool-vendored": "^28.2.1", "fastify": "^4.17.0", "file-type": "^19.4.0", "form-data": "^4.0.0", "install": "^0.13.0", "jimp": "^0.16.2", "lodash.memoize": "^4.1.2", "memoize-fs": "^2.2.0", "mjpeg-server": "^0.3.1", "node-stream-zip": "^1.15.0", "npm": "^9.7.1", "p-queue": "^7.3.4", "papaparse": "^5.4.1", "ramda": "^0.29.1", "sharp": "^0.33.5", "tempfile": "^5.0.0", "tinyld": "^1.3.4", "urldecode": "^1.0.1", "zod": "^4.0.5"}, "devDependencies": {"@fast-check/vitest": "^0.2.2", "@types/async-lock": "^1.4.2", "@types/cli-progress": "^3.11.6", "@types/express": "^5.0.3", "@types/node": "^24.0.14", "esbuild": "^0.15.18", "express": "^4.21.2", "fast-check": "^4.2.0", "supertest": "^6.3.3", "tsx": "^4.20.3", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "directories": {"doc": "docs", "example": "examples", "test": "test"}}
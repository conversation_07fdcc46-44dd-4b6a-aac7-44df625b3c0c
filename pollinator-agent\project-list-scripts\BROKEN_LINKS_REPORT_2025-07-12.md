# 🔗 Broken Links Report - July 12, 2025

**Generated:** 2025-07-12T05:30:46.657Z  
**Total URLs Checked:** 203  
**Broken Links Found:** 30  
**Success Rate:** 85.2%

## 📊 Summary by Category

| Category | Broken Links | Total Projects |
|----------|-------------|----------------|
| Creative | 3 | - |
| Games | 5 | - |
| Hack & Build | 3 | - |
| Chat | 5 | - |
| Social Bots | 9 | - |
| Learn | 2 | - |

## ❌ Broken Links by Status Code

### 404 Not Found (18 links)
- **Elixpo-Art** (creative) - Repository: https://github.com/elixpo/art-platform
- **Minecraft AI (Python)** (games) - Main URL: https://github.com/pollinations/minecraft-ai-python
- **Minecraft AI (Node.js)** (games) - Main URL: https://github.com/pollinations/minecraft-ai-node
- **Sirius Cybernetics Elevator Challenge** (games) - Main URL: https://github.com/sirius-cybernetics/elevator-challenge
- **<PERSON><PERSON> de Memorizar con Pollinations** (games) - Main URL: https://memorizar-pollinations.vercel.app/
- **Infinite World – AI Game** (games) - Main URL: https://infinite-world-game.vercel.app/
- **Pollinations AI Free API** (hackAndBuild) - Main URL: https://pollinations-ai-free-api.vercel.app/
- **DominiSigns** (hackAndBuild) - Repository: https://github.com/dominicva/dominisigns
- **Pollinations MCP Server** (hackAndBuild) - Main URL: https://github.com/pollinations/model-context-protocol-server
- **OkeyMeta** (chat) - Repository: https://github.com/okeymeta/okeymeta
- **Pollinations AI Playground** (chat) - Main URL: https://pollinations-ai-playground.vercel.app/
- **Free AI Chatbot & Image Generator** (chat) - Repository: https://github.com/vercel/ai/tree/main/examples/ai-image-generator
- **UR Imagine & Chat AI** (chat) - Main URL: https://urimagine.netlify.app/
- **Pollinations Discord Bot** (socialBots) - Main URL: https://github.com/pollinations/discord-bot
- **Raftar.xyz** (socialBots) - Repository: https://github.com/raftarxyz/raftar-bot
- **AI Image Generator [ROBLOX]** (socialBots) - Main URL: https://www.roblox.com/games/ai-image-generator
- **Connect Pollinations with Open Web UI tutorial** (learn) - Main URL: https://github.com/pollinations/connect-with-open-webui

### 403 Forbidden (2 links)
- **Image Gen - Uncensored Edition** (creative/chat) - Main URL: https://huggingface.co/chat/assistant/66fccce0c0fafc94ab557ef2

### 502 Bad Gateway (3 links)
- **GPT_Project** (socialBots) - Main URL: https://t.me/gpt_project_official_bot
- **pollinations-tg-bot 🇨🇳** (socialBots) - Main URL: https://t.me/pollinations_cn_bot
- **SingodiyaTech bot** (socialBots) - Main URL: https://t.me/singodiyatech_bot

### 429 Too Many Requests (1 link)
- **OkeyAI** (learn) - Main URL: https://chat.okeymeta.com.ng

### Connection Errors (4 links)
- **Text2Image_audio 🇨🇳** (creative) - Main URL: nihilistic.dpdns.org (Failed to parse URL)
- **Herramientas IA** (hackAndBuild) - Main URL: https://herramientas.ia (fetch failed)
- **AI Chat** (chat) - Main URL: https://aichat.jolav.me/ (fetch failed)
- **LiteAI** (chat) - Main URL: https://liteai.chat/ (fetch failed)
- **Quick AI & Jolbak** (socialBots) - Main URL: https://quickai.jolbak.com (fetch failed)

### Timeouts (2 links)
- **🤖 ImageEditer** (socialBots) - Main URL: https://t.me/ImageEditer_bot
- **Pollinations Telegram Assistant** (socialBots) - Main URL: https://t.me/pollinations_assistant_bot

## 🔧 Recommended Actions

### High Priority (404 Not Found)
These projects should be reviewed for removal or URL updates:
1. Check if projects have moved to new repositories
2. Search for alternative working versions
3. Remove dead projects from the showcase

### Medium Priority (Server Issues)
These may be temporary issues:
1. **502 Bad Gateway** - Telegram bots may be temporarily down
2. **429 Too Many Requests** - Rate limiting, may work later
3. **Timeouts** - Server overload or network issues

### Low Priority (Access Issues)
1. **403 Forbidden** - May require authentication or have access restrictions

## 📁 Files Generated
- **JSON Report:** `broken-links-report.json` (detailed machine-readable format)
- **Markdown Report:** `BROKEN_LINKS_REPORT_2025-07-12.md` (this file)

## 🚀 Next Steps
1. Review the consolidated broken links document: `BROKEN_LINKS_CONSOLIDATED.md`
2. Update project configuration files to remove dead links
3. Search for working alternatives for high-value projects
4. Re-run the link checker after updates to verify fixes

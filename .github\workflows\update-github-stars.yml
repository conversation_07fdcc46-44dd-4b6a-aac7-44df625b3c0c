name: Update GitHub Star Counts

on:
  # Run weekly on Monday at 00:00 UTC
  schedule:
    - cron: '0 0 * * 1'
  
  # Allow manual triggering
  workflow_dispatch:

jobs:
  update-stars:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 1
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Update GitHub star counts
        run: |
          node .github/scripts/update-project-stars.js
      
      - name: Check for changes
        id: git-check
        run: |
          git diff --exit-code || echo "changes=true" >> $GITHUB_OUTPUT
      
      - name: Commit changes if any
        if: steps.git-check.outputs.changes == 'true'
        run: |
          git config --local user.email "github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"
          git add pollinations.ai/src/config/projectList.js
          git commit -m "Update GitHub star counts [skip ci]"
      
      - name: Push changes
        if: steps.git-check.outputs.changes == 'true'
        uses: ad-m/github-push-action@master
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          branch: ${{ github.ref }}

{"name": "pollinations-text-cloudflare-cache", "version": "1.0.0", "description": "Cloudflare R2 + CDN caching solution for Pollinations text service", "type": "module", "scripts": {"deploy": "wrangler deploy", "logs": "wrangler tail", "dev": "wrangler dev --local --port 8888"}, "keywords": ["cloudflare", "r2", "cdn", "caching", "pollinations", "text"], "author": "Pollinations", "license": "MIT", "devDependencies": {"wrangler": "^4.15.0"}, "dependencies": {"fast-json-stable-stringify": "^2.1.0", "node-fetch": "^2.7.0"}}
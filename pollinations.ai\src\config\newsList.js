export const newsList = `- **2025-07-04** – **Flux Kontext Model** now available! General-purpose image-to-image generation model supporting transformations and variations. Requires seed tier or higher. Use with \`?model=kontext&image=URL\`.
- **2025-05-28** – **User Tier System** launched! Manage your app's tier and access benefits like unlimited usage, advanced models at [auth.pollinations.ai](https://auth.pollinations.ai). [Learn more](https://github.com/pollinations/pollinations/blob/master/APIDOCS.md#user-tier-system).
- **2025-05-28** – New Auth Dashboard! Visit [auth.pollinations.ai](https://auth.pollinations.ai) to manage API tokens and referrer domains. [Learn more](https://github.com/pollinations/pollinations/blob/master/APIDOCS.md#authentication-).
- **2025-05-28** – **Special Bee** requests now available for Flower tier upgrades. Submit a [Special Bee Request](https://github.com/pollinations/pollinations/issues/new?template=special-bee-request.yml). [More info](https://github.com/pollinations/pollinations/blob/master/APIDOCS.md#special-bee-) 
- **2025-04-12** – New **Text Feed** section added to the website. View real-time text generations and test text API endpoints directly from the site.
- **2025-03-28** – API documentation improvements with better examples, clearer explanations, and interactive code snippets. [API Documentation](https://github.com/pollinations/pollinations/blob/master/APIDOCS.md).
- **2025-03-24** - MCP Server now supports audio generation! AI assistants like Claude can now generate audio in addition to images using our Model Context Protocol server. Install with \`npx @pollinations/model-context-protocol\`. Community alternatives also available. [Learn more](https://github.com/pollinations/pollinations/tree/master/model-context-protocol).
- **2025-03-20** - New MCP Server! AI assistants like Claude can now generate images directly using our Model Context Protocol server. Install with \`npx @pollinations/model-context-protocol\`. Official and community MCP implementations available. [Learn more](https://github.com/pollinations/pollinations/tree/master/model-context-protocol).
- **2025-03-06** - You can now support us with our new **Tip Us** button. Optionally connect your Discord account to **Ko-Fi** to get premium Discord roles!
- **2025-03-05** - New audio features! Text-to-speech and speech-to-text capabilities are now available with the \`openai-audio\` model. Check the [API Docs](https://github.com/pollinations/pollinations/blob/master/APIDOCS.md).
- **2025-02-05** - Introducing [Pollinations.DIY](https://pollinations.diy) - A browser-based development environment for building AI-powered applications with Pollinations services, based on bolt.diy!
- **2025-02-05** - New Gemini 2.0 Flash and Gemini 2.0 Flash Thinking models are now available on [text.pollinations.ai/models](https://text.pollinations.ai/models)!
- **2025-01-25** - New lightning fast Flux.Schnell backend thanks to Cloudfare (for details on cloudflare check https://developers.cloudflare.com/workers-ai/)
- **2025-01-05** - Want a new feature? Create a [GitHub issue](https://github.com/pollinations/pollinations/issues/new) and our [AI assistant](https://mentat.ai/) will implement it!`;

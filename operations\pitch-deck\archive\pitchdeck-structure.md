**Creating information-dense markdown doc**

The user wants a detailed yet concise markdown document summarizing our conclusions on pitch structure. The document should cover three options (10 slide classic, 8 slide compact, 5 slide teaser) and key lessons, style, do's and don’ts. Remember to keep the text lean, under 20 words per point, with numbered sidebars and slide rationale. They don't need to recheck specifics like stats, but sources might still be good to include. I'm focusing on clarity and details while keeping it structured.


**Planning markdown doc structure**

The goal is to create a structured markdown document with citations after each reference. We'll break it into sections:

* Summary
* Structural conclusions
* Slide flow options (10, 8, 5) with rationale
* Universal slide guidelines
* Visual style guidelines
* Lessons from past decks
* Variant choice guidance
* Appendix with reference frameworks (<PERSON>, Se<PERSON>uo<PERSON>, YC, etc.)

We'll gather at least 12 sources, performing searches and adding citations after each fact or framework reference. Let's get started.


**Crafting markdown with citations**

We’ll now work on the markdown document, focusing on summarizing structure conclusions in a dense, easy-to-read way. I'll add headings for each section and insert citations after any sentence backed by an external source. For internal conclusions, no citations are needed. We aim for at least 10 citations, incorporating frameworks like <PERSON>’s 10 slides, Sequoia’s deck guidelines, and YC’s 7-slide recommendation. I'll integrate these citations where relevant for clarity and context. I’ve gathered at least 11-12 sources, so we’re good to go.


Thought for 54 seconds


## Executive Summary

* **Three deck lengths** cover every context:
  **10‑slide classic** (full partner meeting) → **8‑slide compact** (first call) → **5‑slide teaser** (cold email).
* All variants keep the same narrative spine: **Traction early → Problem → Solution/Demo → Market & Moat → Biz Model → Team/Ask.**
* Universal craft rules: one idea per slide, ≤ 20 words text, numbered sidebar for flow, visuals dominate.\*

---

## 1 · Structural Options

### 1.1 Classic **10‑slide VC deck**

| #             | Slide           | Notes (why keep it)             |
| ------------- | --------------- | ------------------------------- |
| 1             | Cover           | Identity + “Roblox for AI” hook |
| 2             | Traction        | Credibility upfront             |
| 3             | Problem         | Pain in 1‑line                  |
| 4             | Solution + Demo | URL API + mosaic                |
| 5             | Market & Timing | Why‑now window                  |
| 6             | Business Model  | Ads • Premium • Rev‑share       |
| 7             | Go‑to‑Market    | Blog → Discord → SDK            |
| 8             | Roadmap         | 3‑phase timeline                |
| 9             | Team            | Faces + 2‑line bios             |
| 10            | Ask             | \$3 M seed, runway, KPIs        |
| **Rationale** |                 |                                 |

* Mirrors Guy Kawasaki’s 10‑slide template (investor muscle memory) ([Kruze Consulting][1], [Guy Kawasaki][2])
* Matches Sequoia’s order, easing due‑diligence cross‑check ([www.slideshare.net][3])

---

### 1.2 Compact **8‑slide story arc**

| Slide Flow         | Combines              |
| ------------------ | --------------------- |
| Cover + Vision     | —                     |
| Traction hook      | —                     |
| Problem            | —                     |
| Solution & Demo    | slides 4‑5 of classic |
| Market + Biz Model | merge classic 5 & 6   |
| GTM + Moat         | merge 7               |
| Roadmap            | 8                     |
| Team + Ask         | 9‑10                  |
| **Rationale**      |                       |

* YC advises 7‑8 slides for seed; keeps investors’ attention band ([Y Combinator][4], [Forbes][5])
* LinkedIn op‑ed shows eight slides map to “eight psychological pillars” of an investor decision ([LinkedIn][6])

---

### 1.3 Ultra‑lean **5‑slide teaser**

| Slide                                    | Contents |
| ---------------------------------------- | -------- |
| 1 Cover + Tagline                        |          |
| 2 Problem ⇄ Solution split               |          |
| 3 Demo mosaic + traction numbers overlay |          |
| 4 Market size + Revenue model icons      |          |
| 5 Team · Micro‑timeline · Ask            |          |
| **Rationale**                            |          |

* Matches 5‑slide email deck templates (Visible.vc, Slidebean) optimised for intro outreach ([Visible.vc][7], [slidebean.com][8])
* Teaser format endorsed for “attention‑grab then follow‑up” playbooks ([Globalify][9])

---

## 2 · Universal Slide Guidelines

* **One idea, one slide** → improves recall; supported by Pitch.com best‑practice guide ([Pitch][10])
* **≤ 20 words text**; font ≥ 30 pt = 10/20/30 rule ([Guy Kawasaki][2])
* **Numbered sidebar rail** (1‑10) for orientation—borrowed from Yazan deck & recommended by storytelling articles ([Medium][11])
* **Start with proof (traction)**; Wired & Forbes stress showing product/data early to hook investors ([WIRED][12], [Forbes][5])
* **Visual dominance**: demo mosaics, diagrams, icon grids—echoes Venngage & Figma design guidance ([Figma][13], [Venngage][14])

---

## 3 · Layout & Content Patterns worth keeping

| Pattern                    | Origin deck           | Re‑use                         |
| -------------------------- | --------------------- | ------------------------------ |
| Numbered chapter headings  | Old “Yazan” deck      | Add to left rail               |
| Traction before Problem    | Yazan & Laurent decks | Keep slide 2                   |
| Two‑column grids           | Current draft         | Good for dense info            |
| Middle‑layer “SDK” diagram | All legacy decks      | Place in Market & Moat         |
| Metrics scoreboard         | Laurent deck          | Compress into “Traction” slide |
| Risk‑mitigation appendix   | Laurent deck          | Optional backup                |

---

## 4 · When to choose which variant

* **Investor partner meeting / Demo Day** → Classic 10 (full narrative, still concise).
* **First‑look call or recorded Loom** → Compact 8 (4‑min voice‑over).
* **Cold intro e‑mail / event follow‑up** → Teaser 5 (PDF ≤ 1 MB).

---

## 5 · Mapping to Reference Frameworks

| Our Slide     | Guy Kawasaki            | Sequoia              | YC “must‑have” |
| ------------- | ----------------------- | -------------------- | -------------- |
| Traction      | —                       | Why‑now              | Traction       |
| Problem       | Problem                 | Problem              | Problem        |
| Solution      | Value‑prop + Magic      | Solution/Product     | Solution       |
| Market & Moat | Business model + Market | Market & Competition | Market         |
| Biz Model     | Business model          | Business             | Model          |
| GTM           | Go‑to‑Market            | Competition/Product  | Plan           |
| Roadmap       | —                       | Financials → Roadmap | Plan           |
| Team          | Team                    | Team                 | Team           |
| Ask           | Current status/Ask      | Financials           | Ask            |

(Helps another AI align content to any investor‑preferred template.)

---


May 9, 2025
pollinations.ai - Antler Pitch Review
Invited <PERSON>chy <PERSON> Laurent <PERSON>
Attachments pollinations.ai - Antler Pitch Review 
Meeting records Transcript 

Summary
<PERSON> presented Pollinations, an AI app factory that simplifies the creation and monetization of AI applications for developers by handling infrastructure and offering a 50/50 revenue share model. <PERSON> provided extensive feedback on the presentation, suggesting improvements to the flow, structure, visual examples, clarity of the problem and solution, market analysis, and overall conciseness, emphasizing the need to showcase traction, team expertise, and a clear vision. The participants discussed Pollinations' ecosystem, two-sided market strategy, and future plans for growth in the indie developer market.

Details
Pollinations Overview Thomas Haferlach introduced Pollinations as a platform enabling various AI applications, highlighting an example of a popular Roblox game created by a young Ukrainian developer that boasts significant user engagement (00:00:00). This showcases Pollinations' accessibility for independent developers who might face barriers on other platforms (00:01:38).
Developer Integration and Growth Thomas <PERSON> explained that developers can easily integrate Pollinations' API (00:02:40). This simplicity has led to substantial organic growth, with over 100 million AI media generations monthly and a consistent 35% month-over-month user growth (00:06:13).
Problem Statement: Complexity and Monetization <PERSON> outlined the challenges faced by creators and developers in building and monetizing AI applications today, including infrastructure headaches, limits, authentication issues, and a lack of clear monetization paths. For brands, there's a large untapped market of indie apps with unused advertising space (00:03:42) (00:07:40).
Solution: AI App Factory Thomas Haferlach presented the Pollinations AI app factory as a solution, comparing it to Unity for AI, emphasizing its simplicity for building and monetizing AI applications. It offers instant integration and handles complex infrastructure, positioning it as a plug-and-play AI solution (00:04:56).
Revenue Sharing Model Thomas Haferlach detailed a planned 50/50 revenue share model where creators can monetize their apps, aligning incentives between Pollinations and its developers (00:04:56) (00:08:46). This model aims to provide the easiest path to a live, profitable ad-supported app with zero upfront costs (00:06:13).
Ecosystem Breakdown and Focus Thomas Haferlach provided a breakdown of their ecosystem, including visual creation tools, conversational AI tools, and mobile apps, noting that a significant percentage are high-value ad placements (00:10:03). Their primary focus is the indie developer market, recognizing the growing trend of "vibe coding" and the vast advertising potential in viral small apps (00:07:40).
Two-Sided Market Strategy Thomas Haferlach described Pollinations' two-sided market approach, connecting apps with empty advertising space to advertisers seeking untapped opportunities. They have already built a strong supply side (developers and apps) and are now focusing on scaling the demand side (advertisers) (00:08:46).
Future Plans and Vision Thomas Haferlach shared their future plans, including the goal of constant recurring ad revenue within 12 months and the launch of a second SDK version with deeper integrations and multi-modal possibilities thereafter. Their vision is to become the ubiquitous assistant-centric engine for AI creation and distribution (00:11:20).
Risk Analysis (Initial Thoughts) Thomas Haferlach briefly touched on risk factors, suggesting that while important to consider, this topic might be better suited for a Q&A session rather than the main pitch, aiming to frame it around how funding will help mitigate these risks (00:11:20).
Team and Recruitment Needs Thomas Haferlach introduced themself as a veteran in generative AI with extensive industry experience. They also highlighted the company's need for a senior AI ops and infrastructure person, as well as a marketing and community manager to support growth and ad tech efforts (00:12:46).
Feedback on Presentation Flow Laurent Pacoud praised the global flow and structure of Thomas Haferlach's presentation, noting the strong opening and the undertaking of their core belief about AI's future (00:15:27). Laurent Pacoud suggested expanding the initial list of AI-native products and liked the direct shift to a concrete example (00:16:22).
Suggestions for Example Slides Laurent Pacoud recommended including screenshots of multiple diverse apps created with Pollinations and potentially positioning the Roblox example later. Laurent Pacoud emphasized the effectiveness of the user statistics for the Roblox game in capturing attention (00:17:15).
Presentation Structure and Conclusion Laurent Pacoud suggested reconsidering the conclusion, possibly ending with the vision and questions rather than just the team (00:18:14). Laurent Pacoud found the concluding slide on product development a bit complex and advised simplifying it to a few key concepts (00:19:16).
Highlighting Team Expertise Laurent Pacoud agreed with the feedback received earlier to better highlight the team's experience by showcasing logos of previous prominent employers. Laurent Pacoud suggested placing the recruitment needs on a separate slide following the team introduction (00:20:47).
Positive Feedback on "Plug and Play" Concept Laurent Pacoud appreciated the "plug and play system" description, finding it clear and impactful. Laurent Pacoud noted that the presentation contained a lot of information, requiring careful condensation (00:21:47).
Importance of Visual Examples with Ads Laurent Pacoud suggested including an example showing how an ad appears within a generated result to effectively demonstrate the core value proposition (00:22:39). Laurent Pacoud also recommended incorporating videos to make the presentation more engaging (00:23:31).
Refining the Problem Statement Laurent Pacoud felt the explanation of the problems was too rapid and could be synthesized more effectively, focusing on key pain points like the complexity of accessing AI tools, platform barriers, and difficulties in adding ad systems for revenue (00:23:31). Laurent Pacoud suggested framing the problems around user experience, such as the need for sign-ups on other platforms versus Pollinations' no-signup approach (00:24:31).
Simplifying the Solution Presentation Laurent Pacoud disliked the solution slide, finding the UI/UX terminology unexplained and the mixing of technical solutions with monetization confusing (00:26:51). Laurent Pacoud strongly recommended a video demonstrating the simplicity of integrating AI tools using Pollinations' interface (00:27:38).
Emphasizing the AI Toolbox and Traction Laurent Pacoud urged Thomas Haferlach to make the AI toolbox tangible and easy to understand, perhaps by showing a programming editor with Pollinations tools (00:28:47). Laurent Pacoud also advised separating the explanation of the technical toolbox from the ad system toolbox, clearly indicating the latter is under development (00:30:32).
Leveraging the Growth Curve Visually Laurent Pacoud stressed the importance of visually showing the growth curve to attract attention and convey the impressive traction Pollinations has achieved (00:31:24). Laurent Pacoud recounted being convinced by seeing this very curve previously (00:32:26).
Clarifying "Media" Generation Metrics Laurent Pacoud pointed out the ambiguity of the term "media" and suggested using "text, images, and audio generated" or "AI-generated media" for better clarity (00:33:20). Laurent Pacoud enthusiastically endorsed the idea of showing a real-time feed of generated images and text to emphasize the volume and dynamism (00:34:59).
Market Analysis Feedback Laurent Pacoud admitted to not understanding the "use ad market" terminology or the SAM, indicating a need for simplification and clarity in presenting the market opportunity, suggesting focusing on one key figure (00:36:14).
Rethinking the "Circular Economy" Concept Laurent Pacoud, with expertise in circular economy, did not see its applicability to Pollinations and suggested reconsidering its use, focusing instead on the self-perpetuating nature of their ecosystem (flywheel effect) (00:37:43). Laurent Pacoud noted that the explanation of the circular economy reiterated points already made (00:40:43).
Conciseness and Slide Design Laurent Pacoud found the slide on the ecosystem breakdown unreadable due to the amount of information, emphasizing the need for simplification across all slides (00:40:43). Laurent Pacoud advised aiming for shorter presentations, with each slide conveying a single clear message supported by visuals (00:41:28).
Roadmap Presentation Laurent Pacoud found the roadmap slide too complicated, with overlapping elements, and suggested a simple one-to-three step approach for presenting their strategy (00:42:07).
Placement of Risk Analysis Laurent Pacoud agreed that a detailed risk analysis is not suitable for a short pitch and is better reserved for follow-up Q&A, aligning with Thomas Haferlach's intent (00:44:01). Laurent Pacoud suggested framing the use of funds around mitigating these risks (00:45:03).
Team and Vision Slides (Reiteration) Laurent Pacoud reiterated the suggestions for the team slide (simplicity, images, big company logos) and the importance of a clear, concise vision slide with key concept points (00:45:51).
Geographic Focus and Technical Details Laurent Pacoud advised including the EU as a significant user region and cautioned against mentioning overly technical details like "DeepSeek" in the main pitch, suggesting it could lead to too many technical questions (00:46:58).
Importance of Knowing the Pitch by Heart Laurent Pacoud strongly emphasized the need to know the entire presentation by heart to eliminate filler words ("hum") and deliver a confident, impactful pitch (00:48:53). Laurent Pacoud believed further practice and refinement of the structure were necessary .
Follow-up Meeting Laurent Pacoud offered to review the pitch again on Tuesday or Wednesday night .

Suggested next steps
Thomas Haferlach will create a dedicated slide for the team, including photos and logos of previous companies worked at.
Elliot Fouchy will resolve the technical problem causing overlap between the roadmap and activation.
Thomas Haferlach will reformulate the risk factors slide to focus on how the requested funding will mitigate those risks.

You should review Gemini's notes to make sure they're accurate. Get tips and learn how Gemini takes notes
Please provide feedback about using Gemini to take notes in a short survey.

---

Transcript
00:00:00
 
Thomas Haferlach: This can be from um voice voice chat assistants, personalized ones to uh creative experiences and so on. Our traction is Oh, wait. Ah, sorry.
Elliot Fouchy: The FL
Thomas Haferlach: Ah
Elliot Fouchy: the sun is not moving.
Thomas Haferlach: uh it's not moving.
Elliot Fouchy: No.
Thomas Haferlach: Okay. So, let's go back to screen share. I'm sorry. We will try this uh next uh next time. the um
Laurent Pacoud: You're welcome. No problem.
Thomas Haferlach: okay um share let's find the right window sorry second this one okay do you do you have the view now you see
Laurent Pacoud: Yes.
Thomas Haferlach: the okay
Laurent Pacoud: Would you see
Thomas Haferlach: um just reload Okay. Okay. So, um so what does what does what does pollinations allow? Um I've chosen for our presentation um one one example application. It is um an app which has which is been created by a 18-year-old um Ukrainian developer. They um um use Pollinations to create a a Roblox game. So it's a game uh in which you can you can chat within a game universe with um a virtual character uh in this case a kind of a cut.
 
 
00:01:38
 
Thomas Haferlach: Um um and uh this gamers is one of our most um let's say one of our apps with the most most usage. It's got an impressive 1,000 concurrent users at all times. It's got 16 million visits and it's one of one of many examples of what you can what you can build with pollination. So this um this koda would not have had an other another way to create this app because other platforms have restrictions that are a too high barrier to entry for for this independent developer. They they don't even have a credit card. Um so we are g and and even many co many coders that do have credit cards they are not willing to sign up to a service um uh that they don't understand. Um so we are basically
Elliot Fouchy: Good
Thomas Haferlach: ramping.
Elliot Fouchy: moving.
Thomas Haferlach: Sorry. Yeah. Yeah. Yeah. Okay. I'm I'm I'm going to go on. So um what's remarkable is that the developer simply integrate
Elliot Fouchy: Still not
 
 
00:02:40
 
Thomas Haferlach: the
Elliot Fouchy: moving.
Thomas Haferlach: API. Sorry.
Elliot Fouchy: I'm still on pollination. Uh
Thomas Haferlach: Ah no no but but I'm sharing the screen now. Ah it's not moving. What? Okay. Sorry. Sorry. Um this you were supposed to see this while I talk. Okay.
Laurent Pacoud: Okay.
Thomas Haferlach: This we will this we will really improve. Um um um so here you see uh the example. Um and I will do it like this. I know how. Uh
Elliot Fouchy: It's
Thomas Haferlach: okay.
Elliot Fouchy: good now.
Thomas Haferlach: Yeah.
Elliot Fouchy: Yeah.
Thomas Haferlach: Okay. So I um
Elliot Fouchy: Back to
Thomas Haferlach: Okay.
Elliot Fouchy: the first page.
Thomas Haferlach: Yes. Okay. But now we're now we're synced. Okay. Now we're synced. Um so um uh um we we're seeing similar success success stories across discord bots, mobile app apps and web platforms all powered by a simple accessible API. Um so what's the problem?
 
 
00:03:42
 
Thomas Haferlach: The problem is that um creators and developers building with to for creators and developers building with to II today it's complex and expensive. They face infrastructure headaches. they have to create a back end. Um they face issues with um limits and authentication and they don't have a clear path to monetization. So um um on the other hand um we are so our plan is um to allow these developers to and us to monetize their apps through um uh through contextsensitive advertising that we place into into the developers app. Um so uh this is a huge and and so the problem here for for brands is that there's a huge unt untapped market market of indie apps of apps that are going viral but they have no they have they they they have a lot of space for advertising but um um advertisers don't have a clear path towards placing ads into these apps. We have the demand side which is the the supply side which is the apps. So let's get to the fix. Let's get to our solution.
 
 
00:04:56
 
Thomas Haferlach: Then I will come to um supply and demand. Um so our solution is a polinations app AI app factory. It makes it radically simple to build and monetize. Um think unity for AI. Unity is a large um game development platform which allow which allows millions of developers um to monetize their games on their platform and they are also they are also pushing ads to their clients with a very successful business model. Um we we handle the setup you can inst uh you can in integrate instantly um and you don't have to worry about any complex infrastructure. Um it's so it's plug and play for AI. Um we don't know of any other um API that is so easy and and so frictionless um to use. And this is of course also reflected in our reflected in our usage numbers. Um, in the future, we're we we're planning in order to help our our our creators monetize, we are we are planning to uh 50/50 revenue share where we share any revenue they create through their apps with us.
 
 
00:06:13
 
Thomas Haferlach: So, we are offering the easiest path to a live profitable ad app with zero upfront costs. Um due to this this ease of use, we've seen an incredible amount of organic organic growth. Um it's been exponential. Um we're right now powering over 100 million AI media generations every month and it's and the growth has been a consistent 35 uh% month over month um growth. It hasn't been slowing. So we've we've grown from 300k um monthly active users to three million in the period of the last um seven months. And um we have um uh we have certain apps of course we have a certain percentage of apps that are very successful and then a lot of kind of smaller apps. Um but all of them together uh lead to this um u um impressive traction. uh it's also global. So we are we are reaching um uh um China um US India principally they are our main um our main um consumers. China has seen an an a very strong rise um recently due to easy integration with um the powerful DeepS um language model and developers are building apps daily.
 
 
00:07:40
 
Thomas Haferlach: So daily we have an app store to which developers are are p publishing their apps. Maybe store is the wrong word because we're not selling anything but we have an app index. Um and this app index is growing every day um by a couple of apps which has this kind of knock-on effect this which where other people then get to know polinations and it's growing by itself. Uh we're we're focusing um on the indie developer market. So this a large percentage of that is is is youth. Um but it's it's we are not focusing on corporate customers. We're focusing on developers who are especially the large and growing demographic of developers who are um vibe coding. Now we are seeing this rise in our communities. Um everyone is becoming a creator. So this unlocks vast new advert uh advertising potential. So there are many small apps that are going viral and um we are we are we are we have all this advertising space which is unused and what we are offering is a two-sided market.
 
 
00:08:46
 
Thomas Haferlach: So on the one side we have the supply which is uh uh apps that have empty advertising space and a lot of them and they have a many users and on the demand side we are connecting this with advertisers who want to put their ads into spaces that are untapped. So what we what we are what we have already built out is the supply side. So we have an incredible amount of developers and ads in our platform. What we need and what we are scaling now is the the demand side. So to bring the advertisers into these apps and then in the in the next phase we want to share revenue with the creators to also yeah to align our incentives. So I I mean as I've this this leads to a circular economy where um um the geni with the exploding gen AI market and um the advantages that scale give us such as um better data unlocking smarter ad targeting um um we have this we are in this reinforcing cycle um so so unlike other AI model providers is like open AI hugging face.
 
 
00:10:03
 
Thomas Haferlach: We offer free access and a built-in revenue path. Unlike game monetizers like Unity ads, we are the AI creation engine and the monetization layer. Mhm. So, I'm pretty sure we're running out of time by now, but I did waste a little bit of time on the on some places. Um um so we have here a little bit of a breakdown of our ecosystem. So um 40% of tools are visual creation tools, 25% conversational AI tools like chat bots and discord bots and then we have more than 10% mobile apps. We classify from these
Laurent Pacoud: No.
Thomas Haferlach: about 7 75% are high high value ad places. 25% of these apps are not are not interesting to place ads. These um uh we will continue supporting on our free tire but we will we will start offering um premium um features to the models to the s to the customers we can monetize um in the next 12 months we want to we don't want we don't need many new features. We want to scale and build out the platform that we have right right now.
 
 
00:11:20
 
Thomas Haferlach: We wanna um we want our ad based monetization pipeline to be bringing um um constantly growing recurring revenue. And then um after 12 months we want to we we we plan to launch the second um version of our software development kit with um deeper integrations and um and new multimodel um possibilities. We want to become the ubiquitous assist assistant centric engine for AI creation and distribution. Um um so this um now um um we we uh this this is a slide I we would um uh we would we would show in the in the in the because we have like 12 minutes with the with the um antla I see. I don't know if we
Elliot Fouchy: Three,
Thomas Haferlach: would if we would um show this to every every investor unless they ask, you know, but I think it's good um because it shows that we've that we've analyzed the the risk um factors.
Elliot Fouchy: five,
Thomas Haferlach: Um so um but I haven't really practiced um um um um presenting it yet. Maybe you also have some ideas how how we would present it um best because we also don't want to scare them you know that there's that there's too many risks.
 
 
00:12:46
 
Thomas Haferlach: Um but I think it's I think we need to tell them like what maybe we can also formulate it more in terms of like these are this is what we need the money for rather than you know we're going to solve this with your money rather than saying okay this is going to bring us down. But I think it's super good that we that we have this. I just wanted to um reformulate it a little bit and um then we come to the team. Um uh so I am I I am an I am a veteran in the in the generative AI space. I've I've I have more than 20 years of industry experience working with AI. I've worked for Amazon Sun Micros Systemystems and I've also um um got extensive experiences in um scaling um uh scaling solutions uh and and have consistently um had a very precise vision of how the future of AI will develop um um and um I will execute um um delivery and wait sorry um I I didn't practice this one at all really we haven't pitched that much yet Laur um but um so yes but so we are looking for a senior AI ops and infra infrastructure um um person um to so that I can I don't I don't focus um I can focus on the strategy and the and
 
 
00:14:21
 
Thomas Haferlach: and the AI feature And we're looking for for marketing and community person to to grow our to grow our reach and to help us with the ad um with the ad tech. Yes. Thank you.
Laurent Pacoud: Okay, thank you Thomas. Uh time 15 minutes,
Thomas Haferlach: Yeah. Yeah.
Laurent Pacoud: but
Thomas Haferlach: Yeah.
Laurent Pacoud: definitely if you're more train, you will you will you will pass at 12.
Thomas Haferlach: Yeah. Yeah. Yeah. Yeah. I I I wasted a bit of time in here and there and I was I was kind of not um I have I had some kind of things written down but then I completely went into improvisation mode. So um uh the ideas that I practice to stick to stick closer to what what I'd actually planned planned to say before.
Laurent Pacoud: Yeah. What uh when when is the presentation the formal one?
Thomas Haferlach: uh next Friday.
Laurent Pacoud: Next Friday. So you uh Yeah. So you have the time to to make it perfect.
 
 
00:15:27
 
Thomas Haferlach: Yeah, I'm planning to practice every day. I mean, you're one of the um earlier um let's say uh recipients of this pitch, but I really want to have this have this like out of the out of my heart. It's coming. I don't even have to think about it, you
Laurent Pacoud: Okay.
Thomas Haferlach: know.
Laurent Pacoud: Okay. So, well, wonderful. I'm going to share with you what I liked and what I didn't like. Uh there's many
Thomas Haferlach: Let
Laurent Pacoud: thing I liked.
Thomas Haferlach: let us maybe record it if
Elliot Fouchy: recording.
Thomas Haferlach: we can. You
Elliot Fouchy: I mean at least the
Thomas Haferlach: okay? Perfect. Yeah. Yeah. Yeah.
Laurent Pacoud: Okay. So what I think is really good is the the well the global flow uh the way you are presenting all steps. Uh I mean I totally um recognize the the structure of the of the presentation. Uh I really like that you you you undertake and you begin on what you believe.
 
 
00:16:22
 
Laurent Pacoud: This is strong and uh this is really a way to appeal my attention. I do
Thomas Haferlach: Mhm.
Laurent Pacoud: believe that AI will be that application informatic numeric application will be AI native tomorrow. That's gave me my my full attention. So I really
Thomas Haferlach: Yeah.
Laurent Pacoud: liked
Thomas Haferlach: Yeah. Yeah. Yeah.
Laurent Pacoud: um uh when you say you list a long list of of potential numeric products that will become AI native like voice chat uh or and you didn't finish the list. So you may you may continue it.
Thomas Haferlach: Uh-huh. Uh-huh. Uh-huh. Uh-huh.
Laurent Pacoud: That was good. Um, and then you directly shift on an example. Um,
Thomas Haferlach: Uh-huh.
Laurent Pacoud: so unfortunately we didn't shift on the on the on the on the good slide, but I really liked it. Can we go back on the this first very very first
Thomas Haferlach: Yes.
Laurent Pacoud: slide on
Thomas Haferlach: Yes. Yes.
Laurent Pacoud: the
Thomas Haferlach: I actually I'm planning to still extend that a little bit.
 
 
00:17:15
 
Thomas Haferlach: I want to have not just the Roblox example here, but also u just maybe screenshots of two other apps. So you just see like that there's kind of quite a breath in different types of apps that have been made with pollinations.
Elliot Fouchy: And maybe also I would put blocks last uh as it is like most uh outside of the business model that we present.
Thomas Haferlach: Yeah. But I think people don't even know whether that's in the business model or not right now at this point. It's just like one examp we we we want we want two other examples too. Um
Laurent Pacoud: I mean it's good
Thomas Haferlach: yeah
Laurent Pacoud: to to say at one moment I would begin
Thomas Haferlach: yeah yeah
Laurent Pacoud: with this one. This is the biggest user of our system and
Thomas Haferlach: yeah
Laurent Pacoud: it's a huge game. You've been extremely pertinent pertinent with with the figures you said or always commonly were gaming together 1,000 person and and uh you say how much six million of persons users or visited 16 million
 
 
00:18:14
 
Thomas Haferlach: 16 16 million um users visited
Laurent Pacoud: that that's amazing. So 16 million users have used an application developed with that. So I I I that's one of the figure I do remember at the end of the of the of the presentation.
Thomas Haferlach: Yeah. Yeah. Yeah. Yeah.
Laurent Pacoud: Um and then uh so as as I was saying I like the global flow the presentation is appealing then you undertake problem and solution. I like it.
Thomas Haferlach: Mhm.
Laurent Pacoud: And then you come on more the financial details. Um and you conclude with the team. I am not sure about that because uh you may conclude with the with the questions you have maybe the vision.
Thomas Haferlach: Yeah.
Laurent Pacoud: So
Thomas Haferlach: Uh
Laurent Pacoud: maybe you
Elliot Fouchy: Where?
Laurent Pacoud: should
Thomas Haferlach: ah actually we didn't reach the we we didn't reach the slide of the vision. Um uh we still have a vision slide. I I actually I completely forgot about it.
Laurent Pacoud: Well, I mean vision, vision and questions.
 
 
00:19:16
 
Laurent Pacoud: I mean where where where where is where am I today and what is the really the next steps? I
Thomas Haferlach: Uhhuh.
Laurent Pacoud: like the fact that you end up with I am looking for two persons. So I need two
Thomas Haferlach: Yeah.
Laurent Pacoud: persons to make
Thomas Haferlach: Yeah.
Laurent Pacoud: it happen. Uh so that's one of the final step you know I'm here and
Thomas Haferlach: Yeah.
Laurent Pacoud: that's my steps. It's it's good to to conclude with that. Um
Thomas Haferlach: Yeah,
Laurent Pacoud: so this you should you you you would have finished with this one
Thomas Haferlach: this this one. Yes. Um um uh this is
Laurent Pacoud: product development
Thomas Haferlach: the last one.
Laurent Pacoud: supercharging growth and mon monetization scaling is accusation. Okay. Uh yeah I mean you you you you may do it simpler actually
Thomas Haferlach: Uhhuh.
Laurent Pacoud: uh reduce
Thomas Haferlach: Uhhuh.
Laurent Pacoud: the thing without any details you just three you have four point four concepts because I have to read that and I
 
 
00:20:15
 
Thomas Haferlach: Yeah.
Laurent Pacoud: have
Thomas Haferlach: Yeah.
Laurent Pacoud: to
Thomas Haferlach: Yeah. Yeah. Yeah.
Laurent Pacoud: you
Thomas Haferlach: Yeah. I
Laurent Pacoud: so
Thomas Haferlach: think what's up?
Laurent Pacoud: if I read and there are many acronyms or whatever I you lose my attention so the simplest the better really four point concepts no more
Thomas Haferlach: Yeah. Yeah. Yeah. Yeah.
Laurent Pacoud: but
Thomas Haferlach: Totally.
Laurent Pacoud: I like it that that's the good conclusion definitely I think so and and you may put the recruitment into this one. Is it is it into it or
Thomas Haferlach: No.
Laurent Pacoud: no?
Thomas Haferlach: No, it's not. We It was in the previous slide. Yeah,
Laurent Pacoud: Yeah,
Thomas Haferlach: this was
Laurent Pacoud: I
Thomas Haferlach: the
Laurent Pacoud: mean
Thomas Haferlach: previous slide.
Laurent Pacoud: team deserve a very nice slide that that's us too and we present our
Thomas Haferlach: Uh
Laurent Pacoud: to you. You have photos as well. So I would
Thomas Haferlach: yeah,
Laurent Pacoud: put
 
 
00:20:47
 
Thomas Haferlach: yeah, yeah. So, so we were told in our last pitch um this morning that um we are really underelling our our team because we have also like I have certain like big companies I worked at and like when I see other other pictures you know they put like the logos big like ex Amazon you know x um so I think we have to really and that's a general weakness a little bit we have which is to kind of sell ourselves right um um be confident in in in what we say Um but I hope I think practice will also improve that a lot you know.
Laurent Pacoud: Yeah. Okay. So, excellent idea. You
Thomas Haferlach: Yeah.
Laurent Pacoud: put two two uh Thomas with maybe a bit more space for Thomas because you have those logo and
Thomas Haferlach: Yeah.
Laurent Pacoud: little photos logos of where you worked and um well just just that on the on on the PowerPoint just that and then the recruitment you slip it to the to the next point.
Thomas Haferlach: We could make the the GP the image uh GPT action figures.
 
 
00:21:47
 
Thomas Haferlach: That could be funny, you know. I mean, Yeah. Yeah. Yeah.
Laurent Pacoud: Uh
Thomas Haferlach: Yeah.
Laurent Pacoud: what I also liked is that um you you say plug and play system uh that talks to me. I
Thomas Haferlach: Yeah.
Laurent Pacoud: think that
Thomas Haferlach: Yeah. Yeah. Yeah.
Laurent Pacoud: everyone it is a good formula. Um so yeah it it talks to me um and uh well so that that's my good point. I think that you are making it much clearer than the than the the various times you you try to summarize the thing to me. So it
Thomas Haferlach: Uhhuh.
Laurent Pacoud: is extremely condensed. It is extremely intense to follow that actually there are lots of information. So I'm trying to make the point what what do I have in mind and there are so many things that I think that there is too much. So I am
Thomas Haferlach: Yeah.
Laurent Pacoud: now thinking to my comments. Can we come back to the beginning?
 
 
00:22:39
 
Thomas Haferlach: Yes.
Laurent Pacoud: Um okay so as I was saying starting with an example why not it's good if you have another one why not but
Thomas Haferlach: Mhm.
Laurent Pacoud: uh to see what what what the result is and the final usage utilization of your uh system. So I
Thomas Haferlach: Yeah.
Laurent Pacoud: now see that okay they are doing video games and maybe another one.
Thomas Haferlach: Yeah.
Laurent Pacoud: Uh so then you shift to the next one.
Thomas Haferlach: I think I think one thing also um I I I know this is about your your feedback but just so we we remember it is that we should show one example where an ad appears in the in
Elliot Fouchy: Yes.
Thomas Haferlach: the in the result right because that's the whole point we're making um we're going to put ads in the in the in the experiences
Laurent Pacoud: Good
Thomas Haferlach: but
Laurent Pacoud: idea.
Thomas Haferlach: but we this this is already planned.
Laurent Pacoud: Okay, good
Thomas Haferlach: Yeah.
Laurent Pacoud: idea.
Thomas Haferlach: Uh uh uh.
Laurent Pacoud: And a slide with a video is is strong.
 
 
00:23:31
 
Laurent Pacoud: I mean I just see I can hear you. My eyes are working. So it is good to make it work with with videos. So
Thomas Haferlach: Mhm.
Laurent Pacoud: why not seeing text generated with an ad and you
Thomas Haferlach: Mhm.
Laurent Pacoud: explain
Thomas Haferlach: Yeah. Yeah.
Laurent Pacoud: okay
Thomas Haferlach: Yeah. Yeah. Yeah.
Laurent Pacoud: we can pass. So then you arrive there as I was saying it is really good to present it as problem and solution. Uh
Thomas Haferlach: Yeah.
Laurent Pacoud: a little problem here is that you are in one minute you are passing by really rapidly on that
Thomas Haferlach: Yeah.
Laurent Pacoud: and
Thomas Haferlach: Yeah. Yeah.
Laurent Pacoud: uh
Thomas Haferlach: Yeah.
Laurent Pacoud: I have understood that what you were saying is that it is complicated to have AI tools access. It is complicated to access to any kind of platform because you always have lots of barriers and it is complicated to add add system and and therefore revenues to its system. So you explained me all of that but I confess to you Thomas if I did not know this before I could not synthetize it like this
 
 
00:24:31
 
Thomas Haferlach: Yeah.
Laurent Pacoud: because
Thomas Haferlach: Yeah. Yeah. Yeah.
Laurent Pacoud: you
Thomas Haferlach: Yeah.
Laurent Pacoud: said too much and
Thomas Haferlach: Yeah.
Laurent Pacoud: moreover if you explain to me many problems it means that you will present me many solutions.
Thomas Haferlach: Uhhuh. Uhhuh.
Laurent Pacoud: less problem you have I mean something really really evident would be I have one problem one solution that's not
Thomas Haferlach: Yeah.
Laurent Pacoud: your
Thomas Haferlach: Yeah. Yeah.
Laurent Pacoud: but
Thomas Haferlach: Yeah.
Laurent Pacoud: how
Thomas Haferlach: Yeah.
Laurent Pacoud: to reduce this explanation
Thomas Haferlach: I also I also think maybe the privacy isn't like the central thing that users are coming to us for. Um I do see that we could reduce this.
Laurent Pacoud: yeah
Thomas Haferlach: Um what what Yeah. Please uh continue. Yeah.
Laurent Pacoud: yeah
Thomas Haferlach: Yeah. Yeah.
Laurent Pacoud: so and and when I tell the problem I I would say Um for instance whatever you do on internet you have to sign up in our
Thomas Haferlach: Yeah.
Laurent Pacoud: system you don't sign up okay
 
 
00:25:18
 
Thomas Haferlach: Yeah.
Laurent Pacoud: you make me feel you make me feel I always sign up I am I agree against that you have to sign up everywhere with our system you don't have to sign up
Thomas Haferlach: Yeah. Yeah. Yeah. Yeah.
Laurent Pacoud: the
Thomas Haferlach: Yeah.
Laurent Pacoud: second one uh you can begin with no cost
Thomas Haferlach: Yeah. Yeah. Yeah. Yeah.
Laurent Pacoud: and the third one we assist you to make it profitable to make your app profitable well
Thomas Haferlach: Yeah. Yeah.
Laurent Pacoud: I I I went out of the problems So that the tree problem
Thomas Haferlach: Yeah, that's already the solution, right?
Laurent Pacoud: application you will have to subscribe to many application. With ours
Thomas Haferlach: Yeah.
Laurent Pacoud: you don't have to sign up. It is
Thomas Haferlach: Yeah.
Laurent Pacoud: really tough to uh reach um AI tools toolbox. With ours it is simple.
Thomas Haferlach: Yeah.
Laurent Pacoud: It is
Thomas Haferlach: Yeah.
Laurent Pacoud: really tough to develop ad system revenue. With ours it will be simple.
 
 
00:26:06
 
Laurent Pacoud: We are still working on it and
Thomas Haferlach: Yeah.
Laurent Pacoud: we are
Thomas Haferlach: Yeah.
Laurent Pacoud: waiting for you for
Thomas Haferlach: Yeah. Yeah.
Laurent Pacoud: whatever
Thomas Haferlach: Yeah. Yeah, I like it. I like it.
Elliot Fouchy: The
Thomas Haferlach: I
Elliot Fouchy: only thing I would say here is that um we will offer um money back to the user. This is planned for Q3 2026. But that's okay.
Thomas Haferlach: maybe we can also just talk about that later when we talk about the future.
Elliot Fouchy: Yeah. Because like what one thing that we are like thinking also Lauren for you
Thomas Haferlach: But
Elliot Fouchy: to understand.
Thomas Haferlach: maybe let's let's get Lauren's
Elliot Fouchy: Yeah. Yeah.
Thomas Haferlach: feed feedback and then and then we can then we can discuss.
Elliot Fouchy: Good.
Laurent Pacoud: Yeah.
Elliot Fouchy: Good.
Laurent Pacoud: So synthetize the maximum the problem make it really feeling. I do have to feel I have problems with you guys.
Thomas Haferlach: Yeah.
Laurent Pacoud: So put me in the skin of a programmer.
 
 
00:26:51
 
Laurent Pacoud: If you are a programmer you will experience that and
Thomas Haferlach: Yeah.
Laurent Pacoud: at the end
Thomas Haferlach: Yeah.
Laurent Pacoud: of the day you okay and then
Thomas Haferlach: Yeah.
Laurent Pacoud: you
Thomas Haferlach: Yeah.
Laurent Pacoud: come with the solution. I don't like this slide. I don't like this slide guys. I I don't understand the solutions. I don't understand the solution.
Thomas Haferlach: Uh
Laurent Pacoud: I don't know what is UI and UX. I think you did not explain it. Um
Thomas Haferlach: uh.
Laurent Pacoud: and we are mixing the technical solution with monetization. Well, that that that's the point, but would be good to have the two explanation quite quite kazzy independently actually. uh because
Thomas Haferlach: Yeah.
Laurent Pacoud: you have an informatic platform to develop developed and and that's your that's your that's your biggest added value. So you have to show it. I mean I don't I don't see it. I need to show I need to see it. I don't see it here.
 
 
00:27:38
 
Laurent Pacoud: So can you make a video that brings me So yeah I bring I bring you with me in a classic informatic editor programming editor. Look at what happens. The guy clicks here. The guy clicks here and then he have the AI tools. It is incredibly simple and thanks to that he get
Thomas Haferlach: Yeah.
Laurent Pacoud: he can integrate AI AI tool into its programming
Thomas Haferlach: Yeah.
Laurent Pacoud: and
Thomas Haferlach: Yeah.
Laurent Pacoud: and with
Thomas Haferlach: Yeah.
Laurent Pacoud: a video that shows the system. So I understand that you guys have developed quite kind of an interface that gives me AI tools but I have to see it. If I don't see it I don't understand it.
Thomas Haferlach: Yeah. Yeah. I think that maybe that's an opportunity also to show a very simple um real real video because the fact is that uh there's there's a bunch of like let's say vibe coding apps that inclusively were that already were made actually with polinations where you actually have a very simple interface which is a prompt a prompt box and you get then an app out right and and this this we can see in reality um And and I think that's what the feedback we've gotten repeatedly.
 
 
00:28:47
 
Thomas Haferlach: Sorry then I will not stop you because I think it it should be about you and not about what I'm responding to it. Right. Sorry. I I it's a typical thing that happens but yeah but yeah that that we're not showing clearly like you cannot feel it. You know what we're doing. We we're we're telling abstractly.
Laurent Pacoud: Because you know as I'm not anformatician but
Thomas Haferlach: Yeah. Yeah.
Laurent Pacoud: everyone has an idea about what is a program editor and when
Thomas Haferlach: Yeah.
Laurent Pacoud: you show
Thomas Haferlach: Yeah.
Laurent Pacoud: to me the little line on the left saying that's all the pollination tools and everybody has it very simply thanks to one click in something I can I can begin to understand what is going on here and
Thomas Haferlach: Yeah.
Laurent Pacoud: I
Thomas Haferlach: Yeah.
Laurent Pacoud: know that
Thomas Haferlach: Yeah.
Laurent Pacoud: informatician are passing all the day on programming editors and if you
Thomas Haferlach: Yeah.
Laurent Pacoud: say to me you click on the link and you have this toolbox okay I see it I understand it
 
 
00:29:30
 
Thomas Haferlach: Yeah.
Laurent Pacoud: so
Thomas Haferlach: Yeah.
Laurent Pacoud: how to make me feel that you made me feel that guys when you showed to me the editor
Thomas Haferlach: Uhhuh. Uhhuh.
Laurent Pacoud: okay
Thomas Haferlach: Uhhuh. Uhhuh.
Laurent Pacoud: so that's first thing uh make me feel what is the AI toolbox uh to avoid the the acronym that is not that are not explained. UI slash UIX I'm not sure it is serving front end back end I don't need to hear about that uh plug and play I liked it because everyone had something in plug- and play in in its office so it is understandable and I I I I would say so so that's the technical part millions of persons are already using this system
Thomas Haferlach: Yeah.
Laurent Pacoud: millions of person only yesterday uh eis exerson exinformationian have launched requirement with this AI toolbox. Wow.
Thomas Haferlach: Yeah.
Laurent Pacoud: How
Thomas Haferlach: Yeah.
Laurent Pacoud: three million persons in the entire world. Okay. So now we shift from the technical to the financial one
Thomas Haferlach: Yeah.
 
 
00:30:32
 
Thomas Haferlach: Yeah. Yeah. Yeah. Yeah.
Laurent Pacoud: on the traction.
Thomas Haferlach: So then we're at the traction, right? Yeah.
Laurent Pacoud: No no no. I remain on the previous one because in the previous one you are treating the the technical and the financial because
Thomas Haferlach: No,
Laurent Pacoud: you
Thomas Haferlach: no, I mean I just I just said because what you were talking about just now felt like traction to me.
Laurent Pacoud: Yeah. So,
Thomas Haferlach: Um. Um.
Laurent Pacoud: so I would divide this one saying that moreover you are working on developing an ad system toolbox actually and an an ad integration toolbox and this you have nothing to show. So it is it is to be just to just to because when when when I I tried to understand all the flow of your explanation I I felt that we are mixing uh this app system uh toolbox with the technical toolbox and I
Thomas Haferlach: Yeah.
Laurent Pacoud: I
Thomas Haferlach: Yeah.
Laurent Pacoud: mean
Thomas Haferlach: Yeah. Yeah. I I I felt that confusion, too.
 
 
00:31:24
 
Laurent Pacoud: okay so so point you added value basic added value that's the technical tools toolbox make me feel it again and at the end of the day is it a new slide I don't know or is it at the end on the on the down right of the slide saying and we are now developing the ad uh tool the ad toolbox uh it is under development.
Thomas Haferlach: Yeah. Yeah. Yeah. Yeah.
Laurent Pacoud: Okay. Okay. Continue the next one.
Thomas Haferlach: So then we come to the traction slide. Actually, this is the we there's a bunch of graphs we still have um which we didn't which we didn't add here that are a little bit more that are a little bit more um data uh uh driven. This is a very strong abstraction that um Chip made. Um we're still not 100% sure, but please um yeah, let
Laurent Pacoud: Yeah.
Thomas Haferlach: let
Laurent Pacoud: Okay.
Thomas Haferlach: us know. Yeah.
Laurent Pacoud: Here one again I would come to storytelling. How do I attract the attention of my auditor?
 
 
00:32:26
 
Laurent Pacoud: How do I make him feel what I experiencing and he feels it with me. Uh look at that what what happened in the in in the last six months. I pledge
Thomas Haferlach: Mhm.
Laurent Pacoud: you have to show the curve. I pledge, I insist you have to show the curve. I've been interested myself by pollinations because you showed me
Thomas Haferlach: Yeah.
Laurent Pacoud: the curve
Thomas Haferlach: Yeah. Yeah.
Laurent Pacoud: because you explained to me that you were passing by from a little rate of daily demand to a very high rate of daily demand. And to to see that is extremely impressive. And the way I've been convinced has been exactly this point. And I regret you're not showing it like this. You just say that's traillion in the last month. I I I do not what it means actually 100 million the
Thomas Haferlach: Yeah. Yeah. Yeah.
Laurent Pacoud: brain is not gaining it but if you start from the beginning you have the story and you have
 
 
00:33:20
 
Thomas Haferlach: Uh
Laurent Pacoud: to tell the story at the beginning we launch our motor with very poor tools and we already had 30,000 in in in in this date six
Thomas Haferlach: Yeah.
Laurent Pacoud: months later we launched this thing and we had a 100,000 per day yesterday we had this look at the curve Okay. You
Thomas Haferlach: Yeah.
Laurent Pacoud: plan to show it.
Thomas Haferlach: But uh let me just show. Yeah. This is this is the curve. Maybe this one is better, right?
Laurent Pacoud: I love it.
Thomas Haferlach: Yeah.
Laurent Pacoud: Is it
Thomas Haferlach: Yeah. Yeah. Yeah.
Laurent Pacoud: the
Thomas Haferlach: Yeah.
Laurent Pacoud: demand?
Thomas Haferlach: We were we were actually planning to to show this one, but if we Yeah.
Laurent Pacoud: Okay.
Thomas Haferlach: Wait.
Laurent Pacoud: Now, now actually I arrive here and I am still don't knowing what we are talking about because when we when we say request I mean you say okay you showed me an informatic system request
Thomas Haferlach: Yeah.
Laurent Pacoud: but you haven't told me about text to image generation you haven't told me about
 
 
00:34:11
 
Thomas Haferlach: Yeah.
Laurent Pacoud: text text generation I mean
Thomas Haferlach: So
Laurent Pacoud: well
Thomas Haferlach: this
Laurent Pacoud: this our business
Thomas Haferlach: Yeah. This I mean we should maybe and then instead say media media per day. So this is just simply how many media were generated
Laurent Pacoud: yes
Thomas Haferlach: per
Laurent Pacoud: but
Thomas Haferlach: day.
Laurent Pacoud: still when you say media to someone is is think about many things actually. Are we talking about an article of newspaper? Are we talking about um a
Thomas Haferlach: Okay.
Laurent Pacoud: simple
Thomas Haferlach: Yeah. Yeah. Text, images, and audio. Pieces of text, images, and audio. Do you think that's better?
Laurent Pacoud: text generation and image generation and audio image text and audio generate AI generated media and when you when I see that you you you bring me feeling the flow you bring me feeling the wave you are facing. Okay. So now make make it me feel that put
Thomas Haferlach: Yeah. Yeah. Yeah. Yeah.
 
 
00:34:59
 
Laurent Pacoud: like that. I do like this on my computer and I see images flowing
Thomas Haferlach: You know what? You know what
Laurent Pacoud: millions
Thomas Haferlach: I'm going to
Laurent Pacoud: of images.
Thomas Haferlach: You
Laurent Pacoud: Sir,
Thomas Haferlach: know what I'm going to show here? I'm going to show here a real time um um uh uh feed of the images
Laurent Pacoud: exactly.
Thomas Haferlach: because it's it you cannot even you cannot even see one image. you know,
Elliot Fouchy: Exactly.
Thomas Haferlach: if you see the
Elliot Fouchy: This
Thomas Haferlach: real
Elliot Fouchy: is
Thomas Haferlach: time
Elliot Fouchy: cool.
Thomas Haferlach: real
Elliot Fouchy: This
Thomas Haferlach: time.
Elliot Fouchy: is super cool. I
Thomas Haferlach: Yeah.
Elliot Fouchy: think you can even have like image and text side by side.
Thomas Haferlach: Yeah. Just like um uh
Laurent Pacoud: book is what you do. Look at that
Elliot Fouchy: Let's
Laurent Pacoud: sir. Look at that sir. When I click here you see the flow of image created click click click click click click click click click click click click cl
 
 
00:35:35
 
Thomas Haferlach: Yeah. Yeah. Yeah.
Laurent Pacoud: is that and look at that. That's the text now cl. What the f*** is that? Who are those guys?
Thomas Haferlach: Yeah.
Laurent Pacoud: Make
Thomas Haferlach: Yeah.
Laurent Pacoud: me.
Thomas Haferlach: Yeah. Yeah. Yeah. Yeah. Yeah, I I like it. I like it a lot. Let's do it. Cuz I It's also
Elliot Fouchy: do it.
Thomas Haferlach: It's also It impresses me, you know, and I love even watching it by myself. So, it's something I'm kind of proud to proud to show, you know. It's got
Laurent Pacoud: And if you feel the thing when you will present it, the guy will feel it. I mean if you don't feel anything, he feel that you don't feel anything. So you don't present me anything feelings. So there is no money.
Thomas Haferlach: Yeah,
Laurent Pacoud: Okay. So I
Thomas Haferlach: absolutely.
Laurent Pacoud: continue
Thomas Haferlach: I like it. I like it.
 
 
00:36:14
 
Thomas Haferlach: Uh I think because I've been feeling exactly that that there's this what I what's been frustrating me a bit in the pictures I've been giving uh is that I feel like there's this thing I've got this very strong feeling about and I'm not able to transmit it properly and that's leaving me feeling a little bit like constrained you know like
Laurent Pacoud: I do
Thomas Haferlach: um
Laurent Pacoud: understand. I do.
Thomas Haferlach: yeah yeah yeah okay
Laurent Pacoud: Show show images, text, images, text. W what is going on? f******
Thomas Haferlach: Yeah. Yeah.
Laurent Pacoud: millions,
Thomas Haferlach: Yeah. Uh
Laurent Pacoud: millions of media generated. I'm seeing it. They are doing that. I don't I don't believe that. But
Thomas Haferlach: yeah,
Laurent Pacoud: if
Thomas Haferlach: yeah,
Laurent Pacoud: I
Thomas Haferlach: yeah.
Laurent Pacoud: will believe. Okay, we continue.
Thomas Haferlach: I like it. I like it. Um so this um the market I I'm Yeah, please uh let let me know what you thought.
 
 
00:36:59
 
Thomas Haferlach: I I actually Elliot was doing this slide until today, so I I I really wasn't very prepared for it, but I should be.
Laurent Pacoud: Yeah, I I don't know what is use ad market. Uh hearing you I did not understand. So I still don't know. I'm sorry. I don't know what is some sam. I may do have to know but I I
Thomas Haferlach: Yeah.
Laurent Pacoud: don't
Thomas Haferlach: Yeah. Yeah. Yeah. Yeah. Okay. Yeah. Yeah. Please. Yeah. Yeah. But I I mean in the next pitch I will try and make it better. But yeah, please. Yeah. Tell me what what what you think should should be done
Laurent Pacoud: So you're
Thomas Haferlach: here.
Laurent Pacoud: trying to explain me the range the space of of your market the market opportunity. Um it is complicated. I I know you haven't made a complete study on that. It is really complicated.
 
 
00:37:43
 
Laurent Pacoud: I don't know how to sensitize this. I don't know how to recommend whatever. I did not well understood what you wanted to say and I did not feel I did not measure in a very gross manner uh what we what we are talking about in terms of of of size. So I I I may not have the vocabulary. I don't know. But I
Thomas Haferlach: Yeah.
Laurent Pacoud: would
Thomas Haferlach: Yeah.
Laurent Pacoud: that's much simpler. One figure, one figure talking about the market only one.
Thomas Haferlach: Yeah. Yeah. Yeah. I in fact is that I didn't understand it. So I also couldn't in any way make you understand it.
Laurent Pacoud: Okay. Yeah, that's definitely
Thomas Haferlach: Yeah. Yeah. Yeah.
Laurent Pacoud: I felt what you felt.
Thomas Haferlach: Yeah. Yeah. Yeah. Yeah.
Laurent Pacoud: That's good. Okay, we continue.
Thomas Haferlach: Um uh the circular
Laurent Pacoud: Okay.
Thomas Haferlach: economy
Laurent Pacoud: Yeah. Circular economy.
 
 
00:38:31
 
Laurent Pacoud: I know very very well that uh I worked in circular economy. The basic of circular economy is that the west of uh value chain is used as uh an input in another value chain. Right? I confess I don't see that in pollination. I don't understand the usage of the the concept of circular economy. I don't see I mean the west of of of uh of pollination is is energy consuming I don't know what is it so I don't understand that we talk about circular economy
Thomas Haferlach: Uhhuh.
Laurent Pacoud: and um I I I didn't get the point here actually
Elliot Fouchy: The circular economy is not related to to to green. No.
Thomas Haferlach: Yeah. Yeah. I mean, we're we're talking
Laurent Pacoud: means west to input
Thomas Haferlach: Yeah.
Laurent Pacoud: you
Thomas Haferlach: Yeah.
Laurent Pacoud: use a
Thomas Haferlach: Yeah.
Laurent Pacoud: west to make another input that's that's the definition of the term. So I don't know there are many
Thomas Haferlach: Yeah.
Laurent Pacoud: persons working on circular economy but in in this field I I I don't get it.
 
 
00:39:31
 
Laurent Pacoud: Maybe there is a specific definition in informatic systems. Maybe but I I
Thomas Haferlach: Yeah.
Laurent Pacoud: don't know.
Thomas Haferlach: Yeah. I think that what people talk about more is the fly flywheel. Um which means like something that is self self-perpetuating. Um and I think we we actually we may be using circular economy wrong here. I was also in doubt about this word. Um um we really need to be sure about it. I agree. And
Laurent Pacoud: And what what what is the essential message of of this slide? Give it to me in one sentence.
Thomas Haferlach: Okay. So, we have a we we have a large we have a large supply of of creators um who are who are building um um viral apps. They have a lot they have customer they have a lot of customers. Um um these we are we are providing these creators with with AI infrastructure. Um, this is allowing them to reach more customers, which is in turn bringing us more ad revenue, which allows us to allow to to grow our our um our creator base and which has this kind of self-perpetuating um um um C circle going
 
 
00:40:43
 
Laurent Pacoud: You
Thomas Haferlach: on.
Laurent Pacoud: already said that to me in the previous slide.
Thomas Haferlach: Yeah.
Laurent Pacoud: All
Thomas Haferlach: Yeah.
Laurent Pacoud: All of what you said, you already said to me. I mean to to be conselled. To be conselled.
Thomas Haferlach: Yeah. Yeah. I think we I think we can we we can join these SL these slides. I agree.
Laurent Pacoud: Okay. Uh, and this one is is unreadable. Uh, you you lose me when
Thomas Haferlach: Yeah.
Laurent Pacoud: it appears. Lose me. That's automatic. There is so much information here to see. I can I cannot hear you anymore. So, the slide is is is unreadable and and you lose me directly. That's instant instantly because I cannot
Thomas Haferlach: Yeah.
Laurent Pacoud: join what you say with what I see doesn't work.
Thomas Haferlach: Yeah, I'm
Laurent Pacoud: So, everything you said here, I did not get it.
Thomas Haferlach: Yeah, I'm aware of that. Um, it's it's a very bad slide.
 
 
00:41:28
 
Laurent Pacoud: Okay. But
Thomas Haferlach: Yeah.
Laurent Pacoud: I I
Thomas Haferlach: Yeah.
Laurent Pacoud: don't know.
Thomas Haferlach: Yeah.
Laurent Pacoud: I mean it looks like you are you are complexifying your explanation. You don't want to complexify. You want to simplify simplify simplify. So you don't want to give details remains on the most basic. Actually when
Thomas Haferlach: Yeah.
Laurent Pacoud: you say to someone when someone says you have 12 minutes 10 minutes to make my your your your presentation someone that make it in seven minutes will have more its attention than others
Thomas Haferlach: Yeah. Yeah.
Laurent Pacoud: because
Thomas Haferlach: Yeah. I also think we should do less. We should do less rather
Elliot Fouchy: Yes.
Thomas Haferlach: than
Elliot Fouchy: Yes.
Thomas Haferlach: more.
Elliot Fouchy: When you pitched it, I was speaking, you could cut by half what you say in every slide.
Thomas Haferlach: Yeah. Yeah. Yeah.
Laurent Pacoud: okay so this I
Thomas Haferlach: I agree.
Laurent Pacoud: would cons
Thomas Haferlach: Yeah.
Laurent Pacoud: let's
Thomas Haferlach: Yeah.
 
 
00:42:07
 
Thomas Haferlach: Yeah.
Laurent Pacoud: really really straight to point
Thomas Haferlach: Yeah.
Laurent Pacoud: you know you have this problem for road map from is actually uh I don't know if we see the same thing road map from is um over overlapped with
Thomas Haferlach: Yeah,
Laurent Pacoud: activation
Thomas Haferlach: there's a
Elliot Fouchy: Yeah, that's a technical problem that I will resolve. This is a
Laurent Pacoud: yeah I mean your slide guys has to be perfect like there is no little error in the slide no one no one no little one
Elliot Fouchy: Yes.
Laurent Pacoud: uh So I mean this slide is too complicated as well. Uh or you present to me a scheme or you present to me a one to three but the two should match such
Thomas Haferlach: Yeah.
Laurent Pacoud: a perfect way that in one shot with my eye I understand that's not the case. The scheme is telling me some things uh which actually is not really evident to understand at all because the the arrows goes in in
Thomas Haferlach: Yeah. Yeah. Yeah.
Laurent Pacoud: sense my brain stops to think at the first arrow and the other part is is is is hard to read as well.
 
 
00:43:18
 
Laurent Pacoud: So I have to those two things you're telling me um I don't get the information. What what do you mean? What do you mean? What what do you want to say to me?
Elliot Fouchy: Yeah.
Laurent Pacoud: So
Elliot Fouchy: Yeah.
Thomas Haferlach: Yeah. Yeah.
Laurent Pacoud: come back to one slide one message here. You want to say uh we have we have a strategy in three steps. The first steps is the 12 months period that comes that will happen this this this. The second one is uh the two to three years is this this this and the third one is our vision. Yeah I
Thomas Haferlach: Yeah.
Laurent Pacoud: like it.
Thomas Haferlach: Yeah.
Laurent Pacoud: One
Thomas Haferlach: Yeah.
Laurent Pacoud: to three just that and schemes that are not extremely clear should not be there.
Thomas Haferlach: Yeah. Yeah. Yeah. Yeah. Yeah. Totally. Totally. We have to uh simplify a lot. Yeah.
Laurent Pacoud: Okay, passing.
Thomas Haferlach: Yeah.
 
 
00:44:01
 
Laurent Pacoud: I agree with you. We you should not in a 10 minutes presentation and risk analysis doesn't come a risk
Thomas Haferlach: Yeah.
Laurent Pacoud: analysis is uh in a detailed
Thomas Haferlach: Yeah.
Laurent Pacoud: uh
Thomas Haferlach: Yeah. Yeah.
Laurent Pacoud: presentation document. It it does not his uh so
Thomas Haferlach: Um um the the reason actually the fact is that we're going to we're going to have um a tw a 12m minute pitch and a a 20 minute Q&A afterwards. So actually this slide is supposed to be in the you know we I'm sure we're going to reach it you know because we're going to talk about risk in the 20 minutes Q&A uh and um so this is one this is uh the first one let's say we prepared to to in order to answer followup questions
Laurent Pacoud: Yeah,
Thomas Haferlach: right
Laurent Pacoud: exactly.
Thomas Haferlach: that's that's the idea of this slide and it kind of slipped in it slipped in um uh because I was actually also testing seeing if we could in include it in the main pitch But actually I came to the conclusion um that this should go into the supplementary material,
 
 
00:45:03
 
Laurent Pacoud: Okay. Okay.
Thomas Haferlach: you know,
Laurent Pacoud: So, you have all your slide
Thomas Haferlach: but
Laurent Pacoud: and then you
Thomas Haferlach: but
Laurent Pacoud: have a
Thomas Haferlach: I
Laurent Pacoud: questions and you are prepared with
Thomas Haferlach: Yeah. Yeah. Yeah. With with all of these um Elliot has also prepared quite a lot of kind of supplementary material already. Um, and I think um we we could also um um bring some of this into the pitch in forms of in the form of like what what are we going to spend our money on, right? and we're going to spend our money on reducing these risks, you know, then there's already the the solution, you know, because if we just present the risks, uh that's maybe not the tactically most clever thing. But
Laurent Pacoud: Definitely.
Thomas Haferlach: but I
Laurent Pacoud: Yeah.
Thomas Haferlach: feel like um because some sometimes I have a we we also when we explain like what are we going to use the money on, I'm I'm a little bit I don't have a very clear strategy yet.
 
 
00:45:51
 
Thomas Haferlach: Of course, we're going to improve that. But I think mitigating these risks is is probably the main um let's say the main driver or one of the main drivers, right, be behind es especially this first part of of of money we will get because um we've got a business that's working well. It's growing, right? So what what's um what's the highest priority is is is remove all risks um um that that that stand in the way from it failing in in the near future and then of course also the longer term. Yeah.
Laurent Pacoud: Okay. Uh so we agree achieve that at the end. Uh and then the team we talked about it simple
Thomas Haferlach: Yeah.
Laurent Pacoud: to think with the image big uh big big names the you have the vision I think. Huh? Okay.
Thomas Haferlach: Yeah. Yeah. Yeah. Yeah.
Laurent Pacoud: Then you
Thomas Haferlach: Yeah.
Laurent Pacoud: pass that point concept point concept point concept.
Thomas Haferlach: Yeah.
Laurent Pacoud: or more I I see my eye understand what I see in one shot and then I need to hear you to understand step by step that's the way we do presentation you
 
 
00:46:58
 
Thomas Haferlach: Yeah.
Laurent Pacoud: just have concepts and when I see it I understand everything of the slide and I get question in 1.5 seconds and the explanation of the guy gives me the explanation of the question I have in mind that that's
Thomas Haferlach: Yeah,
Laurent Pacoud: how okay
Thomas Haferlach: yeah, yeah, yeah, yeah, yeah, yeah.
Laurent Pacoud: can
Thomas Haferlach: Totally.
Laurent Pacoud: we come back please can we come on
Thomas Haferlach: What?
Laurent Pacoud: the on the camber China
Thomas Haferlach: This was here. I just removed it.
Laurent Pacoud: that was here. Yeah.
Thomas Haferlach: Yeah.
Laurent Pacoud: Yeah. Yeah. Yeah. Yeah. Okay. Um when you you said China, US and India are our big uh players. I mean
Thomas Haferlach: Yeah.
Laurent Pacoud: um I would I would avoid to say that like this because it is important to say that EU is a big player in your system.
Thomas Haferlach: Yeah. Yeah. Yeah. Yeah.
Laurent Pacoud: Uh
Thomas Haferlach: I think I think the EU is actually quite big when you take it as a whole.
 
 
00:47:47
 
Thomas Haferlach: Um um we we we've been often seeing the countries separately. So then they are not so uh not so big. Um but um I also think the EU should be in there. I mean we're pitching from the EU. Um yeah. Yeah.
Laurent Pacoud: Yeah, you can say EU
Elliot Fouchy: a couple minutes.
Laurent Pacoud: EU countries have been historically the biggest user. Uh but recently we've seen uh the world growing in our in our demand uh in our demand market.
Thomas Haferlach: Yeah.
Laurent Pacoud: And
Thomas Haferlach: Yeah.
Laurent Pacoud: when you talked about China, you talked about deepse uh it was impossible to understand the link. uh would advise avoiding that. It is extremely technical. Uh it could brings lots of question. Um
Thomas Haferlach: Okay. Okay. I thought I thought um because deepseek like everyone's been reading deepseek in the news like even my parents talk about it. So I thought um deepseek you know the fact that um deepseek and pollinations are connected is potentially um interesting but maybe I shouldn't say it or maybe I should present it differently.
 
 
00:48:53
 
Laurent Pacoud: I mean that that brings lots of technical questions.
Thomas Haferlach: Uhhuh.
Laurent Pacoud: Uh in a 10-minute speech I would avoid uh in
Thomas Haferlach: Yeah.
Laurent Pacoud: the question and response. Why not? Because it could come.
Thomas Haferlach: Yeah. Yeah.
Laurent Pacoud: Okay. And uh final thing uh how to kill the hum Thomas. How to kill the hum? You may not have any um in your final presentation. So I tell you how to kill the hum. You need to know everything by heart. Everything. Every single word. Every single word. You know everything
Thomas Haferlach: Yeah,
Laurent Pacoud: by
Thomas Haferlach: yeah, yeah, yeah, yeah,
Laurent Pacoud: by her is it means that you don't have the slide. You can say it like this to yourself
Thomas Haferlach: yeah.
Laurent Pacoud: in one shot. It's thinking about it. This is by heart as you were doing poetry when you were 10. I do do that you know. So yeah, you have a huge work to make it to make it happen, but you have to lend on your final structure. You still have lots of work to to work on the final structure that makes you totally believe that what you're doing is perfect and
Thomas Haferlach: Yeah.
Laurent Pacoud: that's not the case. So you have to finalize your l do your your structure be totally sure that it's perfect is flowing intellectually flowing and then you're learning by her. You have lots of work, man.
Thomas Haferlach: Yeah. Yeah. Absolutely. I'm aware of it.
Laurent Pacoud: okay, guys, if you want to make it to me again, on on Tuesday, or Wednesday night, maybe I have lots of work, you can make it
Thomas Haferlach: Okay. Okay, I'd love to. Let's keep
Laurent Pacoud: Okay.
Thomas Haferlach: yeah yeah, let's keep Tuesday night in mind if you
Laurent Pacoud: Okay. I love it, guys. That's good. Please fight. You have lots of things to do.
Thomas Haferlach: Okay.
Laurent Pacoud: Ciao.
Thomas Haferlach: Thank you.
 
 
Transcription ended after 00:50:44

This editable transcript was computer generated and might contain errors. People can also change the text after it was created.

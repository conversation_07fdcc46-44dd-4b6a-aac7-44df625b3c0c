
DESCRIPTION >
    -- v2: Added usage detail columns
    Store LLM events with usage metrics, costs, and metadata

SCHEMA >
    `timestamp` DateTime `json:$.start_time`,
    `organization` String `json:$.proxy_metadata.organization` DEFAULT '',
    `project` String `json:$.proxy_metadata.project` DEFAULT '',
    `environment` String `json:$.proxy_metadata.environment` DEFAULT '',
    `user` String `json:$.user` DEFAULT 'unknown',
    `chat_id` String `json:$.proxy_metadata.chat_id` DEFAULT '',
    `message_id` String `json:$.message_id`,
    `model` LowCardinality(String) `json:$.model` DEFAULT 'unknown',
    `model_used` Nullable(String) `json:$.model_used`,
    `prompt_tokens` UInt16 `json:$.response.usage.prompt_tokens` DEFAULT 0,
    `completion_tokens` UInt16 `json:$.response.usage.completion_tokens` DEFAULT 0,
    `cached_tokens` UInt16 `json:$.response.usage.cached_tokens` DEFAULT 0,
    `total_tokens` UInt16 `json:$.response.usage.total_tokens` DEFAULT 0,
    `response_time` Float32 `json:$.standard_logging_object_response_time` DEFAULT 0,
    `duration` Float32 `json:$.duration` DEFAULT 0,
    `cost` Float32 `json:$.cost` DEFAULT 0,
    `exception` String `json:$.exception` DEFAULT '',
    `traceback` String `json:$.traceback` DEFAULT '',
    `response_status` LowCardinality(String) `json:$.standard_logging_object_status` DEFAULT 'unknown',

    `messages` Array(Map(String, String)) `json:$.messages[:]` DEFAULT [],
    `response_choices` Array(String) `json:$.response.choices[:]` DEFAULT [],
    `proxy_metadata` String `json:$.proxy_metadata` DEFAULT '',
    `provider` LowCardinality(String) `json:$.provider` DEFAULT 'unknown',

    `llm_api_duration_ms` Float32 `json:$.llm_api_duration_ms` DEFAULT 0,
    `end_time` DateTime `json:$.end_time`,
    `id` String `json:$.id` DEFAULT '',
    `stream` Bool `json:$.stream` DEFAULT false,
    `call_type` LowCardinality(String) `json:$.call_type` DEFAULT 'unknown',
    `api_key` String `json:$.api_key` DEFAULT '',
    `log_event_type` LowCardinality(String) `json:$.log_event_type` DEFAULT 'unknown',
    `cache_hit` Bool `json:$.cache_hit` DEFAULT false,
    `response` String `json:$.response` DEFAULT '',
    `response_id` String `json:$.response.id`,
    `response_object` String `json:$.response.object` DEFAULT 'unknown',

    `embedding` Array(Float32) `json:$.embedding[:]` DEFAULT [],

    `usage_completion_tokens_details_text_tokens` UInt16 `json:$.response.usage.completion_tokens_details.text_tokens` DEFAULT 0,
    `usage_completion_tokens_details_audio_tokens` UInt16 `json:$.response.usage.completion_tokens_details.audio_tokens` DEFAULT 0,
    `usage_completion_tokens` UInt16 `json:$.response.usage.completion_tokens` DEFAULT 0,
    `usage_prompt_tokens_details_text_tokens` UInt16 `json:$.response.usage.prompt_tokens_details.text_tokens` DEFAULT 0,
    `usage_prompt_tokens_details_audio_tokens` UInt16 `json:$.response.usage.prompt_tokens_details.audio_tokens` DEFAULT 0,
    `usage_prompt_tokens_details_cached_tokens` UInt16 `json:$.response.usage.prompt_tokens_details.cached_tokens` DEFAULT 0,
    `usage_prompt_tokens` UInt16 `json:$.response.usage.prompt_tokens` DEFAULT 0,

ENGINE "MergeTree"
ENGINE_PARTITION_KEY "toYYYYMM(timestamp)"
ENGINE_SORTING_KEY "timestamp, organization, project, environment, user, chat_id"
ENGINE_PRIMARY_KEY "timestamp, organization, project"

FORWARD_QUERY > 
  SELECT 
    timestamp, organization, project, environment, user, chat_id, message_id, 
    model, model_used, provider,
    prompt_tokens, completion_tokens, cached_tokens, total_tokens,
    response_time, duration, cost,
    response_status, call_type, log_event_type, cache_hit,
    llm_api_duration_ms, end_time, id, stream,
    usage_completion_tokens_details_text_tokens,
    usage_completion_tokens_details_audio_tokens,
    usage_completion_tokens,
    usage_prompt_tokens_details_text_tokens,
    usage_prompt_tokens_details_audio_tokens,
    usage_prompt_tokens_details_cached_tokens,
    usage_prompt_tokens
  FROM llm_events
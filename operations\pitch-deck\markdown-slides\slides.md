---
layout: two-cols-header
---

# pollinations.ai - Docs

::left::

- [📝 One-Pager](/16)
- [🎤 Pitch Deck](/18)

<br>

- [📑 Executive Summary](/2)
- [🔭 Vision & Mission](/3)
- [💰 Business Model](/4)
- [💹 Financial Model](/5)
- [🛣️ Roadmap](/6)
- [👥 Team](/7)

::right::

- [📈 Market Opportunity](/8)
- [🌍 Ecosystem Analysis](/9)
- [🤝 Add Partners](/10)
- [⚠️ Risk Register](/11)
- [🥊 Competitive Landscape](/12)
- [🚀 KPI & Traction Metrics](/13)
- [🏗️ Tech Architecture](/14)

<br>

- [🔍 Due Diligence](/15)


---
src: ./docs/01-executive-summary.md
---

---
src: ./docs/02-vision-mission.md
---

---
src: ./docs/03-business-model.md
---

---
src: ./docs/05-financial-model.md
---

---
src: ./docs/04-roadmap.md
---

---
src: ./docs/10-team.md
---

---
src: ./docs/03-market-opportunity.md
---

---
src: ./docs/03-blooming-ecosystem.md
---

---
src: ./docs/14-ad-partners.md
---

---
src: ./docs/11-risk-register.md
---

---
src: ./docs/09-competitive-landscape.md
---

---
src: ./docs/12-traction-metrics.md
---

---
src: ./docs/07-tech-architecture.md
---

---
src: ./docs/16-due-diligence.md
---

---

<img src="/media/pollinations.ai - One Pager (May 2025).png" alt="Pollinations.ai One Pager" class="w-full rounded-lg shadow-lg">

---

# **Pitch Deck**

---
class: text-center flex flex-col justify-center items-center h-full
---

# ![Pollinations.ai Logo](/media/pollinations-ai-logo.png)

## Spark Creation, Unlock Ad Revenue
#
<div class="bg-yellow-300 hover:bg-yellow-400 text-black font-bold py-2 px-4 rounded-2xl shadow-lg inline-block transition-colors duration-200">
  <a href="https://pollinations.ai" class="no-underline">🐝 Start building with pollinations.ai</a>
</div>

<!--
* Hi everyone, I'm Thomas from **Pollinations.ai**
* AI allows **Everyone to be a coder**. Interacting with ChatGPT is **coding in natural language**.
* A prompt is the **source code** for AI applications.
* "We provide the **AI platform** for our vibrant community to easily build **gen AI apps**."
* "We partner with specialized **Ad Tech** companies to **monetize** the unique, untapped **ad inventory** provided by these apps."
* "Today I'll show you our **phased path** to **shared success**"
-->

---
layout: two-cols-header
---

# <span class="bg-red-600 text-white p-2 rounded">**🔗 Problem : Fragmented, Unfunded AI**</span>

::left::

<img src="/media/problem_friction.png" alt="Problem: Friction" class="w-1/4 rounded-lg shadow-lg" />

#

# **Indie/Vibe Coder** 
#
### ⚙️ **Complex AI Access & High Costs**
#
### 💰 **Monetization Blindspot for AI Content**

<br>

::right::

<img src="/media/problem_reach.png" alt="Problem: Reach" class="w-1/4 rounded-lg shadow-lg" />

#

# **Advertisers & Ad Tech**
#
### 🍀 **Fragmented & Unique AI Inventory**
#
### 📊 **Generic Ads Don't Fit AI Context**

<br>

<!--
* "The GenAI boom creates distinct problems:"
* **"For Indie Coders (left):**
  * "**Complex AI access**, high **costs**, limited model variety.**Stifles creation**."
  * "If they build, a **monetization blindspot**. AI-specific tools **lacking**."
* **"For Advertisers & Ad Tech (right):**
  * "An **untapped AI frontier**, but **fragmented AI inventory** is hard to access."
  * "And **generic ads don't fit AI context**, leading to **poor engagement**."
* "**Pollinations solves** the **disconnect** between **difficult creation & access** vs. **ineffective monetization & reach**"
-->

---
layout: two-cols-header
---

# <span class="bg-green-600 text-white p-2 rounded">**✨ AI Creation Meets Monetization**</span>

<br> 

::left::

# 👩‍💻 Indie/Vibe Coder
### **→ Build & Monetize**
#
<div class="flex justify-left space-x-4 mb-2">
  <img src="/media/logo_koboldai.png" alt="KoboldAI Logo" class="h-10">
  <img src="/media/logo_lobe.png" alt="Lobe Logo" class="h-10">
  <img src="/media/logo_quen.png" alt="Quen Logo" class="h-10">
</div>

<br>
<br>

# 🏢 Advertisers
### **→ Reach AI-Natives**

<br>

<div class="flex justify-left space-x-4 mb-2">
  <img src="/media/ad-partner-garlic.png" alt="Garlic Partner" class="h-8">
  <img src="/media/ad-partner-nexad.png" alt="Nexad Partner" class="h-8">
</div>
  <img src="/media/logo_google_ad_sense.png" alt="Ad-sense Partner" class="h-12">


::right::

<div class="bg-blue-100 p-4 rounded-lg shadow-lg mb-2 text-center">
<div class="font-bold text-blue-600 text-xl">Developers Create Apps with AI Media</div>
</div>
<div class="text-center font-bold text-xl">↓</div>

<div class="bg-green-100 p-4 rounded-lg shadow-lg mb-2 text-center">
<div class="font-bold text-green-600 text-xl">Apps Attract & Engage Users</div>
</div>
<div class="text-center font-bold text-xl">↓</div>

<div class="bg-purple-100 p-4 rounded-lg shadow-lg mb-2 text-center">
<div class="font-bold text-purple-600 text-xl">pollinations.ai Integrates Ad Solutions</div>
</div>
<div class="text-center font-bold text-xl">↓</div>

<div class="bg-amber-100 p-4 rounded-lg shadow-lg mb-2 text-center">
<div class="font-bold text-amber-600 text-xl">Revenue Generated & Shared</div>
</div>

<!--
**Solution - Connecting Creation with Monetization**

**Our Core Value Proposition:**
* We take creators from **idea to income** with **zero upfront cost** - this is crucial

**For Indie/Vibe Coders:**
* We make **AI integration effortless** while providing a **clear path to profit**
* Their apps can **self-fund** their own compute costs through our ads!
* Un 2026, we'll launch **Nectar** with **50/50 revenue sharing** for top performers

**For Advertisers:**
* We're opening access to these previously **untapped AI-native audiences** through **context-aware dynamic ad placements**
* This drives much **higher engagement** than traditional ad formats

**In short:** We build the **infrastructure**, partner with **ad experts**, and **everyone shares** in the success
## End
-->

---
layout: two-cols
---

# 🚀 **Developer Adoption, It's Real.**

<img src="/media/git-star-history.png" alt="GitHub Star History" class="w-6/7 rounded-lg shadow-lg">

::right::

<div class="mx-2">
  <video class="w-1/1 rounded-lg shadow-lg" autoplay loop muted controls onloadedmetadata="this.currentTime = Math.random() * this.duration; this.playbackRate = 2.0;">
    <source src="/media/what-do-we-do_slideshow-3.mp4" type="video/mp4">
  </video>

  <div class="mb-4"></div>

  <video class="w-1/1 rounded-lg shadow-lg" autoplay loop muted controls onloadedmetadata="this.currentTime = Math.random() * this.duration; this.playbackRate = 2.0;">
    <source src="/media/what-do-we-do_slideshow-4.mp4" type="video/mp4">
  </video>
</div>

---

# **🎮 Creator Story: Pokémon Image Generation**

<div class="flex">
  <div class="w-4/10 pt-4 pr-4 flex flex-col justify-start">
    <div class="text-3xl mb-8">
      <p>Watch how <strong>easy</strong> it is<br>
      <br>
      To <strong>build</strong> engaging<br>
      <br>
      AI applications <br>
      <br>
      <strong>in seconds</strong> ⚡️</p>
    </div>
    <div class="bg-gradient-to-r from-purple-500 to-purple-400 text-white font-bold py-3 px-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 inline-block transform hover:scale-105" style="max-width: 70%;">
      <p class="m-0 text-lg">✨ Turn your AI dreams into reality in minutes with <span class="text-yellow-300">pollinations.ai</span> 🚀</p>
    </div>
  </div>
  
  <div class="w-6/10 ml-auto">
    <video class="w-full rounded-lg shadow-lg" autoplay loop muted controls onloadedmetadata="this.playbackRate = 2.0;">
      <source src="/media/pokemon-image-gen-2x-compressed.mp4" type="video/mp4">
    </video>
  </div>
</div>

<!--
## Creator Story
* This is a real example of how creators use our platform
* They can quickly build engaging AI applications with minimal effort
* Our infrastructure handles all the complexity, letting them focus on creativity
* This creates unique ad inventory opportunities in these engaging experiences
-->


<!--
## Product Showcase Apps Cohort
*   Community builds diverse apps on our infra.
*   Examples: (For each video, briefly state)
    *   "[App Type 1]: [Key user], [Pollinations tech used]."
    *   "[App Type 2]: [Key user], [Pollinations tech used]."
    *   (Shows platform power & creativity).
## End
-->

---

# **☁️ Our Cloud Architecture**

<div class="bg-pink-100 p-2 mb-5 rounded-lg shadow-lg inline-block">
  <h3 class="m-0"><strong>Scalable Infrastructure</strong> powering our ecosystem 🔌</h3>
</div>

```mermaid
%%{init: {'theme': 'base', 'themeVariables': {'fontSize': '14px', 'nodeSpacing': 10, 'rankSpacing': 25}}}%%
flowchart TD
    subgraph Clients[<b style='font-size:18px'>Clients</b>]
    Q["<b>Messaging Platforms</b>"] 
    N["<b>30+ Apps</b>"] 
    A["<b>Website</b>"] 
    R["<b>AI Agents</b>"] 
    AI["<b>AI Assistants</b>"]
    end
    
    subgraph APIs[<b style='font-size:18px'>APIs</b>]
    L1["<b>image.api</b>"]
    L2["<b>text.api</b>"]
    MCP["<b>MCP Server</b>"]
    end
    
    subgraph Services[<b style='font-size:18px'>Services</b>]
    B["<b>Image Generation</b>"]
    F["<b>Prompt Enhancement</b>"]
    D["<b>FLUX Models</b>"]
    P["<b>News/Search</b>"]
    end
    
    subgraph Providers[<b style='font-size:18px'>Providers</b>]
    SC["<b>Scaleway</b>"]
    DS["<b>Deepseek</b>"]
    G["<b>Azure</b>"]
    CF["<b>Cloudflare AI</b>"]
    end
    
    subgraph Models[<b style='font-size:18px'>Models</b>]
    QW["<b>Qwen</b>"] 
    LL["<b>Llama</b>"]
    OP["<b>OpenAI</b>"] 
    CL["<b>Claude</b>"]
    MI["<b>Mistral</b>"] 
    CFL["<b>Llama Models</b>"]
    end
    
    Q & N & A & R --> L1
    N & A --> L2
    AI --> MCP --> L1
    L1 --> B --> F --> D
    L2 --> P & SC & DS & G & CF
    SC --> QW & LL
    G --> OP & CL
    CF --> MI & CFL
    
    %% Styling
    classDef default fill:#FCE7F3,stroke:#DB2777,stroke-width:1px,color:#831843,rx:10,ry:10
    classDef container fill:#ECFDF5,stroke:#059669,stroke-width:2px,color:#065F46,rx:15,ry:15
    
    class Q,N,A,R,AI,L1,L2,MCP,B,F,D,P,SC,DS,G,CF,QW,LL,OP,CL,MI,CFL default
    class Clients,APIs,Services,Providers,Models container
```

<!--
## Our Architecture
* This is the technical foundation that powers everything you've seen
* Our architecture is designed for scale, reliability, and flexibility
* We connect diverse client applications to the best AI models through our services
* This modular design allows us to integrate new models and services quickly
-->
---
layout: two-cols
---

# 💬 **Pixpal.Chat** (web app)

<br>
<br>

<div class="relative">
  <div v-click-hide="1" class="absolute top-0 left-0">
    <div class="text-4xl font-bold">
      <span class="italic">#1 on Bing</span>
    </div>
    <div class="text-3xl">
      and various assistants for
    </div>
    <div class="text-3xl font-bold">
      AI image generation 🖼️
    </div>
  </div>

  <div v-click="1" class="absolute top-0 left-0">
    <div class="text-4xl font-bold">
      <span class="italic">Contextual & Personalized</span>
    </div>
    <div class="text-3xl">
      Ad Integration
    </div>
    <div class="text-3xl font-bold">
      🎯
    </div>
  </div>
</div>

<div style="margin-top: 180px" class="flex gap-4 items-center">
  <img src="/media/logo_mistral.png" alt="Mistral AI Logo" class="h-15">
  <img src="/media/logo_llama.png" alt="Meta AI Logo" class="h-20">
</div>

::right::

<div class="flex justify-center items-center relative h-full">
  <img v-click-hide="1" src="/media/pixpal-1.png" class="w-3/3 rounded-lg shadow-lg absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" alt="Pixpal AI chat with images">
  <img v-click="1" src="/media/pixpal-2.png" class="w-3/3 rounded-lg shadow-lg absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" alt="Pixpal AI chat with contextual ad">
</div>

<br>
<br>

<!--
## Product Showcase - Ad Integration
* Pixpal, a community app using our infra, is **#1 on Bing** for 'AI Chat with Images'
* Here's our AI monetization: User asks about Greece in Pixpal
* Pollinations' AI **analyzes context**. Our system, with ad partners like **Garlic/Nex.ad**, delivers a **hyper-contextual ad**
* Note the **dynamically AI-generated image** (e.g., Santorini) and **targeted partner offer** (e.g., TalkPal)
* This approach is **smart**, **non-intrusive**, and **incredibly effective**. This is how creators will monetize
## End
-->

---
layout: two-cols
---

# **🎮 Roblox Case Study**

<div class="bg-pink-400 text-white p-2 rounded-lg inline-block mb-4">
  <h2 class="m-0"><strong>Revenue Share Success</strong></h2>
</div>

<br>
<br>

<div class="text-4xl font-bold">
  <span class="italic">#1 on Roblox</span>
</div>
<div class="text-3xl">
  🏆 AI Game Category
</div>

<br>

<div class="text-4xl font-bold">
  <span class="italic">2M+</span>
</div>
<div class="text-3xl">
  👥 Monthly Active Users
</div>

<br>
<br>

<div class="flex gap-4 items-center">
  <img src="/media/roblox-logo.png" alt="Roblox Logo" class="w-20">
  <img src="/media/logo_llama.png" alt="Roblox Logo" class="h-20">
</div>

::right::

<div class="flex justify-center items-center h-full">
  <video class="w-4/4 rounded-lg shadow-lg" autoplay loop muted>
    <source src="/media/roblox_video.mov" type="video/mp4">
  </video>
</div>

<!--
## Roblox Case Study - Real Revenue Sharing in Action
* Let me share a powerful **real-world example** of our model working today
* This is "AI Character RP" - currently the **#1 AI game** on Roblox
* It's reached an impressive **2M monthly active users** - real validation of what our infrastructure enables
* The monetization happens through the **Roblox platform** itself
* We've established a **custom revenue-sharing arrangement** with the developer
* This is essentially **piloting our Nectar model** that we'll roll out more broadly
* It proves our concept works in the **real world**, with **real users** and **real revenue**
## End
-->

---
layout: two-cols-header
---

# <span class="bg-blue-600 text-white p-2 rounded">**🚀 Traction: Flywheel Already in Motion**</span>

::left::

## 👥 **3M+** Mo Active End-Users
## 🎨 **100M+** Media Gen/Month
#
<div class="bg-gray-200 text-black p-2 rounded-lg inline-block">
  <h3 class="m-0">📈 <strong>30%</strong> Month-o-Month Growth</h3>
</div>

<br>
<br>

## 🚀 **300+** Apps Live
## ⚡ **2+** Built Daily
#

<div class="bg-gray-200 text-black p-2 rounded-lg inline-block">
  <h3 class="m-0">💬 <strong>13K+</strong> Discord Community</h3>
</div>

<br>
<br>

::right::

<div class="flex justify-center items-center">
  <img src="/media/traction-media-generated-6mo.png" alt="Media Generated Growth" class="w-8/8 rounded-lg shadow-lg">
</div>

#

<div class="text-center">📊 Media Generated per Day</div>

<!--
## Traction - Our Flywheel is Already Spinning
* Our flywheel is not just theoretical - it's **already in full motion**
* We've reached **3M+ monthly active users** generating over **100M pieces of media** per month
* We're seeing consistent **organic growth of 30%** month-over-month
* Our community has built over **300 apps**, with **2+ new ones** launching daily
* **Text generations** have actually surpassed images - now at **1.3M per day**
* Our ad pilot program has already delivered **14M impressions** - and we're learning fast
* This traction **validates our model** and shows we're solving a **real need** in the market
## End
-->

---
layout: two-cols-header
---

# <span class="bg-orange-600 text-white p-2 rounded">**🌍 $20 B of Ads, One Drop‑In Line of Code**</span>

::left::

<br>

## **€218 B** TAM 🌐
### Mobile + Web Ad Spend **Outside** Walled Gardens (2025)

<br>

## **€20 B** SAM 📣
### Ad spend on Indie Apps

<br> 

<div class="bg-gray-100 p-4 rounded-lg inline-block">

# **€768 M** SOM 💻
### **3.8 %** of SAM → **massive runway**
</div>



::right::

<div class="flex justify-center items-center">
  <img src="/media/tam-sam-som.png" alt="TAM SAM SOM Visualization" class="w-6/7 rounded-lg shadow-lg">
</div>



<!--
### Market
*   TAM: €218B (Mobile/Web ads outside giants).
*   SAM: €20B (Indie App Ad Spend reachable via SDKs).
*   SOM: €768M (Our potential from AI-native apps).
*   Our infra + ad partners tap this growing market.
*   **We're Unity Ads for Gen‑AI micro‑apps** - Unity scaled ads from 0 → $1.6 B in revenue with similar developer base.
  
#### In Short
- We’re targeting a €218B ad market — mobile and web ads outside the big platforms.
- Our real focus is a €20B slice: indie apps that can use SDKs like ours.
- From that, we aim to capture €768M through Gen-AI micro-apps.
- Think Unity Ads — they scaled from 0 to $1.6B. We’re doing the same, but for AI.

#### Longer
- The total market we’re going after is huge — €218 billion in mobile and web ad spend, outside of the major platforms like Meta and TikTok.
- Our serviceable market is about €20 billion — that’s the indie apps where Unity-style SDKs can realistically plug in and monetize.
- From that, we’ve scoped out a clear beachhead: €768 million — that’s our share of AI-native apps, where we believe we can have the strongest impact.
- We are confident because we’re doing for Gen-AI micro-apps what Unity Ads did for mobile games — and they went from zero to over $1.6 billion in ad revenue, serving a very similar type of long-tail developer base.
- We’ve got the infrastructure and getting ready with ad-tech partners in place to tap this fast-growing space.

## End
-->

---
layout: two-cols
---

# <span class="bg-purple-600 text-white p-2 rounded">**💰 From Prompt to Profit**</span>

<br>
<br>
<br>

<div class="bg-purple-100 p-4 rounded-lg shadow-lg mb-4 relative" style="min-height: 240px;">
  <div v-click-hide="1" class="absolute top-0 left-0 right-0 bottom-0 p-4">
    <div class="font-bold text-3xl">💼 Example: Mirexa app</div>
    <div class="text-2xl">(Text/Image Generator)</div>
    <div class="grid grid-cols-2 gap-4 mt-4">
      <div>
        <div class="font-semibold text-xl">720K</div>
        <div class="text-base">Media/Month</div>
      </div>
      <div>
        <div class="font-semibold text-xl">144K</div>
        <div class="text-base">Ad Impressions/Month</div>
      </div>
      <div>
        <div class="font-semibold text-xl">€5</div>
        <div class="text-base">eCPM</div>
      </div>
      <div>
        <div class="font-semibold text-xl text-green-600">€324</div>
        <div class="text-base">Monthly Creator Revenue</div>
      </div>
    </div>
  </div>
  
  <div v-click="1" class="absolute top-0 left-0 right-0 bottom-0 p-4">
    <div class="font-bold text-3xl">💰 Key Unit Economics</div>
    <div class="text-2xl">(per 1k Media Generations – 2027)</div>
    <div class="mt-4">
      <div class="flex justify-between items-center mb-2">
        <div class="font-semibold">Ad Revenue (Net via Partners):</div>
        <div class="font-bold">~€1.40</div>
      </div>
      <div class="flex justify-between items-center mb-2">
        <div class="font-semibold">AI Compute Cost:</div>
        <div class="font-bold">~€0.20</div>
      </div>
      <div class="flex justify-between items-center mb-2 text-green-600 font-bold">
        <div>Net Revenue (Post Cloud & Nectar):</div>
        <div>~€0.84</div>
      </div>
    </div>
  </div>
</div>

::right::

<div class="flex justify-center items-center h-full">
  <img src="/media/tier-ladder.png" alt="Media Generated Growth" class="w-3/5 rounded-lg shadow-lg">
</div>

<!--
### From Prompt to Profit

#### Overview
* Let me walk you through our clear **3-step journey** for creators
* Step 1 - **Seed** (Live Today): We provide completely **free tools** and **ad-supported compute** through our partners
* This **removes all barriers** to entry and helps creators get started immediately
* Step 2 - **Flower** (H2 '25): Apps begin to **self-fund** their own compute costs through ad revenue
* At this stage, Pollinations earns **platform revenue** from our partners, and creators get **better models** and **higher limits**
* Step 3 - **Nectar** (H2 '26): For top-performing apps, we introduce a **50/50 net ad revenue share** from our partner revenue
* To put this in perspective: With 1M MAU and a $1.50 CPM, that's about **$13.5K net monthly revenue**, with **$6.75K going directly** to the developer
* This creates a **sustainable ecosystem** where **everyone wins** - creators, advertisers, and Pollinations

#### Unit economics
- **Each 1,000 media units generate €1.40** in ad revenue via contextual placements.
- **AI compute costs remain low at €0.20**, thanks to model efficiency at scale.
- After creator share, we retain **€0.84 net per 1k outputs**.
- This margin **covers infra in real-time** and scales linearly with demand.
- **Value is shared** with creators without compromising platform profitability.

- **Net Revenue per App/Year**: €626 (from table)
- **Lifetime** (App stays active): 2 years (conservative)
- **LTV per App**: €1,252
- **CAC per App** (e.g. devrel): €50 (Discord, hackathons, docs, demos)
- **LTV/CAC Ratio**: ~25x

## End
-->

---
layout: two-cols-header
---

<div class="flex items-center justify-between gap-4">
  <h1><span class="bg-black text-white p-2 rounded"><strong>👥 Team</strong></span></h1>
  <img src="/media/pollinations-ai-logo.png" alt="Pollinations AI Logo" class="h-15">
</div>

<br>

<div class="flex flex-col md:flex-row gap-6 mb-6 justify-center items-stretch">
  <div class="bg-gray-100 p-5 rounded-lg w-full md:w-1/2 flex-1 shadow-md border-l-4 border-blue-500">
    <div class="flex items-center mb-3">
      <img src="/media/2.png" alt="Thomas Haferlach" class="w-25 h-25 rounded-full mr-4 border-2 border-gray-300 shadow" />
      <div>
        <h2 class="font-bold text-xl mb-1">Thomas Haferlach</h2>
        <div class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold inline-block">CEO</div>
        <p class="text-2xl text-gray-700">8+ yrs GenAI, Ex-Amazon, EU Funding Winner</p>
      </div>
    </div>
    <div class="flex justify-end mt-3">
      <img src="/media/amazon_logo.png" alt="Amazon" class="h-5 mr-3 opacity-70" />
      <img src="/media/sun-logo.png" alt="Sun" class="h-5 opacity-70" />
    </div>
  </div>

  <div class="bg-gray-100 p-5 rounded-lg w-full md:w-1/2 flex-1 shadow-md border-l-4 border-green-500">
    <div class="flex items-center mb-3">
      <img src="/media/1.png" alt="Elliot Fouchy" class="w-25 h-25 rounded-full mr-4 border-2 border-gray-300 shadow" />
      <div>
        <h2 class="font-bold text-xl mb-1">Elliot Fouchy</h2>
        <div class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold inline-block">COO</div>
        <p class="text-2xl text-gray-700">6+ yrs AI Ops, Built Efficient AI Infra</p>
      </div>
    </div>
  </div>
</div>

## 🔍 **Planned Hires**

::left::

### **1.** MLOps & Infrastructure Lead 🛠️ 

::right::

### **2.** Community & Creator Success 👥
### 🤝 **Kalam** (Incoming)

<!--
## Team - Experience Where It Matters Most
* I'm Thomas, the CEO - I bring **8+ years of generative AI** experience
* I've scaled platforms to over **100M requests per month** and worked at **Amazon AI**
* I also won **1.2m EU funding** for previous AI projects - I know how to **build and scale**
* My co-founder Elliot is our COO with **6+ years of AI operations** experience
* He's built **efficient AI infrastructure** for multiple scale-ups - crucial for our model
* **Lead MLOps / Infra Engineer** (H2 2025): GPU fleet optimization, Scaling Backend, Model-serving pipeline
* **Kalam** (Head of Community & Creator Success, H2 2025): Creator onboarding, XR/UGC initiatives, User acquisition

## End
-->

---
layout: two-cols-header
---

# <span class="bg-teal-600 text-white p-2 rounded">**🤝 We are raising - reach out: <EMAIL>**</span>

::left::

# 🚀 **Now | Activate**
### - Native Ad Toolkit
#
### - Flower GA
#
### - Nectar pilot
#
### - Contextual-Ad Partners
#
### - Project Hosting
#
### - Polli Assistant

<br>

::right::

# 📈 **+12 mo | Scale**
### - Nectar GA
#
### - Monthly payouts
#
### - Ecosystem Fund
#
### - Dynamic & interactive ad formats

<br>

<!--
## Roadmap - Fueling the AI App Monetization Revolution
* Let me share our clear roadmap to fuel this **AI app monetization revolution**

* **Right Now - Activation Phase:**
  * We're shipping our **Native Ad Toolkit** for seamless partner integration
  * We're launching **Polli Assistant** to make creation even easier
  * We're expanding our **Nectar pilot** beyond Roblox to more app types
  * We're onboarding specialized **Contextual-Ad Partners** like Garlic
  * We're starting **Project Hosting** to further reduce barriers to entry

* **Next 12 Months - Scaling Phase:**
  * We'll launch **Nectar GA** with the **50/50 revenue sharing** model
  * We'll implement **monthly payouts** and create an **Ecosystem Fund**
  * We'll introduce **dynamic and interactive ad formats** with our partners
  * We'll build **global creator community programs** to drive adoption

* This roadmap systematically **builds our flywheel** and creates **value for all stakeholders**
## End
-->

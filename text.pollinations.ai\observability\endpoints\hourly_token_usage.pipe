TOKEN read_pipes READ

DESCRIPTION >
    Get hourly token usage for a specific user

NODE hourly_token_usage_node
SQL >
    %
    SELECT
        toStartOfHour(timestamp) as hour,
        sum(total_tokens) as total_tokens,
        sum(prompt_tokens) as prompt_tokens,
        sum(completion_tokens) as completion_tokens,
        sum(cost) as total_cost,
        count() as total_requests
    FROM llm_events
    WHERE 1=1
        {% if defined(user) %}
        AND user = {{String(user, 'wBrowsqq')}}
        {% else %}
        AND user = 'wBrowsqq'
        {% end %}
        {% if defined(start_date) %}
        AND timestamp >= {{DateTime(start_date)}}
        {% else %}
        AND timestamp >= now() - interval 6 day
        {% end %}
        {% if defined(end_date) %}
        AND timestamp <= {{DateTime(end_date)}}
        {% else %}
        AND timestamp <= now()
        {% end %}
        {% if defined(organization) and organization != [''] %}
        AND organization IN {{Array(organization)}}
        {% end %}
        {% if defined(project) and project != [''] %}
        AND project IN {{Array(project)}}
        {% end %}
        {% if defined(environment) and environment != [''] %}
        AND environment IN {{Array(environment)}}
        {% end %}
        {% if defined(model) and model != [''] %}
        AND model IN {{Array(model)}}
        {% end %}
    GROUP BY hour
    ORDER BY hour ASC

TYPE endpoint
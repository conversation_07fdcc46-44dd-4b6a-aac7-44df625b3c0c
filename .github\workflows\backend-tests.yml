name: Backend Tests

on:
  pull_request:
    paths:
      - 'text.pollinations.ai/**'
      - 'image.pollinations.ai/**'
      - '.github/workflows/backend-tests.yml'

jobs:
  determine-tests:
    runs-on: ubuntu-latest
    outputs:
      text-changed: ${{ steps.changes.outputs.text }}
      image-changed: ${{ steps.changes.outputs.image }}
    steps:
      - uses: actions/checkout@v4
      - name: Check for changes
        id: changes
        run: |
          echo "text=$(git diff --name-only ${{ github.event.pull_request.base.sha }} ${{ github.sha }} | grep -q '^text\.pollinations\.ai/' && echo 'true' || echo 'false')" >> $GITHUB_OUTPUT
          echo "image=$(git diff --name-only ${{ github.event.pull_request.base.sha }} ${{ github.sha }} | grep -q '^image\.pollinations\.ai/' && echo 'true' || echo 'false')" >> $GITHUB_OUTPUT

  text-backend-tests:
    needs: determine-tests
    if: needs.determine-tests.outputs.text-changed == 'true'
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: text.pollinations.ai
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: text.pollinations.ai/package-lock.json
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test

  image-backend-tests:
    needs: determine-tests
    if: needs.determine-tests.outputs.image-changed == 'true'
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: image.pollinations.ai
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: image.pollinations.ai/package-lock.json
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test
# Conversation Transcript

## Initial Stakeholder Analysis
- "What kind of participants in your ecosystem?"
- "So I guess your primary stakeholder is the developer."
- "So what exactly do you have for the developer today?"
- "You kind of, basic stuff like, you have an API and an SDK that gives access to the following models and is for free today."
- "And that these types of developers have built these types of apps on top of, I think, is missing."

## Value Proposition Clarity
- "And then from that developer, if that's the key stakeholder, you touch on it somewhat, but why do they use pollinations and not others?"
- "I think you can kind of just tell it much more user-centric or customer-centric."
- "And then if then on top the pitch is that you're actually building the and then kind of why do they use you as other solutions you're touching on multiple things but what is it"
- "So you're saying they don't have the ability to monetize on one hand. On the other hand, you say it's privacy and security. On the third one, you say you don't have to build the layout for authentication."
- "But you're telling that on different parts of the pitch as well. So I think that needs to be more to the point and clearer."

## Missing Elements
- "We're not mentioning competitors, and we're also not mentioning some examples because like, so maybe these two things would already help."

## Advertising Network Concerns
- "And then the second part of what you're building, if I understand correctly, is you're ultimately building an advertising network"
- "And you're not talking about kind of what it actually takes to build an advertising network at all and how you're going to do that."
- "And if you will continue to give access to the models that you end up paying for for free to others and you monetize via the ads but I need to understand the unit economics"

## Economic Viability Questions
- "How do you make sure that this works across the board?"
- "Because I can, there's some apps that I can build that have an audience that is valuable, where you can actually sell ads for a decent amount of money."
- "And then I can use a shit ton of your tokens for free in my app and not really have a lot of valuable audience in front of it so there's no valuable advertising inventory that I generate"

## Team Response on Economics
- "We offer something for free, but at the same time, when your app is not like validated by us, you don't get like these high rates, you don't get like a few things that you don't get that makes the cost for us very limited."
- "And then for different apps that we realize, okay, this is good for monetization, then we offer all this like monetization plugin, We incentivize them to use those tools."
- "And then we kind of sub-select. And it can be automated based on just numbers that we get from analytics."
- "But I think this is where we would target certain apps and not others to give them more credits, more access, more resources."

## Core Business Model Concerns
- "Because the obvious rebuttal to what you're doing is you're essentially giving other people your cloud credits for free at the moment giving away something for free that will always show traction."
- "Is there viable business to build on top of that?"

## Market Size Discussion
- "We were like, oh, do we use the market size of the creative AI gen builders? Or do we use the market size of actually the ads that could be shown to use people that are not shown because all these ads are built but there's no way to put the ads in"
- "This is why we focused on the we tried at least to focus on the ads because this is where the revenue comes from so the ad is actually the money comes from there and then it's just like going through us to the developer back."

## Business Model Focus
- "Should we also focus on other potential revenue streams than just focusing on ads? Or would that complicate the pitch?"
- "Because it sounds like we're trying to do too much. We're trying to do a freemium. We're trying to do an ad product."
- "I think focus makes sense, so typically when companies at an early stage try to have four different, very different sorts of revenues..."
- "You have to build four different businesses and you typically die in the complexity."
- "So I don't know if ads is the best one, but I would not try to in the next 18 months, try to build three different revenue streams. It's not going to get there."
- "You need to start with one and crack that first."

## Target Audience Confusion
- "I don't really get entirely the youth ad market. I don't really get that slide."
- "You're ultimately you're targeting developers that build apps yes those users and customers I guess are skewed towards younger audiences today"
- "It's like all these new spaces that everyone can build an app that can receive ads. So it's just that it's just exploding because of vibe coding."
- "And then this is what we wanted to show is like this space, this ad space that is new and that is being created."

## AI-Assisted Development Insight
- "When you're using an AI assistant these days, these AI assistants nowadays are already, when you ask something a little bit more complex, they're already writing a little bit of code for you and executing it."
- "So indirectly everyone is becoming a creator of code, you know."
- "The next step is just, ah, I liked this report you made, can you make a website for me? And suddenly you've created your own little app"

## Market Size Perspective
- "I think ultimately if what you're saying is that a whole bunch of people will build applications with tons of audiences on your platform and you become the advertising network for that"
- "I don't think that the market size is the biggest objection but I think then how would I size that"
- "Ultimately what you're saying is that a whole chunk of the applications of the future will have some sort of GenAI generated media input into them."
- "Also what we're offering is assistants so multi-turn who can integrate with your UI so kind of a share of all of those applications and then kind of how many eyeballs will those get"

## Presentation Feedback
- "Each person says one sentence is super hard to follow because you get used to a speaker and then I get interrupted and need to kind of adapt to the new speaker every time."
- "I think you need to sell yourselves better. You're very modest. None of your bragging rights of great things that you've done in the future are in there"
- "I think overall somebody who hears this for the first time won't get what you're doing"
- "I haven't understood yet from this or from our discussion so far, whether building an ad network on this type of thing is a good idea or not."

## Closing
- "We were thinking of participating in the pitch roast tomorrow. Do you think that's a good idea?"
- "Yeah, absolutely. Take every opportunity"

---

## Transcript integral 

## Speaker A
- Kind of participants in your ecosystem.
- So I guess your primary stakeholder is the developer.
- So what exactly do you have for the developer today?
- Kind of how it gives access to the following models and is for free today.
- And that these types of developers have built these types of apps on top of, I think is missing.
- Um, and then from that developer, if that's the key stakeholder.
- You touch on it somewhat, but the, why do they use pollination and not others?
- I think you can kind of just tell much more user centric or customer centric.
- Um, and then.
- Then on top the pitch is that you're actually building...
- And then kind of why do they use you as other solutions?
- You're touching on multiple things, but what is it? So you're saying they don't have the ability to monetize on one hand, on the other hand you say it's privacy and security, on the third one you say it's kind of you don't have to build the layout of authentication, but you're telling that on different parts of the pitch as well. So I think that needs to be more.
- To the point and clearer.
- Exactly. And then the second part of what you're building, if I understand correctly, is you're ultimately building an advertising network.
- And you're not talking about what it actually takes to build an advertising network at all.
- And how you're going to do that.
- If you will continue to give access to the models that you end up paying for free to others and you monetize via the ads, that I need to understand the unit economics.
- And what I kind of put in more about content and less about pitch, what I still haven't understood is the
- How do you make sure that this works across the board?
- Because I can, there's some apps that I can build that have an audience that is valuable, where you can actually sell ads for a decent amount of money.
- And then I can use a shit ton of your tokens for free in my app, and not really have a lot of valuable audience in front of it, so there's no valuable advertising inventory that I generate in my app.
- I understand your...
- I can come up with probably a bunch of B2B applications that doesn't actually have a consumer audience that is advertisements in front of it.
- Yeah, I think we're going to have way more detail on that whole side of the equation.
- Because the obvious, obvious...
- Rebuttal to what you're doing is essentially giving Giving other people your cloud credits for free at the moment giving away something for free That will always show traction Yes, that is their vital business to build on top of that
- That's my main topics.
- Looking forward to the next...
- So I don't know what the right business model is.
- But I think the...
- I think focus makes sense, when companies in the early stage try to have four different, very different sorts of revenues.
- You have to build four different businesses and you typically die in the complexity.
- I don't know if ads is the best one, but I would not try to, in the next 18 months, try to build three different revenues.
- Yeah, I don't really get entirely the youth market. I don't really get that slide.
- Ultimately you're targeting developers that build apps. Yes those users and customers I guess ask you towards younger audiences today
- My guess is that people don't know
- With other but it's kind of like the
- How would you do that? Ultimately at the moment you're in an open developer ecosystem and if I build my dating platform for the elderly then I do that.
- That's the creator, but that doesn't mean that they are building for that top end user, right?
- Yeah, I agree, I agree.
- If what you're saying is that...
- A whole bunch of people will build applications with tons of audiences on your platform and you become the advertising network for that.
- I think we talked about a large number and then taking a rev share on that advertising.
- I don't think that the market size is the biggest objection, but I think then how would I size that?
- I would probably
- The next meeting has already started.
- Yeah, then I would come somehow via...
- Ultimately, what he was saying is saying that
- A whole chunk of the applications of the future will have some sort of GenAI-generated media input into them.
- So kind of a share of all of those applications and then kind of how many eyeballs will those get?
- Yeah, yeah, yeah, okay, um, I
- Need to now jump into my next session
- I think this, obviously before this, is kind of something that you can raise on.
- It's still going to require quite a bit of work.
- A couple of more, kind of how to present observations.
- Each person says one sentence is super hard to follow because you get used to a speaker and then I get interrupted and need to adapt to the new speaker every time.
- I think that's hard.
- I think you need to sell yourselves better.
- You're very modest.
- None of your bragging rights of great things that you've done in the future are in there.
- I think that needs to be part of the pitch.
- And I think overall, somebody who hears this for the first time won't get what you're doing.
- And I haven't understood yet from this or from our discussion so far whether...
- Building an ad network on this type of thing is a good idea or not.
- I'm still not there yet.
- So for me it's not just a how to present, but I haven't really understood that yet.
- Um...
- That's my main topics.
- Looking forward to the next...

## Speaker B
- You kind of basic stuff like kind of you have an API and an SDK that
- Um...
- We have this extra...

## Speaker C
- I think what we also, because we did of course a little bit last minute our preparation for today.
- Of course we spent a lot of time and energy but it could have been more, you know.
- But what I definitely am aware of is that we didn't, we're not mentioning competitors and we're also not mentioning some examples because like, so maybe these two things would already help.
- Yeah, but I mean, just to verify what you're saying is we have this one, for example, it's one of our biggest, it's one of our biggest let's say apps. That's not the biggest, but one of the big ones. I went through him because they are putting a lot of typical, this kind of like generic AdSense advertising on their page. And I calculated with them how much is our cost for this and how much could we get is a realistic revenue share from the ads. And this didn't look economically feasible. So I understand your...
- Do you have any questions?
- We have these extra... Sorry, but I think it's better if we get your feedback, because we have all the week then to explain, to answer your questions maybe.
- No.
- Maybe that would be a question to you, but yeah, sorry if you have something else to say.
- Go ahead.
- So, because we're wondering, should we like, should we also focus on other potential revenue streams, then just focusing on ads?
- Or would that complicate the pitch?
- Because it sounds like we're trying to do too much, you know, we're trying to do a freemium, we're trying to do an ad product.
- I've been, I've been usually on the side of trying to keep it as lean and simple as possible.
- That's why we're honing so strongly in on the ad idea.
- But yeah.
- Yeah. I mean, I also think it's, it's, it's, it's more general than, than you for sure.
- Um, um, cause like, I mean, I think like our, our key, like, let's say one, at least for me, you know, for me, one, one key kind of insight is that like when you're using an AI assistant these days, these AI assistants now, nowadays are already exit.
- When you ask something a little bit more complex, they already writing a little bit of code for you and executing it.
- So indirectly, everyone is becoming a creator of code, you know, in the next step is just, ah, I liked this, uh, report you made.
- Can you make a website or for me, and suddenly you've created your own little app.
- So I think this, this is, and then what, what our belief was that when you don't have a clear target audience, when you're like kind of our way, we're, we're, we're, we're the platform for everyone from, from young to old.
- Then you, then it's also a little bit more difficult to kind of like get your message.
- Exactly. Like, like optimized for, for this. And we, we, our belief isn't that it could be so this youth audience is potentially so big that maybe it's more efficient to, to, to focus on that or on them.
- And like, it also like.
- Also what we're offering is assistance, so multi-turn, who can integrate with your UI.

## Speaker D
- We have kind of evolution for like...
- Yeah, but I mean...
- For two more audience that is appetisable in front of it.
- But then we can have these different tiers that we offer something for free, but at the same time when your app is not validated by us, you don't get these high rates, you don't get a few things that you don't get that makes the cost for us very limited.
- And then for different apps that we realise, okay, this is good for monetisation, then we offer all this monetisation plugin, we incentivise them to use those tools and then we kind of like sub-select and it can be automated based on just numbers that we get from analytics.
- But I think this is where we would target certain apps and not others to give them more like credits, more access, more resources.
- There is something with Thomas we were thinking quite a lot also this week.
- It's like this difference between the different actually who are the...
- because we ended up thinking like if you check the SAM, like the market size, we were like, oh, do we use the market size of the creative AI gen builders or do we use the market size of actually the ads that could be shown to use people that are not shown because all these apps are built, but there's no way to put the ads in.
- So I think there we have like kind of like this is why we focused on the...
- we tried at least to focus on the ads because this is where the revenue comes from.
- So the ad is actually the money comes from there.
- And then it's just like going through us to the developer bank.
- And then so this is this kind of will that we were like trying to think of and not really like the...
- This is why maybe we focused a bit less on the user, the developer, the creator.
- Just can't be so big.
- And no customers also, like direct customers, which removes like a whole layer of like issues.
- But it's kind of like the untapped new market that is being created by the...
- I mean, that is expanding.
- It's like all these new spaces that are...
- Everyone can build an app that can receive ads.
- So it's just that it's just exploding because of vibe coding.
- And then this is what we wanted to show.
- It's like this space, this ad space that is new and that is being created.
- But maybe that's not the best way to put it.
- Yeah, yeah, yeah, yeah.
- I think it's maybe we could, it's just that by experience on all these courts, we see a lot of young people, like this is validated.
- The people that chat, sometimes they're 11, sometimes they're 17, I mean, we see that a lot.
- But that's the...
- The creators.
- But that doesn't mean that they are building for that top end user, right?
- Yeah, I agree, I agree.
- So we need bigger numbers
/**
 * Creative Projects 🎨
 * Turn prompts into images, video, music, design, slides
 */

export const creativeProjects = [
  {
    name: "Evolve AI 🤖",
    url: "https://evolve-ai-gamma.vercel.app/",
    description: "Evolve AI is an intelligent assistant platform that brings together a variety of cutting-edge AI tools to simplify and automate user tasks. It integrates multiple APIs from Pollinations to enable features such as advanced text-to-speech, code generation, code editing, AI-based image generation, and more. The platform is designed to evolve with user needs, combining creativity and productivity into one cohesive experience.",
    author: "@chandankumarm55",
    repo: "https://github.com/chandankumarm55/Evolve-ai",
    submissionDate: "2025-08-03",
    order: 1
  },
  {
    name: "What is this? ❔",
    url: "https://whatisthis.pinkpixel.dev/",
    description: "A fun AI-powered object identification tool that helps you identify mysterious objects with just a photo upload. Uses advanced AI vision to analyze photos and tell you exactly what that mysterious object is and what it's used for, plus provides interesting facts about the object.",
    author: "@sizzlebop",
    repo: "https://github.com/pinkpixel-dev/what-is-this",
    submissionDate: "2025-08-03",
    order: 1
  },
  {
    name: "🇷🇺 Aimpress",
    url: "https://aimpress.ru/",
    description: "AIMpress is a web-based image generation platform that allows users to create AI-generated images by writing their own prompts. Users can also optionally select styles, effects, and aspect ratios to customize their results. Every generated image is automatically saved and published to a public archive, effectively creating a massive and growing stock of AI-generated visuals. This database is searchable, allowing users to discover and reuse images based on prompts, styles, or tags.",
    author: "<EMAIL>",
    submissionDate: "2025-08-03",
    language: "ru-RU",
    order: 1
  },
  {
    name: "Vizbo 📱",
    url: "https://vizboapp.com/",
    description: "Vizbo is a mobile app for AI powered vision board creation.",
    author: "<EMAIL>",
    submissionDate: "2025-08-03",
    order: 1
  },
  {
    name: "Imagine Draw AI 🎨",
    url: "https://imaginedrawai.vercel.app/",
    description: "This AI image was created for free using ImagineDraw AI a powerful tool to generate unlimited AI images with just a text prompt. No login, no limits, 100% free!",
    author: "TechWithAiTelugu",
    repo: "https://github.com/TechWithAiTelugu",
    submissionDate: "2025-08-03",
    order: 1
  },
  {
    name: "EzPromptla",
    url: "https://ezpromptla.netlify.app",
    description: "EzPromptla is an advanced visual prompt builder and creative partner designed to simplify high-quality AI image generation. It abstracts the complexity of prompt engineering by providing a category-driven interface (for automotive, portraits, products, etc.) and uses Google's Gemini model as a core reasoning engine to build, analyze, and refine user ideas into master-level prompts for Google Imagen.",
    author: "<EMAIL>",
    submissionDate: "2025-07-13",
    order: 1
  },
  {
    name: "Argent Script",
    url: "https://perchance.org/ai-text-to-audio",
    description: "AI Voice Generator - Generate text to audio for free and without limits, powered by GPT-4o Audio. Instantly convert your words into natural, lifelike speech.",
    author: "https://github.com/withthatway",
    submissionDate: "2025-07-04",
    order: 1
  },
  {
    name: "Unfoldtech",
    url: "https://studio.unfoldtech.online/",
    description: "Easily generate new images inspired by the Pexels website with embedded IPTC (title, description, keywords) on download. Find beautiful image inspiration from Pexels, create prompts from images, and generate new images with metadata embedded for immediate use in stock photography or upscaling.",
    author: "<EMAIL>",
    submissionDate: "2025-06-23",
    order: 1
  },
  {
    name: "Celebrity AI Image Generator",
    url: "https://www.aicelebrity.design/",
    description: "An AI-powered celebrity image generator that uses Pollinations.ai and a1.art APIs. Users can directly input celebrity text to generate celebrities doing anything. Features celebrity photo shooting capabilities where users can upload their pictures and take selfies with celebrities like Labubu, Taylor Swift, and more.",
    author: "@Colin-Zero",
    repo: "https://github.com/Colin-Zero",
    submissionDate: "2025-06-16",
    order: 1
  },
  {
    name: "Coloring AI 🎨",
    url: "https://coloring-ai.com/",
    description: "An intelligent web-based coloring assistant that turns black-and-white sketches into fully colored artworks using AI. Features multiple artistic styles, AI coloring generator, sketch-to-image conversion, and image-to-coloring page tools.",
    author: "<EMAIL>",
    submissionDate: "2025-06-13",
    order: 1
  },
  {
    name: "JSON Pollinations API",
    url: "https://pollinations-json.deno.dev/openai",
    description: "A Deno Deploy API wrapper for Pollinations that provides JSON-formatted responses. Designed for applications that require JSON API integration with Pollinations services.",
    author: "@apersonwhomakesstuff",
    submissionDate: "2025-06-13",
    order: 1
  },
  {
    name: "MrEgonAI",
    url: "https://mr-egon-ai.vercel.app/",
    description: "An image generator powered by pollinations.ai and its image generation models. Features text-to-image generation with various AI models.",
    author: "@mregon",
    submissionDate: "2025-06-07",
    order: 1
  },
  {
    name: "Image Creator",
    url: "https://saepulwap.blogspot.com/p/flux-image-creator.html",
    description: "Create images with multi-language prompts, the language will be automatically translated by AI into English.",
    author: "https://facebook.com/403.frobidden",
    submissionDate: "2025-06-07",
    order: 1
  },
  {
    name: "CatGPT Meme Generator 🐱",
    url: "https://pollinations.github.io/catgpt/",
    description: "Transform your questions into sassy cat wisdom! An AI-powered meme generator that creates personalized cat comics in response to your questions. A collaboration between Pollinations.AI and Tanika Godbole, the original creator of the CatGPT comic.",
    author: "@voodoohop",
    repo: "https://github.com/pollinations/catgpt",
    stars: 2,
    submissionDate: "2025-06-05",
    order: 1
  },
  {
    name: "Avatar GenStudio",
    url: "https://astudio-dcae4.web.app",
    description: "A system for creating custom characters that uses the Pollinations API for totally free and unlimited image generation.",
    author: "@nic-wq",
    submissionDate: "2025-03-10",
    order: 1
  },
  {
    name: "StoryBolt",
    url: "https://storybolt.vercel.app/",
    description: "Generate stories with enticing narratives and beautiful illustrations. Users can enter a prompt, customize the genre and art style, and publish their stories! Users can favorite, download, and listen to their stories, and read other books created by the community.",
    author: "@gumbasil",
    repo: "https://github.com/sahilalamgir/StoryBolt",
    stars: 0,
    submissionDate: "2025-06-13",
    order: 1
  },
  {
    name: "Musify - AI Enhanced Music Streaming",
    url: "https://musify-sd.vercel.app/",
    description: "Musify is your AI-powered music buddy, making your jam sessions smarter and more fun. Powered by pollinations API, it offers AI Music Assistant, Voice Commands, AI Playlist Creator, and Responsive Design.",
    author: "@Sugamdeol",
    submissionDate: "2025-02-27",
    order: 2
  },
  {
    name: "CalcuBite AI",
    url: "https://calcubite.vercel.app/",
    description: "CalcuBite AI is a smart tool that analyzes food from images to provide calorie and nutrient details. Just take a photo, and it quickly gives you an estimate of your meal's nutritional value. It uses AI for accurate analysis, and if you run out of free scans, you can watch an ad to get more!",
    author: "@sugamdeol",
    submissionDate: "2025-03-15",
    order: 1
  },
  {
    name: "Elixpo Art",
    url: "https://elixpoart.vercel.app",
    description: "A Web interface to create thematic images from prompts, with multiple aspect ratios and also image reference inputs.",
    author: "Ayushman Bhattacharya",
    repo: "https://github.com/Circuit-Overtime/elixpo_ai_chapter",
    stars: 8,
    submissionDate: "2025-03-31",
    order: 1
  },
  {
    name: "Case Me 🇧🇷",
    description: "A vending machine that creates customized phone cases with photos or other images and colors chosen by the end customer.",
    author: "<EMAIL>",
    submissionDate: "2025-03-19",
    language: "pt-BR",
    order: 2
  },
  {
    name: "Generator AI Image 🇮🇩",
    url: "https://kenthir.my.id/advanced-generator/",
    description: "Advanced AI Image Generator adalah platform inovatif yang memungkinkan Anda membuat gambar digital menakjubkan dengan kecerdasan buatan by pollinations.ai. Dengan dukungan berbagai model AI canggih seperti DALL·E 3, Stable Diffusion, dan Flux-Default. (An innovative platform that allows you to create amazing digital images with artificial intelligence powered by pollinations.ai. Supports various advanced AI models like DALL-E 3, Stable Diffusion, and Flux-Default.)",
    author: "@kenthirai",
    submissionDate: "2025-04-15",
    language: "id-ID",
    order: 1
  },
  {
    name: "Pollinations.ai Image Generation (for Frame)",
    url: "https://github.com/CitizenOneX/frame_pollinations",
    description: "A Flutter application that listens for image generation prompts, requests images from Pollinations.AI, and displays them on the Frame wearable device. Users can use voice commands to generate images and save/share them using the device's sharing mechanism.",
    author: "CitizenOneX",
    repo: "https://github.com/CitizenOneX/frame_pollinations",
    stars: 6,
    submissionDate: "2025-04-13",
    order: 1
  },
  {
    name: "POLLIPAPER",
    url: "https://github.com/Tolerable/POLLIPAPER",
    description: "A dynamic wallpaper app that uses Pollinations AI.",
    author: "@intolerant0ne",
    order: 1,
    category: "creativeApps",
    stars: 9
  },
  {
    name: "Generator Text AI 🇮🇩",
    url: "https://app.ariftirtana.my.id/",
    description: "Text-to-image generator using Pollinations, supporting Indonesian and English prompts.",
    author: "@ayick13",
    repo: "https://github.com/ayick13/app",
    stars: 1,
    submissionDate: "2025-04-16",
    language: "id-ID",
    order: 2
  },
  {
    name: "NailsGen",
    url: "https://www.nailsgen.com/",
    description: "Create beautiful nail art designs with AI. Generate unique nail art designs with different styles and colors.",
    author: "<EMAIL>",
    submissionDate: "2025-04-30",
    order: 1
  },
  {
    name: "ImageGen AI Image",
    url: "https://imagegenaiimage.com/",
    description: "Generate high-quality AI images for any purpose. Features a variety of models and styles.",
    author: "https://www.linkedin.com/in/narendradwivedi",
    submissionDate: "2025-04-22",
    order: 1
  },
  {
    name: "RuangRiung AI Image 🇮🇩",
    url: "https://ruangriung.my.id",
    description: "RuangRiung AI Image Generator is ideal for digital artists, designers, or anyone who wants to explore creativity with AI assistance. Available in English and Indonesian, this website combines complete functionality with an elegant and responsive design.",
    author: "@ruangriung",
    repo: "https://github.com/ruangriung",
    submissionDate: "2025-04-20",
    language: "id-ID",
    order: 1
  },
  {
    name: "Dreamscape AI",
    url: "https://dreamscape.pinkpixel.dev",
    description: "Dreamscape AI is a creative studio for generating, enhancing, and transforming images, plus conversational AI capabilities with text and voice interfaces, and a deep research tool. The entire site is almost all powered by Pollinations API aside from the image enhancement tools. It generates images, optimizes prompts and creates image titles with the text API, features lots of image styling prompts, also has chat and voice chat with chat memory, and a research tool.",
    author: "@sizzlebop",
    repo: "https://github.com/pinkpixel-dev/dreamscape-ai",
    stars: 2,
    submissionDate: "2025-05-02",
    order: 1
  },
  {
    name: "PollinateAI",
    url: "https://pollinateai.vercel.app",
    description: "PollinateAI is an image generation platform that aims to ease the stress of graphic and visual designers in delivering inspirations for their work. Regular consumers are also welcomed.",
    author: "@Auspicious14",
    repo: "https://github.com/Auspicious14/image-generator-fe.git",
    stars: 0,
    submissionDate: "2025-05-01",
    order: 1
  },
  {
    name: "FlowGPT",
    url: "https://flowgpt.com/p/instant-image-generation-with-chatgpt-and-pollinationsai",
    description: "Generate images on-demand with ChatGPT!",
    order: 1
  },
  {
    name: "Zelos AI image generator",
    url: "https://websim.ai/@ISWEARIAMNOTADDICTEDTOPILLOW/ai-image-prompt-generator",
    description: "It uses Pollinations for both prompt enhancing and image generation, it was a easy to make project due to pollinations services being easy to use.",
    author: "https://www.roblox.com/users/4361935306/profile",
    submissionDate: "2025-02-17",
    order: 2
  },
  {
    name: "IMyself AI 🇨🇳",
    url: "https://openai.lmyself.top/",
    description: "我们提供高质量的AI生成服务，包括图像生成、文本生成、音频生成和语音转文本服务， 让您轻松创建各种创意内容。 (We provide high-quality AI generation services, including image generation, text generation, audio generation, and speech to text services, allowing you to easily create various creative content.)",
    author: "Shadownc",
    submissionDate: "2025-03-27",
    language: "zh-CN",
    order: 5
  },
  {
    name: "Image Gen - Uncensored Edition",
    url: "https://huggingface.co/chat/assistant/66fccce0c0fafc94ab557ef2",
    description: "A powerful image generation assistant on HuggingChat.",
    author: "@DeFactOfficial",
    submissionDate: "2025-02-16",
    order: 1
  },
  {
    name: "Own-AI",
    url: "https://own-ai.pages.dev/",
    description: "An AI text-to-image generator.",
    author: "Sujal Goswami",
    submissionDate: "2025-02-16",
    order: 3
  },
  {
    name: "Pollinator Android App",
    url: "https://github.com/g-aggarwal/Pollinator",
    description: "An open-source Android app for text-to-image generation.",
    author: "@gaurav_87680",
    submissionDate: "2025-02-16",
    order: 2,
    stars: 28
  },
  {
    name: "Pollinations.ai Model Comparison",
    url: "https://endemicmedia.github.io/FLARE/llm-comparison-tool/",
    description: "An interactive tool designed to compare outputs from various large language models with customizable timeout settings and real-time testing capabilities.",
    author: "https://github.com/dseeker",
    repo: "https://github.com/EndemicMedia",
    submissionDate: "2025-02-16",
    order: 4
  },
  {
    name: "Elixpo Art Chrome Extension",
    url: "https://chromewebstore.google.com/detail/elixpo-art-select-text-an/hcjdeknbbbllfllddkbacfgehddpnhdh",
    description: "It uses the pollinations image endpoint to generate an image with `boltning` as the model in 4 types of aspect ratios and themes with prompt engineering thus transforming selected texts into art smoothly with a disposable GUI in web.",
    author: "Ayushman Bhatacharya",
    repo: "https://github.com/Circuit-Overtime/elixpo_ai_chapter/tree/main/Elixpo%20Chrome%20%20Extension",
    stars: 8,
    submissionDate: "2025-03-14",
    order: 1
  },
  {
    name: "Imagen",
    url: "https://altkriz.github.io/imagen/",
    description: "A beautiful web interface for generating images using Pollinations.ai API with only the \"flux\" and \"turbo\" models.",
    author: "@altkriz",
    repo: "https://github.com/altkriz/imagen",
    stars: 3,
    submissionDate: "2025-04-13",
    order: 1
  },
  {
    name: "Foodie AI",
    url: "https://foodie-ai.vercel.app/",
    description: "An AI application for food analysis that uses advanced artificial intelligence technology to help users understand food ingredients, nutritional value, and health impacts. Provides food safety analysis, nutritional health assessment, sports and fitness analysis, visual display, alternative recommendations, and practical insights for different dietary habits.",
    author: "@Aashir__Shaikh",
    submissionDate: "2025-05-06",
    order: 1
  },
  {
    name: "AIMinistries",
    url: "https://www.ai-ministries.com",
    description: "A collection of free AI tools including AI chat, writing tools, image generation, image analysis, text-to-speech, and speech-to-text.",
    author: "@tolerantone",
    submissionDate: "2025-04-21",
    order: 1
  },
  {
    name: "MoneyPrinterTurbo",
    url: "https://github.com/harry0703/MoneyPrinterTurbo",
    description: "Simply provide a topic or keyword for a video, and it will automatically generate the video copy, video materials, video subtitles, and video background music before synthesizing a high-definition short video. Integrates Pollinations' text generation service to create engaging and relevant video scripts.",
    author: "@harry0703",
    repo: "https://github.com/harry0703/MoneyPrinterTurbo",
    stars: 38710,
    submissionDate: "2025-05-13",
    order: 1
  },
  {
    name: "Match-cut video ai",
    url: "https://github.com/lrdcxdes/text-match-cut",
    description: "This AI generates video from text in match-cut text style, uses pollinations llm to generate nearby text, and supports API integration.",
    author: "@r3ap3redit",
    repo: "https://github.com/lrdcxdes/text-match-cut",
    submissionDate: "2025-05-19",
    order: 1,
    stars: 16
  },
  {
    name: "The Promised Pen",
    url: "https://promisedpen.app",
    description: "A free, feature-rich novel writing application that helps writers organize stories, characters, and worlds. Uses Pollinations AI for generating chapter summaries, rewriting text based on context, and generating new content based on previous chapters and character information.",
    author: "@soryn.san",
    submissionDate: "2025-05-19",
    order: 1
  },
  {
    name: "Text2Image_audio 🇨🇳",
    url: "nihilistic.dpdns.org",
    description: "文生图与文生语音网站 - 一个初学者的AI编程项目，支持文本转图像和音频生成功能。(Text to Image and Text to Audio website - A beginner's AI programming project supporting text-to-image and audio generation features.)",
    author: "@peyoba",
    repo: "https://github.com/wtliao/text2image",
    stars: 0,
    submissionDate: "2025-06-05",
    language: "zh-CN",
    order: 1
  },
  {
    name: "Aiphoto智能绘画 🇨🇳",
    url: "https://qiyimg.3d.tc/Aiphoto",
    description: "AI艺术工坊 - 智能绘画生成器。这是一个基于AI的绘画生成工具，可以根据用户输入的中文描述自动生成相应的图片。(An AI art workshop - intelligent painting generator. This is an AI-based painting generation tool that can automatically generate images based on Chinese descriptions input by users.)",
    author: "@qiyimg",
    submissionDate: "2025-05-11",
    order: 1,
    language: "zh-CN"
  },
  {
    name: "AI YouTube Shorts Generator",
    description: "Python desktop app that automates YouTube Shorts creation with AI-generated scripts, voiceovers (via ElevenLabs), and visuals using Pollinations API. Designed for content creators, educators, and marketers to produce high-quality short videos quickly without manual editing.",
    author: "@Sami-Alsahabany",
    authorEmail: "<EMAIL>",
    submissionDate: "2025-05-16",
    order: 1
  },
  {
    name: "FoldaScan",
    url: "https://fs.wen.bar",
    description: "Use Natural Language to \"Converse\" with Your Codebase, Folda-Scan Smart Project Q&A, powered by advanced vectorization technology, allows you to easily understand complex code, pinpoint information, and offers unprecedented convenience for AI collaboration.",
    author: "@0010skn",
    repo: "https://github.com/0010skn/WebFS-Toolkit-Local-Folder-Scan-Monitor-Versioning-AI-Prep",
    stars: 178,
    submissionDate: "2025-05-19",
    order: 1
  },
  {
    name: "Emojiall AI Drawing Platform",
    url: "https://art.emojiall.com",
    description: "A platform focused on allowing users to draw pictures according to their own requirements with many preset styles and themes. Part of Emojiall, which has other text-based AI features like Emoji translation to text, Emoji recommender, and Emoji chatbot.",
    author: "@James-Qi",
    authorEmail: "<EMAIL>",
    submissionDate: "2025-05-20",
    order: 1
  },
  {
    name: "MASala",
    url: "https://github.com/Naman009/MASala",
    description: "Multi-Agent AI That Cooks Up Recipes Just for You ~ From fridge to feast, MASALA plans it all.",
    author: "@Naman009",
    repo: "https://github.com/Naman009/MASala",
    stars: 4,
    submissionDate: "2025-05-20",
    order: 1
  },
  {
    name: "PixPal",
    url: "https://pixpal.chat",
    description: "PixPal is a free AI assistant that can analyze, edit, and generate images, build websites from screenshots, create 3D games, and write full blog posts—all in one chat. Upload a photo, describe an idea, or request a UI clone and PixPal instantly delivers creative results.",
    author: "@andreas_11",
    submissionDate: "2025-05-30",
    order: 1
  },
  {
    name: "🇪🇸 Generador de presentaciones con imágenes y texto V2",
    url: "https://proyectodescartes.org/IATools/Crea_presentaciones4/",
    description: "Una herramienta configurable que permite crear presentaciones con 3 a 20 diapositivas usando la API de Pollinations. Genera títulos, descripciones e imágenes para cada diapositiva, con posibilidad de regenerar imágenes y descargar en HTML. (A configurable tool that allows you to create presentations with 3 to 20 slides using the Pollinations API. Generates titles, descriptions and images for each slide, with the ability to regenerate images and download in HTML.)",
    author: "@juanrivera126",
    submissionDate: "2025-06-03",
    language: "es-ES",
    order: 1
  },
  {
    name: "🇪🇸 Yo el director",
    url: "https://yoeldirector.dpana.com.ve",
    description: "Web para crear peliculas y contenido para youtube, usando Pollinations (Web platform for creating movies and YouTube content using Pollinations)",
    author: "@henryecamposs",
    submissionDate: "2025-06-04",
    language: "es",
    order: 1
  },
  {
    name: "Imagemate AI",
    url: "https://play.google.com/store/apps/details?id=com.madameweb.imgmate",
    description: "Imagemate AI is a powerful image generation app designed to turn your imagination into stunning visuals with the help of advanced artificial intelligence. Built using the Pollinations AI API, Imagemate AI allows users to input a text prompt and instantly receive AI-generated images that match the description.",
    author: "@Shanto-Islam",
    authorEmail: "<EMAIL>",
    submissionDate: "2025-05-13",
    order: 1
  },
  {
    name: "B&W SVG Generator",
    url: "https://fluxsvggenerator.streamlit.app/",
    description: "Uses Flux (through pollinations) and potrace to create B&W Vector files",
    author: "@pointsguy118",
    submissionDate: "2025-04-15",
    order: 1
  },
  {
    name: "Imagen",
    url: "https://altkriz.github.io/imagen/",
    description: "A beautiful web interface for generating images using Pollinations.ai API with only the \"flux\" and \"turbo\" models.",
    author: "@altkriz",
    repo: "https://github.com/altkriz/imagen",
    stars: 3,
    submissionDate: "2025-04-13",
    order: 1
  },
  {
    name: "Elixpo-Art",
    url: "https://elixpo-art.com",
    description: "A digital art platform that combines AI image generation with traditional digital art tools, offering creative filters and style transfers powered by Pollinations.",
    author: "@elixpo",
    repo: "https://github.com/elixpo/art-platform",
    stars: 18,
    submissionDate: "2025-05-09",
    order: 1
  },
  {
    name: "TurboReel",
    url: "https://turboreel.framer.ai/",
    description: "A fast AI video generation service for social media content, leveraging Pollinations to create short, impactful videos from simple descriptions.",
    author: "@turbo_reels",
    submissionDate: "2025-05-08",
    order: 1
  },
  {
    name: "StorySight",
    url: "https://github.com/tangg555/story-generation-demo",
    description: "An AI tool that generates illustrations for stories and articles using Pollinations, helping writers visualize their narratives.",
    author: "@story_viz",
    repo: "https://github.com/tangg555/story-generation-demo",
    submissionDate: "2025-05-14",
    order: 2,
    stars: 14
  },
  {
    name: "Anime Character Generator",
    url: "https://perchance.org/ai-character-generator",
    description: "A dedicated AI tool for generating high-quality, unique anime-style characters. Offers detailed customization of art style, character traits, clothing, and accessories, all powered by Pollinations.",
    author: "@AnimeArtDevs",
    submissionDate: "2025-03-01",
    order: 1
  },
  {
    name: "AI PPT Maker",
    url: "https://slidesgpt.com",
    description: "An AI-powered presentation generator that creates PowerPoint slides from text prompts using Pollinations. Features customizable templates, image suggestions, and content structuring to streamline presentation creation.",
    author: "@ppt_monster",
    submissionDate: "2025-04-25",
    order: 1
  },
  {
    name: "Promptgenerator.art",
    url: "https://promptgenerator.art",
    description: "FREE Art Prompt Generator that helps you turn simple ideas into structured prompts for tools like Midjourney, DALL·E, and ChatGPT. One of its standout features is Test Visualization, which lets you instantly preview how your prompt might look — helping you fine-tune ideas before using any AI tool. This powerful visual preview is made possible through integration with Pollinations AI, enhancing your creative workflow like never before.",
    author: "<EMAIL>",
    submissionDate: "2025-07-14",
    order: 1
  }
];

name: Run Tests

on:
  pull_request:
    branches: [ main ]
  push:
    branches: [ main ]

jobs:
  test-image-service:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./image.pollinations.ai
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js 18
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: './image.pollinations.ai/package-lock.json'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run tests
      run: npm test
      env:
        NODE_ENV: test
        DEBUG: pollinations:*

  test-text-service:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./text.pollinations.ai
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js 18
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: './text.pollinations.ai/package-lock.json'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run tests
      run: npm test
      env:
        NODE_ENV: test

  test-frontend:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./pollinations.ai
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js 18
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: './pollinations.ai/package-lock.json'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run tests
      run: npm test -- --watchAll=false
      env:
        NODE_ENV: test
        CI: true
# @pollinations/react - Generative AI Playground

Welcome to the Pollinations React Playground! This project showcases a React component that demonstrates the usage of Pollinations.ai's generative AI hooks for text, image, and chat generation in an interactive environment.

## Features

- **Text Generation**: Utilize the `usePollinationsText` hook to create text responses based on user prompts.
- **Image Generation**: Generate images using the `usePollinationsImage` hook, with customizable dimensions.
- **Chat Interaction**: Engage with an AI assistant through the `usePollinationsChat` hook.
- **Real-Time Component Previews**: Instantly visualize the effects of parameter changes.
- **Customizable Parameters**: Modify seed, model, and image dimensions dynamically.
- **Effortless Copy & Paste**: Easily copy code snippets with a click for quick testing.
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices.


## Installation

To install and run the project, follow these steps:

1. **Clone this repository**:
   ```bash
   git clone https://github.com/diogo-karma/pollinations-react-doc
   cd pollinations-react-doc
   ```

2. **Install the necessary dependencies**:
   ```bash
   npm install # or yarn install or bun install or pnpm install
   ```

3. **Run the development server**:
   ```bash
   npm run dev # or yarn dev or bun dev or pnpm dev
   ```

4. **Open the application**:
   Navigate to [http://localhost:3000](http://localhost:3000) in your browser to see the app in action.

## Usage

The Pollinations Playground component provides three main tabs:

1. **Text Generation**: Enter a prompt to generate text using various models.
2. **Image Generation**: Create images based on text prompts with customizable dimensions.
3. **Chat Interaction**: Engage in a conversation with an AI assistant.

Each tab includes a code snippet demonstrating how to use the respective Pollinations hook in your own projects.

## Dependencies

This project relies on the following technologies:

- **Node.js**
- **React**
- **Next.js**
- **Tailwind CSS**
- **@pollinations/react** (for AI generation hooks)
- Various UI components (`@/components/ui/*`)

## Contributing

Contributions are welcome! Feel free to fork this repository, make your changes, and submit a Pull Request.

## License

This project is open-source and available under the [MIT License](./LICENSE).

---

## TODO

- [X] Real-time updates for available models.
- [X] Model selection for text generation.
- [X] Image size selection.
- [X] Seed selection options.
- [X] Model selection for image generation.
- [X] "Copy Code" button.
- [X] Markdown rendering for better visualization.
- [X] Documentation for hooks.
- [X] Preview functionality in the hooks documentation.
- [ ] Add `onChange` functionality with debounce.
- [ ] Document all parameters for components and hooks.
- [ ] Loading indicators for asynchronous actions.
- [ ] Enhance documentation for all possible parameters.
- [ ] Integrate Pollinations.ai’s visual CSS styling pattern into the docs.

---

## 💡 Learn More

- Explore the [Pollinations Generative React Hooks & Components](https://www.npmjs.com/package/@pollinations/react) on npm.
- Try the [Chatbot example](https://karma.pollinations.ai) to experience more features.
- Check out the [Storytelling example](https://storytelling.karma.yt/) for creative uses.
- Learn more about Pollinations at [Pollinations.ai](https://pollinations.ai).

### Made with ❤️ by the [Pollinations.ai](https://pollinations.ai) Team & [Karma.yt](https://karma.yt)
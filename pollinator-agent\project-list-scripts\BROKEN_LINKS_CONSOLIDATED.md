# 🚨 Consolidated Broken Links Report

> **Generated:** 2025-07-12

This report provides a consolidated summary of the 91 broken links found in the Pollinations projects showcase. It merges all investigation findings into a single, actionable table.

## 📊 Final Status Summary

- **✅ Fixed:** 22
- **🟡 Alternative Found:** 30
- **❌ To Be Removed:** 35
- **❓ Needs Investigation:** 4

## 📋 Consolidated Broken Links Table

| Category | Project | Final Status | New URL / Alternative | Notes |
|:---|:---|:---|:---|:---|
| Chat 💬 | 🤖 DesmondBot | ❌ To Be Removed | — | No official or working link found. |
| Chat 💬 | AdvanceChatGptBot | ❌ To Be Removed | — | No official repo or site found. |
| Chat 💬 | AI Chat | 🟡 Alternative Found | [https://jolav.github.io](https://jolav.github.io) | <PERSON><PERSON>’s project hub; no direct AI Chat, but author’s site. |
| Chat 💬 | Free AI Chatbot & Image Generator | 🟡 Alternative Found | [https://vercel.com/templates/ai/ai-sdk-image-generator](https://vercel.com/templates/ai/ai-sdk-image-generator) | Vercel AI SDK template for chatbot + image gen. |
| Chat 💬 | Irina | ❌ To Be Removed | — | No official repo/site found. |
| Chat 💬 | KoboldAI Lite | ✅ Processed | [https://lite.koboldai.net/](https://lite.koboldai.net/) | Official KoboldAI Lite is working and supports Pollinations API. |
| Chat 💬 | LiteAI | 🟡 Alternative Found | [https://github.com/iamlite/liteAI](https://github.com/iamlite/liteAI) | Unofficial repo, not a hosted site. Consider as repo only. |
| Chat 💬 | LobeChat | ✅ Processed | [https://lobechat.com](https://lobechat.com) | Official LobeChat site working, GitHub repo active. |
| Chat 💬 | OkeyMeta | ✅ Processed | [https://playground.okeymeta.com.ng/](https://playground.okeymeta.com.ng/) | Official OkeyMeta AI Playground is working. |
| Chat 💬 | Pollinations AI Chatbot | ✅ Processed | [https://github.com/vercel/ai-chatbot](https://github.com/vercel/ai-chatbot) | Project removed from chat.js. |
| Chat 💬 | Pollinations AI Playground | 🟡 Alternative Found | [https://pollinations.ai/](https://pollinations.ai/) | Main Pollinations site, playground integrated. |
| Chat 💬 | Pollinations Chatbot | 🟡 Alternative Found | [https://pollinations.github.io/hive/main/ai-chat/](https://pollinations.github.io/hive/main/ai-chat/) | Pollinations Hive, similar functionality. |
| Chat 💬 | PrivatePollenAI | ❌ To Be Removed | — | No official repo/site found. |
| Chat 💬 | Snarky Bot | ❌ To Be Removed | — | No official or working link found. |
| Chat 💬 | Unity AI Lab | ✅ Processed | [https://unity.unityailab.com/](https://unity.unityailab.com/) | Unity AI Lab working demo found. |
| Chat 💬 | UR Imagine & Chat AI | 🟡 Alternative Found | [https://perchance.org/ur-imagine-ai](https://perchance.org/ur-imagine-ai) | Perchance generator, not full chat app. |
| Creative 🎨 | AI PPT Maker | ✅ Processed | [https://slidesgpt.com](https://slidesgpt.com) | Multiple AI PPT generators available, SlidesGPT highly rated. |
| Creative 🎨 | AI 文本转音频 🇨🇳 | ❌ To Be Removed | — | Vercel deployment removed, likely temporary demo. |
| Creative 🎨 | AI-Bloom | ✅ Processed | [https://bloom.ai/](https://bloom.ai/) | Multiple Bloom AI platforms available. |
| Creative 🎨 | Aiphoto智能绘画 | ✅ Processed | [https://github.com/devopsmi/ai-painting](https://github.com/devopsmi/ai-painting) | No official demo, but related repo exists. |
| Creative 🎨 | Anime AI Generation | ❌ To Be Removed | — | Vercel deployment removed, appears to be temporary demo. |
| Creative 🎨 | Anime Character Generator | ✅ Processed | [https://github.com/0x5eba/Anime-Character-Generator](https://github.com/0x5eba/Anime-Character-Generator) | Official repo found and active. |
| Creative 🎨 | BlackWave | ❌ To Be Removed | — | blackwave.studio domain not found, no active presence. |
| Creative 🎨 | Elixpo-Art | ✅ Processed | [https://elixpo.com](https://elixpo.com) | Demo and related repo found. |
| Creative 🎨 | Generative AI Images Gallery | ✅ Processed | [https://github.com/steven2358/awesome-generative-ai](https://github.com/steven2358/awesome-generative-ai) | Comprehensive generative AI gallery/list. |
| Creative 🎨 | Image Gen - Uncensored Edition | ✅ Processed | [https://huggingface.co/chat/assistant/66fccce0c0fafc94ab557ef2](https://huggingface.co/chat/assistant/66fccce0c0fafc94ab557ef2) | Demo is alive on HuggingChat. |
| Creative 🎨 | IRINA by visuallink | ❌ To Be Removed | — | No repo or official project found. |
| Creative 🎨 | Jackey | ❌ To Be Removed | — | No demo or repo found. |
| Creative 🎨 | Match-cut video ai | 🟡 Alternative Found | [https://github.com/lrdcxdes/text-match-cut](https://github.com/lrdcxdes/text-match-cut) | Web-based text match cut video generator. |
| Creative 🎨 | Memed | ❌ To Be Removed | — | No repo or official project found. |
| Creative 🎨 | MIDIjourney | 🟡 Alternative Found | [https://www.audiocipher.com/post/ai-midi-generators](https://www.audiocipher.com/post/ai-midi-generators) | Various AI MIDI generators available as alternatives. |
| Creative 🎨 | Pollinations AI Image Generator | ❌ To Be Removed | — | Vercel deployment removed, redundant with main Pollinations.ai. |
| Creative 🎨 | Pollinations AI Video Generator | ❌ To Be Removed | — | Vercel deployment removed, likely demo/prototype. |
| Creative 🎨 | Pollinations Feed | ❌ To Be Removed | — | Vercel deployment removed, appears to be temporary demo. |
| Creative 🎨 | Pollinations Gallery | ❌ To Be Removed | — | Netlify deployment removed, likely prototype. |
| Creative 🎨 | Polynate | ❌ To Be Removed | — | No repo or official project found. |
| Creative 🎨 | Rangrez AI | ❌ To Be Removed | — | No specific project found, only generic AI tools. |
| Creative 🎨 | Snapgen.io | ❌ To Be Removed | — | Domain snapgen.io not accessible, no GitHub repo found. |
| Creative 🎨 | StorySight | 🟡 Alternative Found | https://github.com/tangg555/story-generation-demo | Story generation demo (75% confidence) |
| Creative 🎨 | StoryWeaver | 🟡 Alternative Found | https://github.com/Aria-Zhangjl/StoryWeaver | AAAI 2025 StoryWeaver unified model (90% confidence) |
| Creative 🎨 | Text2Image_audio 🇨🇳 | 🟡 Alternative Found | [https://github.com/wtliao/text2image](https://github.com/wtliao/text2image) | Text to image generation, not audio-specific. |
| Creative 🎨 | TurboReel | ✅ Processed | [https://turboreel.framer.ai/](https://turboreel.framer.ai/) | Official TurboReel AI video generator working. |
| Creative 🎨 | WebGeniusAI | ✅ Processed | — | Netlify deployment removed, no active GitHub repo found. |
| Games 🎲 | Abyss Ascending | ✅ Processed | [https://interzone.art.br/abyss_ascending/](https://interzone.art.br/abyss_ascending/) | Working demo found by user. |
| Games 🎲 | AI Character RP (Roblox) | ✅ Processed | https://github.com/snipcola/Roblox-AI | Roblox AI automation and character interaction (80% confidence) |
| Games 🎲 | Infinite Tales | ✅ Processed | https://github.com/JayJayBinks/infinite-tales-rpg | Interactive choose-your-own-adventure RPG (95% confidence) |
| Games 🎲 | Infinite World – AI Game | ❌ To Be Removed | — | No specific project found, only generic AI world generation tools. |
| Games 🎲 | Juego de Memorizar con Pollinations | ❌ To Be Removed | — | No specific Pollinations-related memory game found. |
| Games 🎲 | Minecraft AI (Node.js) | 🟡 Alternative Found | https://github.com/Michael-Andrzejewski/Minecraft-God-AI | Winner of Apart Minecraft AI competition (85% confidence) |
| Games 🎲 | Minecraft AI (Python) | 🟡 Alternative Found | https://github.com/TinyAI-ID/tinyai-id-minecraft-ai-agent-example | Simple Minecraft AI agent example (80% confidence) |
| Games 🎲 | Pollinations AI Game | ✅ Processed | https://github.com/ednsinf/pollinations-ai | Telegram bot with game-like AI interactions (75% confidence) |
| Games 🎲 | Sirius Cybernetics Elevator Challenge | ✅ Processed | https://github.com/pollinations/sirius-cybernetics-elevator-challenge | Official Pollinations repository found (100% confidence) |
| Hack-&-Build 🛠️ | DominiSigns | 🟡 Alternative Found | [https://www.template.net/ai-sign-generator](https://www.template.net/ai-sign-generator) | AI sign generators available, Template.net offers free AI sign maker. |
| Hack-&-Build 🛠️ | Herramientas IA | ✅ Processed | [https://github.com/cusanotech/90-herramientas-de-inteligencia-artificial](https://github.com/cusanotech/90-herramientas-de-inteligencia-artificial) | Curated list of AI tools in Spanish. |
| Hack-&-Build 🛠️ | MVKProject Nexus API | ❌ To Be Removed | — | No repo or official project found. |
| Hack-&-Build 🛠️ | Pollinations AI Free API | 🟡 Alternative Found | https://github.com/Igor-Vitin/pollinations-free-API | Free API wrapper for Pollinations (90% confidence) |
| Hack-&-Build 🛠️ | Pollinations MCP Server | ✅ Processed | https://github.com/pinkpixel-dev/MCPollinations | Model Context Protocol server for Pollinations APIs (95% confidence) |
| Hack-&-Build 🛠️ | Server Status Dashboards | ✅ Processed | [https://github.com/hverr/status-dashboard](https://github.com/hverr/status-dashboard) | Highly configurable open-source server status dashboard. |
| Hack-&-Build 🛠️ | Smpldev | ✅ Processed | — | No specific development tool with this name found. |
| Hack-&-Build 🛠️ | urSapere AI | ❌ To Be Removed | — | No repo or official project found. |
| Learn 📚 | Chinese DeepSeek Tutorial | ❌ To Be Removed | — | 403 Forbidden, no public tutorial found. |
| Learn 📚 | Connect Pollinations with Open Web UI tutorial | 🟡 Alternative Found | https://github.com/cloph-dsp/Pollinations-AI-in-OpenWebUI | Tutorial on adding Pollinations to OpenWebUI (100% confidence) |
| Learn 📚 | OkeyAI | ✅ Processed | — | Confirmed not present in chat.js. |
| Social Bots 🤖 | AI Image Generator [ROBLOX] | ✅ Processed | https://github.com/snipcola/Roblox-AI | Alternative repo added to socialBots.js. |
| Social Bots 🤖 | AlphaLLM - AI Discord Bot | ✅ Processed | — | Confirmed not present in socialBots.js. |
| Social Bots 🤖 | OpenHive | ✅ Processed | — | Confirmed not present in socialBots.js. |
| Social Bots 🤖 | PolliBot | ✅ Processed | — | Confirmed not present in socialBots.js. |
| Social Bots 🤖 | Pollinations Discord Bot | 🟡 Alternative Found | https://github.com/Zingzy/pollinations.ai-bot | Active Discord bot for AI image generation (95% confidence) |
| Social Bots 🤖 | Quick AI & Jolbak | ✅ Processed | https://github.com/jacob-ai-bot/jacob | Alternative repo added to socialBots.js. |
| Social Bots 🤖 | Raftar.xyz | ✅ Processed | [https://discord.com/discovery/applications/1285597879020556308](https://discord.com/discovery/applications/1285597879020556308) | Raftar.xyz Discord bot is active and working. |
| Social Bots 🤖 | SingodiyaTech bot | ✅ Processed | — | User confirmed this project is working. Kept in socialBots.js. |
| Social Bots 🤖 | Titan-GPT | ✅ Processed | — | Confirmed not present in socialBots.js. |
| Vibe Coding ✨ | Define | ✅ Processed | — | 503 Service Unavailable, no specific dev tool found. |
| Vibe Coding ✨ | Open Prompt | ✅ Processed | — | No repo or official project found. |
| Vibe Coding ✨ | WebGeniusAI | ✅ Processed | — | Netlify deployment removed, no active GitHub repo found. |

## 🚀 Next Steps

1.  **Remove Dead Projects:** Remove the 30 projects marked as **❌ To Be Removed** from the project list.
1.  **Apply URL Updates:** Update the project configuration files (`*.js`) for the 19 projects marked as **✅ Fixed**.
2.  **Add Alternatives:** For the 20 projects with a **🟡 Alternative Found**, decide whether to update the existing entry to the alternative or add it as a new reference.
3.  **Remove Dead Projects:** Remove the 30 projects marked as **❌ To Be Removed** from the project list.
4.  **Investigate Remaining:** Prioritize investigating the 22 projects still marked as **❓ Needs Investigation**.


  <svg width="800" height="400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 400">
    <!-- Font -->
    <style>
      text {
        font-family: Arial, Helvetica, sans-serif;
        fill: #333;
      }
      .tick-text {
        font-size: 13px;
        font-weight: bold;
      }
    </style>
    
    <!-- Define clip path to prevent overflow -->
    <defs>
      <clipPath id="chart-area">
        <rect x="60" y="30" width="700" height="310" />
      </clipPath>
    </defs>
    
    <!-- X-Axis -->
    <line x1="60" y1="340" x2="760" y2="340" stroke="#555" stroke-width="1.5" />
    
        <line x1="60" y1="340" x2="60" y2="345" stroke="#555" />
        <text x="60" y="360" text-anchor="middle" font-size="13" font-weight="bold" font-family="Arial, Helvetica, sans-serif">Jan 2025</text>
      
        <line x1="237.96610169491527" y1="340" x2="237.96610169491527" y2="345" stroke="#555" />
        <text x="237.96610169491527" y="360" text-anchor="middle" font-size="13" font-weight="bold" font-family="Arial, Helvetica, sans-serif">Feb 2025</text>
      
        <line x1="415.93220338983053" y1="340" x2="415.93220338983053" y2="345" stroke="#555" />
        <text x="415.93220338983053" y="360" text-anchor="middle" font-size="13" font-weight="bold" font-family="Arial, Helvetica, sans-serif">Mar 2025</text>
      
        <line x1="593.8983050847457" y1="340" x2="593.8983050847457" y2="345" stroke="#555" />
        <text x="593.8983050847457" y="360" text-anchor="middle" font-size="13" font-weight="bold" font-family="Arial, Helvetica, sans-serif">Apr 2025</text>
      
    
    <!-- Y-Axis -->
    <line x1="60" y1="340" x2="60" y2="30" stroke="#555" stroke-width="1.5" />
    
      <line x1="55" y1="278" x2="60" y2="278" stroke="#555" />
      <text x="50" y="283" text-anchor="end" font-size="13" font-weight="bold" font-family="Arial, Helvetica, sans-serif">1M</text>
    
      <line x1="55" y1="216" x2="60" y2="216" stroke="#555" />
      <text x="50" y="221" text-anchor="end" font-size="13" font-weight="bold" font-family="Arial, Helvetica, sans-serif">2M</text>
    
      <line x1="55" y1="154" x2="60" y2="154" stroke="#555" />
      <text x="50" y="159" text-anchor="end" font-size="13" font-weight="bold" font-family="Arial, Helvetica, sans-serif">3M</text>
    
      <line x1="55" y1="92" x2="60" y2="92" stroke="#555" />
      <text x="50" y="97" text-anchor="end" font-size="13" font-weight="bold" font-family="Arial, Helvetica, sans-serif">4M</text>
    
      <line x1="55" y1="30" x2="60" y2="30" stroke="#555" />
      <text x="50" y="35" text-anchor="end" font-size="13" font-weight="bold" font-family="Arial, Helvetica, sans-serif">5M</text>
    
    
    <!-- Data Line - black and bold -->
    <path d="M 60 307.893424 L 65.9322033898305 310.755716 L 71.86440677966101 311.775492 L 77.79661016949153 312.106324 L 83.72881355932203 316.101852 L 89.66101694915254 314.443042 L 95.59322033898306 314.581612 L 101.52542372881356 313.83227999999997 L 107.45762711864407 313.190766 L 113.38983050847457 314.808656 L 119.32203389830508 314.047606 L 125.2542372881356 315.66438 L 131.18644067796612 313.147614 L 137.1186440677966 270.941238 L 143.05084745762713 265.555422 L 148.98305084745763 260.077474 L 154.91525423728814 251.03080599999998 L 160.84745762711864 252.83246400000002 L 166.77966101694915 258.695866 L 172.71186440677968 263.02297 L 178.64406779661016 243.7729 L 184.5762711864407 221.39592199999998 L 190.5084745762712 219.33249999999998 L 196.4406779661017 212.660308 L 202.3728813559322 209.276286 L 208.3050847457627 216.21774399999998 L 214.23728813559322 217.035772 L 220.16949152542372 212.42861399999998 L 226.10169491525426 211.477782 L 232.03389830508476 202.34295 L 237.96610169491527 183.598614 L 243.89830508474577 184.419556 L 249.83050847457628 218.687142 L 255.76271186440678 221.55656399999998 L 261.6949152542373 221.197832 L 267.62711864406776 219.65924 L 273.5593220338983 226.11374999999998 L 279.49152542372883 229.810748 L 285.42372881355936 226.33254799999997 L 291.35593220338984 232.717556 L 297.2881355932203 216.738668 L 303.22033898305085 238.79988 L 309.1525423728814 228.636158 L 315.08474576271186 230.93759799999998 L 321.0169491525424 216.681256 L 326.9491525423729 211.722682 L 332.8813559322034 209.90285799999998 L 338.8135593220339 206.368548 L 344.7457627118644 217.889512 L 350.67796610169495 216.825034 L 356.6101694915254 209.605878 L 362.54237288135596 179.729814 L 368.47457627118644 188.103658 L 374.40677966101697 135.97101999999998 L 380.33898305084745 140.236868 L 386.271186440678 145.385906 L 392.2033898305085 144.754932 L 398.135593220339 175.555354 L 404.0677966101695 165.39237599999998 L 410 175.66558999999998 L 415.93220338983053 176.885874 L 421.864406779661 158.03341 L 427.79661016949154 175.397874 L 433.728813559322 150.87005399999998 L 439.66101694915255 152.245462 L 445.5932203389831 169.735786 L 451.52542372881356 163.583836 L 457.4576271186441 163.905306 L 463.3898305084746 154.07018399999998 L 469.3220338983051 165.419594 L 475.2542372881356 166.09527 L 481.1864406779661 183.039994 L 487.1186440677966 189.77462 L 493.0508474576271 188.628736 L 498.98305084745766 170.756678 L 504.91525423728814 173.200098 L 510.8474576271187 166.911624 L 516.7796610169491 189.152078 L 522.7118644067797 176.874776 L 528.6440677966102 146.025126 L 534.5762711864406 132.51297 L 540.5084745762713 147.73273 L 546.4406779661017 137.059306 L 552.3728813559322 130.145872 L 558.3050847457628 134.955088 L 564.2372881355932 138.81992 L 570.1694915254237 111.500674 L 576.1016949152543 138.936046 L 582.0338983050848 146.171198 L 587.9661016949153 146.658704 L 593.8983050847457 148.591182 L 599.8305084745763 161.595496 L 605.7627118644068 138.39584 L 611.6949152542373 79.65288599999997 L 617.6271186440678 93.09696599999998 L 623.5593220338983 142.30326599999998 L 629.4915254237288 158.102602 L 635.4237288135594 165.963334 L 641.3559322033899 171.59281 L 647.2881355932203 172.392548 L 653.2203389830509 123.71975799999998 L 659.1525423728814 111.656542 L 665.0847457627119 123.07905 L 671.0169491525423 113.56297999999998 L 676.9491525423729 108.44828999999999 L 682.8813559322034 123.072788 L 688.8135593220339 100.95000999999999 L 694.7457627118645 106.52715799999999 L 700.6779661016949 97.564686 L 706.6101694915254 93.24254199999999 L 712.542372881356 104.761088 L 718.4745762711865 94.31489399999998 L 724.406779661017 107.24486999999999 L 730.3389830508474 95.79539199999999 L 736.271186440678 69.29206599999998 L 742.2033898305085 63.09783199999998 L 748.135593220339 115.00782799999999 L 754.0677966101695 127.67622599999999 L 760 113.71822799999998" fill="none" stroke="black" stroke-width="3" stroke-linejoin="round" clip-path="url(#chart-area)" />
  </svg>
  
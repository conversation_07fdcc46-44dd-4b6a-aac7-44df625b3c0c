name: Resolve Issue with MentatBot

on:
  issues:
    types: [labeled]
  pull_request:
    types: [opened, reopened, synchronize]

permissions:
  contents: write
  pull-requests: write
  issues: write

jobs:
  call-mentat-resolver:
    runs-on: ubuntu-latest
    if: contains(github.event.issue.labels.*.name, 'mentat') || contains(github.event.pull_request.labels.*.name, 'mentat')
    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
      
      - name: Run Mentat Action
        uses: AbanteAI/mentat-action@v1
        with:
          openai_api_key: ${{ secrets.OPENAI_API_KEY }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
# Meeting Summary: Garlic & Pollinations Collaboration Discussion

## Company Overviews

### Garlic
- Developing contextual advertisement solutions for AI startups
- Started 4-5 months ago (around January)
- Currently has a Python library/SDK for contextual ads implementation
- Planning to develop an API for broader integration
- Looking for 10-20 beta testers

### Pollinations
- AI platform providing text and image generation capabilities
- Acts as gateway to various model providers
- Offers frictionless integration (no signup required)
- ~4 million monthly active users
- ~500 applications built on their APIs
- ~100 million API responses per month
- Currently subsidizing costs through cloud credits

## Potential Collaboration
Pollinations is exploring ad-based revenue model, while Garlic offers contextual advertisement technology:
- Pollinations needs help with the ad serving logic and advertiser network
- Garlic can provide the contextual ad network specifically built for Pollinations
- Implementation would happen in Pollinations' backend, with ads embedded in API responses
- Currently using referral links with tracking via redirects through Pollinations

## Ad Performance Data
- Click-through rates of 5.8%-10% reported, with some ads reaching 15%
- AI companion ads seeing highest engagement (15% CTR)
- Example calculation: 10k MAU app with 10 queries/day could generate ~$16k/month ($200k/year)
- Currently showing ads in ~5% of responses during testing phase

## Technical Considerations
- Ads primarily text-based with referral links currently
- Exploring image-based ads in future
- Implementation via Pollinations' backend with redirect tracking
- Data sharing and privacy considerations between multiple layers (Garlic → Pollinations → end users)
- Need for audience analysis phase (0.5-1 month) to understand user demographics

## Next Steps
- Both parties to evaluate collaboration potential
- Reconnect following week after Pollinations' investor pitch
- Potentially share more detailed metrics to help with Pollinations' pitch
- Pollinations mentioned interest in competitor "NEXT.ad" (raised $6M)

## Key Challenges
- Ad frequency optimization (avoiding overwhelming users)
- Contextual relevance and appropriate ad targeting
- Privacy considerations across multiple service layers
- Explaining the value proposition to potential advertisers

## Transcript
And we read this we got some information about it and we got it in the back of our mind, you know? And we were trying to iterate and trying to to build an interesting product around um LLMs um and we figured that um basically monetizing LLMs is pretty pretty complicated um There is a lot of different layers, a lot of different applications and the user end user will have to buy um an application for not taking, an application for generating something and uh another one. And that's twenty plus twenty, et cetera, you know, so that's not sustainable for the end user the in in retail. And we want to to to kind of help um well, AI startups that are building uh wrappers around at, uh to be able to implement contextual advertisement, um so that they can um easily monetize those without taking it directly onto the consumer. If the consumer allows it, of course, if you know, that could be turned off also. and so this is how we started and we started around uh, I' say, like four four months ago, some five months ago, something like that, but we are moving um, you know, quite slowly because we have um also on the side of our our our jobs. And so we first what we are doing is that we are trying to to test the market. We are trying to to to to put together a pool of data testers. So what we have is a is right now um um um we have some interest from uh from some uh some some companies. We have a dashboard um that we uh we can offer. So, you know, you can connect and either try to create a campaign to advertise your own company. Either you can um, well, implement the um as decades to to implement the contextual advertisement inside of your uh of your um application. um or you can do both one, by the way. um and um and that's where we are right now. And now we are really trying to refine and to improve a little bit our our um SDK because it's not everybody that uses the same language. It's not necessarily easy to integrate for everybody. Currently we have a python library, uh and we want to transfer that into an API that can be uh embedded by a by everybody. Yeah. So this is where we are uh where we are right now, um We are looking for, I would say, between 10 and uh and 20 better testers, uh to test it, um and um validate the the the demand Yes, for cool. Um, and and you have like a lot you have a network of like um advertisers you tap into um in terms of like uh how how how does that work? So, uh in terms of uh so we have different possibilities, right now we didn't we decided not to not to move to um to an external network. We decided to start building uh the two sides, uh little by little. um because of the fact that we want to make sure that we have the relevant um advertisers compared to the user base um of the applications in AI. Tot Yeah. Because we could we could look at um um, you know, um um let's say, a network an advertisement network and and just get their API and the list, you know, and then implement it into the contextual. But uh we don't want to move like this. We want to read it focus on the contextual part and make sure that. it's relevant to the user. I don't know if you saw on our website, we we we put a video about how this works. um if you're in one of application and you you talk about something, then it should really give you um a clear advertisement saying, okay, this is an advertisement. So it's not generated uh, you know, it's not knowledge, it's an advertisement, but it's super relevant to what you are discussing. and it should actually almost help you. It's almost like a recommendation rather than a YouTube thing that you want to just get rid of. Yeah, yeah, yeah. It makes certain sense because it makes a lot of sense to me because I've been kind of implementing something um similar for pollinations right now. um uh and um and I've been tackling exactly these questions, right? Like how do you make the ad um um context sensitive what you there's a lot of kind of potential I saw when I played when I when I um, let's say made our proof of concept, um of this. um maybe I do you want me to just tell you where we're coming from? um Yeah, sure. So so pollations today I is um um um and a platform, which um has a which allows people to access um both text and image generation models. We we we basically gateway to a to a variety of um model providers. And we offer a very simple and f friction list um um on boarding in the sense that you don't need to sign up uh even to our website. You can use you can use AI models that we provide um um directly, um let's say you can you just copy in paste a piece of code into your, let's say, Web web app or your um it's called BOT or your um or your uh or your uh or Roblox, uh games already, um using built onollination. So basically you can drop a little piece of code into your app and you can add an AI assistant or you can like add image um uh a generation functionality. And um and so we and the fact that it's so frictionless and free means that we have a lot we have a lot of users where we have um about four million monthly active users. So um which we are currently, of course, subsidizing through cloud cloud credits. But right now, we want to as so we we evaluated different um business models and um we came we are now um um building a business model, um and pitch around an art based revenue model. And we've also been making our first tests. So so I could also just show you something um but we so so we have a lot of we have a lot of traffic and and we've just we've we've um I don't know if it's easier if I if I show you an example, um because basically we have like, let's say a bunch of websites that have, let's say chatpots that are often free and that use pollinations in the back end. And there we are we are we are experimenting with embedding advertisement into directly into the responses of the of the chatpot. And these advertisements are context sensitive and we experimenting right now with the pool of about we have like 30, 30 um uh random, let's say, referral links, uh you know, one for North VPN, one for um one for uh uh, let's say, a language learning app, um one for uh, you know, a I companion, this one is clicked a lot. um um but we but but so we're experimenting with this with directly embedding ads into the into the responses. And I will just show you very quickly a screenshot so you so you so you understand how it looks. um So let me just share this. Let me just open the big spot. So so we have we have a very large community um because we are on boarding is so easy. We have a very large community of creators who are building on top of pollations. and we so we have about 500 up small ups that the people have built on top of our APIs. So there's a lot of places where let's say our our responses go. And I'm showing you just one example uh um here. uh So this is an let me just share a screen there we are. um I would just I would just go, can you see it? Yeah. I would just go to Bing because this is one member of our community who is very good at at SEO. So he if you search for AI chat with images on Bing, uh the first result is PixPA and PixPal is built on Polyn uses pollinations in the back end. And so I can shut him and the address is the example I do now. Hey, can you um do more? I only the big Sorry?, sorry, I'm in the wrong window. It opened in a new window. I need to I don't know why it's doing that. one second uh opening. Okay, I just have to share the window. Give me one. present something else. Uh huh. There we go. Are you sing yeah, you sing me typing now? yes. Okay, um so here this is um this is a chatpot which uses pollinations. um and now I can say, hey, it does it can both respond and it can also make images. So I would say,ey, can you uh make an image of a beautiful holiday destination? Um, and it will make an image and then I'm gonna already and then I'm I'm gonna tell it, wow. This is is beautiful. What language is spoken there? I'm just it's a silly example of it, right? Um, I want to learn it. Um, and now I have a trigger word to trigger art system. This is just for the building. And and now what happens is here we get the the response and then underneath. um Okay, it embedded uh a link um to to talkal, which is a language learning, learning up. And so if I click this now, then I can I need to if I sign up, then of course the ideas that we then get a get a revenue share it's I have this actually, this is an old version of our art system so what we're doing now is we we're actually using the language model to rewrite the sentence to be so so if you're talking about learning French, um it will it will say, hey, if you want to learn French, um please uh check talk part. So we have actually uh one one step further where it's actually personalizing the advertising message to the user And that's what we're doing right now, but we're thinking like this logic, right, which does the mapping between what people talk about and connects us to advertise. It's a very big, big work and we are small startup, you know, and and so that's why I've been looking for partners who we can basically outsource this this um logic too, you know, and we can focus on our ecosystem, we can grow our our the number of creators in the up and um and the whole chunk of like mapping from context to to ad is done by someone who specialized in that, right? And that's why I thought from what I read about you, this sounded quite good to me like as a potential opportunity. That makes sense. Yeah, that makes a lot of sense to me because obviously you can get away with the first part of um of of building the contextural advertisement engine. But then there is the what the maintenance of it and there's the growing the network of and this is where we realize actually, if I tell you the truth, we started in around um January to rebuild. I think that's right, mate, right? January, we started to build the the platform and even the we had the SDK. And February, we finished building it. And since then we are working to get people on board advertisers, and all of that, it actually does what takes the most time because people are not used to well, to this type of thing, it's very new. We have quite a difficulty, honestly, um and we're also a little bit technical, um to to to explain it to people. uh like it seems it seems obvious once you've kind of like made the connection, but to actually get people to understand it is not so easy. But yeah, yeah, yeah, please continue. yeah, yeah. Yeah, and it's it's it it takes time. It takes a lot of time basically. It's um I think people will get to understand it. They will implement it advertisers, uh, they will do it, you know. We started targeting startups, of course, to advertise because they they want to advertise, but we started also targeting for bigger partnerships. So um companies that uh manage the marketing of several tens and tens of hundreds of other companies, you know, uh So that they have one account on garlic, um and then uh they spread the campaigns uh all over there. and what would happen is that then they beat on those campaigns um and there's the whole beidding system behind in the back hand. um and there, um we would automatically take the relevant score um and the the being and distribute that towards the network of um of applications like big sub, for example.. So this is a actually a path because we don't come we don't come from advertising, right? is the path we we are not experts in and that's exactly, you know, what we realize also. there there's all these kind of like market dynamics between builders and and and buyers, right? And so yeah, yeah, exactly. So that's that's that's where we want your want so your health probably. So but do you see any any uh do you see any like, uh like maybe it also depends a little bit on our demographics. We have a lot of data, you know, about because we can we can also like we we let's say analyze also the conversations people have in our logs to figure out like, okay, for example, the language learning, I put it into our inventory because I saw a lot of people there's a a significant percentage that talks about language learning, right? And so there's a lot of data that that we'm going to use, I guess, right? to um to me it sounds like we quite a nice match if if if I don't know what you think. Is there anything that do you see this as a as a as a as a good idea to make some kind of um collaboration together? Yeah, I think I think it I think it would be interesting to be honest. It's it's only about the only thing that we need to to pay attention to is the implementation because um you um then you are pollinations and basically your users are not your users, but the users of your users, right? So behind the scenes and then we have then three layers there. There is us um that or are as decades to work, we need to process the quiry. Yeah. And then the choiry is in our hands. Then we we do have data. So then you need to agree that we process that data, uh, and for you you, your users need to agree that, well, uh you process their data and you also give it to a third party of. Yeah, yeah, yeah yeah one thing that we need to be careful of. um and um, and then the second thing would be um seems it's not really you that is going to implement, I guess there is the case, it's more going to be and either embedded into pollinations but the way the way it's well just sorry to interrupt you, but the way it's running right now is um that we're doing all this in our back and. So so what our clients they they just get a response from theI, which includes the the the the ad and we are also to we are talking with, let's say, the apps that have more users. um and and with them we are we can also send like um we can agree on a specific um way to send them um uh this like content, depending on if it's just if it's a, let's say, in a media or if it's a texter we have the I think I unless that doesn't make sense, right? But ideally we do this all in our back end and our users just get a get a response with the ad embedded in it. Okay. All right. One thing, though, in terms of what those are technical things, we could maybe discuss out. Well, in terms of maybe, you know, um clicks, uh data. Well, okay, that that's a little bit I'm also always interested in in in in technical questions if you do want to go into it. um I mean just maybe one thing for me to understand is, um for example, a big spa, um that company, if one of their users clicks on um on the link, we would still get the information made, right? We would know that it has been clicked, I guess. yes.. I would know. Okay. Yeah. It just because we need to make sure that we can have the the views count um and the data, uh of uh what has been uh licked so that we can price it. um Yeah, yeah. I mean, what we're doing what we're doing right now is that it refer the referral link we're putting into the response. It doesn't go to Pixal directly. It goes to pollinations and which then redirects to Pixal. So that's how we that's how we register and how we calculate like um um how many people clicked on the ad. Because if we were to send them directly to PPal, of course we could not we we would not know Apart from in Pixpal's dashboard, right? We would not know how many people clicked. So we send it through our back end via read a redirect.. Yeah, uh. And that and another thing that would be important for us to understand is in terms of users, um yeah, I guess because the if we were to collaborate and work together, what we would do is then focus on serving the user's uh of your users in terms of advertisements. So then we would need to advert to kind of understand we'll probably start with um a period of um I don't know, maybe a half a month or one month to gather data and understand who they are and what they are doing. uh so that we can go and reach out and talk with advertisers saying, hey, we've got to pull off a that many people in that many countries are. And um they are just waiting uh to get advertice to, you know, yeah, yeah, it will be important part the next I mean, that makes a little sense, you know, like, I mean, we've been doing that kind of casually without like, making a a a a very systematic analysis, right? Now, how do you how do you what format do the ads you you or you are going to serve or you are already serving, how how are they like rich, rich media? Are they like a h for like irames or HML arts or are they are they do they work also via um referral links? How how how does the kind of this part of the system work? So at the beginning we we start with um basically referral links and uh um click rate. I mean clickased uh manes. So it's basically um a text under, you know, that says, hey, this is a very relevant to what you're talking about and you hear uh it's gonna be great for you. Yes, yes, yes, yes. We are exploring um of course, um well, media, um in terms of um image based, definitely. where um exploring um embedding details directly and so it that would be a part as well, but I will come later. Yeah, to be honest right now we really focus on text. Yeah, I mean that's that's that makes a lot of sense to us because that's that's what also Paris is quite good, easy because we we're sending like most of our clients receive a markdown. I mean, it gets a bit technical, right, but most of our most of our clients can receive formated text. So that's why we make it also very, very easy by, you know, by adding the here you see in the video. It's personalized the ads. So it said, I was talking about Greek and it even because we have image generation. It's a funny test, right? It even made an image, um and it head like, hey, if you want to learn Greek Greek go to talk. So this is with the archients don't need to do anything because since they are already receiving format texts from our API, we're just we're just adding this ad with with our kind of custom link and the leaf. But as I said, we have certain clients with which we're thinking about the deep integration where we could serve even the video ads, you know, and they would then receive it and and um displayed where it makes sense, you know, on their on their website. And that's exactly why I was saying, well, you know, if you have 500 applications that are created on top of pollations then each of those applications are different. and they all have a different user experience you can see actually the I don't know if you can see the here I made some some screenshots of of different applications. Of course, these are more the landing pages, but you get an idea that they are all quiet um quite different. Yeah,, I think it's interesting. I think we could uh we could try and um and collaborate uh together on this um I mean, the value that I can see for us bringing here is really the um serving, you know, uh contextually an ad network. But a not network that is built for you uh for your. That's where I can think we could bring volume. Yeah, I mean that's that's that's great. for us you know, because it's um something that then allows us to focus on on our point, you know, and um and it's not something we are expected, right? So and I think we really have like crazyaction. We've been growing we've been growing from um we've been growing we've been growingirty every month. And we're now at like, I think this even an underestimation, we're like we're generating like hundred million uh IP I responses per month. So I for you, it could also be interesting because we we are already operating at like kind of quite a scale, you know and then the more, of course, the more scale you have the the more you can also collect data um uh an optimize um things. So to me, it makes a lot of sense. Yeah. Yeah, yeah, yeah, how should we proceed? Maybe we think um a few days to to think about how we can work together and uh have set up, maybe the next steps. I mean it's you you're using pyon, right? You could integrate the vit. Yeah, that's no problem for us like we have we because we we have like a um we do we're cashing where we've got a bunch of services running in the clouds, so some things already are in pipe and some things are notort S But that's I think that's for us, it's not such a big deal, like which language is in. Yeah, and maybe there's one thing like, yeah, we we also quite busy because we we're fundraising and we're gonna pitch on on Friday and two days to work to an investor. So probably first again towards the end of next week, um maybe we could kind of all, yeah, next week we could get back in touch. Yeah, I mean, there's a there's a few questions like where like it could help us for our pitch, actually to have a few numbers, you know, in terms of like what's what kind of CPMs uh and so on. Do you do you estimate? Do you do you have this kind of kind of data? Yes. um So I uh I didn't manage to put it uh inside the the application. See, I knew I should have put it there. You like you are like us. We are But but I mean I'll I'll I'll share my screen and I know quickly show you from trying some stuff here. I'm really sorry by the way, it's like, um don't don't write right when we we with asensions usually when we're when So you're gonna see my uh my my vi coding map because I'm actually not the coder here is my I I'm just doing stuff on the side, but uh and she had to fix all the mess you make. it's No, no, no, it's not it's let me just uh shall I stop sharing wait let me just stop my screen sharing um. Are you already, but I'm lost in the taps. I know. Where was it?? Okay. Where are youass? By the way? Just a out of curiosity? Um, so well, I mean, we um we want to um we want to I mean, the company, uh, is supposed to be based in in the US. um we want the company to be based in uh in in San Francisco, but physically, right now, um I am based in Zurich and mine is based in C. in. Cool, cool. Are you are you part of the speed run? Uh, the ACI did you up did I or did I confuse you with another company? Did you get the this startup accelerated? We did apply. Oh, okay, we did Yeah, I didn't either yet. how do you know? I know, because I was I was researching um um I got I came up with a list of like four interesting companies, like from my research and you are one of them and there was another one that uh that was um um funded by that was already like in a previous cohort of speed run of the Asex in Z. And because we kind of applied to it too, I I thought I couldn't remember if it was you or or this other company. I can send you them if you want to know. um yeah, I would like. Yeah, yeah, I' doesn't changed. that? Okay., lets. Um, so I just built that quick little thing. um usage metrics, yeah, revenue forecast. Or here, you could have an example, and that's why I'm saying, you see, the application type that is using that is built on top of Polination matters or the click through rate. Yeah, yeah.. Yeah. So it depends on what is gonna be. But let's say, I don't know, uh language learning, for example. Oh yeah, that's a language learning and you have um I like how many monthly active users you let's say, well I think this app maybe it has about ten ten ten thousand? 10 thousand? Mhm. and thousand and average quiries per user per month we could actually check in in our data. I don't know no, no, in my of my head, but let's just assume something like um um fur or or or yeah, let's just do one per day. Okay, because maybe people use use it like five or maybe actually more because one session usually is a few is a few uh responses. So maybe ten per day. Yeah. So, well, with this with this data then, um you would be um with a click for rate of uh 5.8%, uh and a revenue per clique at 0.8 be roughly at around 16 catre per um per month, 200 K annual, but that would be only for that specific app. yeah. we were just working on this tables because we trying to calculate this too yeah, very very very cool, very cool to see um yeah, yeah. And do you do you already do you already like, is this data based on like a simulation or do you already like feed this with kind of real um real feedback your you've already started getting. based on the market and the industry. knowing that we expect because it's contextual, we we expect more um clicks through. um and uh and therefore we expect that the building would be higher. uh or a click. Yeah, if you would be ready to beat much more to get there, you know the fact that well, you know, it's it's better for uh for the the possibility for the user to actually buy that, buy as I can I can actually show you some data because we've already we've already kind of rolled a rolled out out system. uh to and we already have some conversion conversions from these ads. And they're actually the clickuates are quite quite uh high. We depends on the advertiser, but we've seen something like 10% for some some advertisers. And even one an a funny thing is the one that the highest, it's 15 15 percent click through, and that's for the AI companion um like the kind of AI girlfriend girlfriend, uh yeah, yeah that's that one is is is probably also in that application that is specifically designed for AI, right? I mean, but you know what? We we' not we're not we should use that. That's that's actually a really good point. but we're using the conversation. the content of the conversation to determine what types of ads apply. So we so we we pass it through a kind of a a language model to say, okay, this conversation is about, let's say, romance, you know. And if the language model determines that this conversation is about romance, then we we we showed the the out of the AI companion. But it's a little bit problematic because we really don't want this to appear in an app, which is for teenagers, for example, right? So we should we should be using more than just the conversation um um content, you know, like yeah. that's the problem that we also figured is um, and this is why it's something to be keep I mean, we need to keep on working on it and we need to keep on improving it because, for example, you don't want um someone uh that asks advice about the current relationship that he has with his girlfriend to get an advertisemement about just replace her with a eye.. You have to be very careful. Yeah, yeah, it becomes very black mirror. There's some parallels, some black mirror episodes. I'm I'm I'm seen. Exactly. Yeah, yeah, yeah, yeah, yeah. The last one, right with the woman in the that ends up, yeah with the part of the bread in the cloud, right? Yes. You know what? so crazy that yesterday I was testing our system and I realized that since the if theot puts that in its in its response and and then the conversation continues, then it it starts referencing the ad, right? And then I realized, okay, I'm what I'm going to do in our back end is I'm going to remove the ad from its context, you know, before it before it responds to so then basically the same experience, uh, the woman was having, which is like, you're like, hey, why did you reference um um um a nordVPN, right? And it's like, what? I never said that. I I really felt like there was there was a quite a power level going on. to be honest when I saw that the episode that was I was feeling very bad need to some a bit of time I was like, am I actually working on that, you know? I mean, but but I mean that also like when I look at for example, the ad for the language learning, right? And there's a and there's actually some really kind of good products, you know, that that you can recommend. So and for us, like for our community, they they would much rather see ads than put their credit card and do one more subscription, right? Like so we we surveys and an 80% of our community uh would much rather an add supported system than one that they have to pay for. So, I mean, you have to choose one evil, right? Like how you make um not, you know, and and it all depends on how you frame it, you know, and what kind of content in the end you, because in the end users are also gaining something which is access to something which would be monetized, right? For for free, let's say indirectly. So, yeah. Yeah, I mean, to be honest, I agree 100%, I think we could have the users could, in the end, um, not only get an advertisement, but actually a recommendation and uh get activity. And it be translated, you know, that fable to something much bigger than just advertisement. It could be a full set of recommendation, uh, and embedded into into each application, depending on and this is where userained needs to be, but depending on what the application is doing. Yeah, yeah, yeah. We can also work towards actually making it less fri I mean, more friction less for them, um they could see one advertisement every 10quiries, you know, uh or every or something like that, we could reduce that, you know, what what kind of that'm I'm curious because we also um playing with these numbers, um uh did you find any kind of like, like uh a point? So far, our users haven't complained much about that because they're quite like an intrusive in a way, you know? But that so we're putting it right now just because we're testing uh to about only like about, let's say 5% percent of responses, right? Because it's more like how much is the kick rate, we don't need to, let's say, put it I'm curious, like, how how you put it how you determining that number, like, how how often to show at because when you're doing with a chatot, you've got a conversation, right, and you don't want, let's say, in every response to having an advertisement, you know, do you have any any like view on that? I mean, we do have a view, but we't yet to be and of course it's I mean, the more you show advertisements, the less they are gonna be clicked. Therefore, the less advertis are gonna be ready to pay for click. And it's an algorithm that needs to be designed. Uh, so make sure that the customer stays and keeps on using the same amount of quiries, you know. the click crew is the highest and the beard is the highest. It's just an algorithm between those three.. And but we need to test it and yeah. yeah, yeah, yeah, totally, totally. Yeah, interesting. I mean, uh it's yeah, I'm I'm quite a quite happy to talk to you just because there's very few people I've talked to who've really like tried to solve exactly this problem, right? So, yeah, I mean, I would I'd be super happy to continue the conversation, um afterwards. Do you have any other, like questions or something you or or are we No, I think they're good. Um, maybe just um you yeah, I mean, uh if you can send us the um the name of that company that you you saw I will tell you right now, um they are there they look quite slick, let's say. um give me one second. And I think also like I think there if I think we have a probably a lot of data that could be interesting to you because we have uh we're saving um conversation and not anonymized without association to a user, um but for different apps, you know, so we have one app which is like focused on there's an app that's that's focused on like um uh there's all kinds of niches, right? There's certain apps that are in games, there's there's an that's um focused on on on food ingredients, you know, so yeah, yeah, and yeah, maybe you you also have some things that can help us for even for our pitch, you know, even this even this conversation is really valuable for for our pitch. Let me just I'm just going to find this this company and then I think we can also move on. uh one second, it was an advertising partners here. We have a partners document. The company was, um next ad, I think. NEX.A. Let me just open it. Yes. um Yeah, yeah, yeah, yeah. You find raised six millions through Exactly, exactly. Yeah, yeah, yeah. Okay. Well, we didn't know about that. Yeah, I mean I think it's quite I because we I was searching this kind of companies and and I couldn't find some like I think it's all quite recent that they that this this is emerged. Yeah, yeah, yeah, yeah, yeah. Interesting. Very, very interesting. Um, okay, yes, so, I mean, if you need any other things for the for the beach, you know, don't hesitate to I mean, if you need to put us as partners, if you need to more data about the potential forecast or whatever, don't hesitate to reach out to us. And um and yeah, good luck for your uh for your peach. Thank you so much. I think volations is great. I think uh serving um people that want to build things for others and growing a community is super super cool. I'm gonna use that one. I'm going to use that happens in a pitch., you' yeah, you just we can connect maybe after that, and maybe I will have it next thing. Either way either message me or I will message you, um next week sometime, okay? And maybe I will have some numbers questions I will just send you an email, but also no pressure to respond. Yeah. Thank you. All right. It was nice to meet you. You too. bye-bye. Have a good day. I'm yeah. bye. Disgusting some feels fabulous.

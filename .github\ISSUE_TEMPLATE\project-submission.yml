name: Project Submission
description: Submit your project to be featured on Pollinations
title: "[Project Submission]: "
labels: ["project-submission"]
body:
  - type: markdown
    attributes:
      value: |
        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 24px;">
          <img src="https://raw.githubusercontent.com/pollinations/pollinations/master/operations/assets/pollinations_ai_logo_text_white.png" alt="Pollinations Logo" width="100%" style="display: inline-block;">
          <h1 style="margin: 0; font-size: 24px; display: inline-block;">Project Submission</h1>
        </div>
        <div style="font-size: 1.25em; font-style: italic; margin-bottom: 12px;">
          Thanks for taking the time to submit your project! Please fill out the information below to help us understand and showcase your project.
        </div>

  - type: input
    id: project-name
    attributes:
      label: Project Name
      description: What is the name of your project?
      placeholder: "e.g., AI Art Gallery"
    validations:
      required: true

  - type: textarea
    id: project-description
    attributes:
      label: Project Description
      description: Please provide a brief description of your project and how it uses Pollinations
      placeholder: "Describe what your project does and how it integrates with Pollinations..."
    validations:
      required: true

  - type: input
    id: project-url
    attributes:
      label: Project URL
      description: Link to your project (if public)
      placeholder: "https://example.com"
    validations:
      required: true

  - type: input
    id: contact-info
    attributes:
      label: Contact Information
      description: How can we reach you? (Discord username, GitHub username, email, or social profile link)
      placeholder: "e.g., @username or https://profile.example.com"
    validations:
      required: true

  - type: input
    id: github-repo
    attributes:
      label: GitHub Repository URL
      description: Provide a link to your project's GitHub repository (if available)
      placeholder: "https://github.com/your-repo"

  - type: dropdown
    id: project-category
    attributes:
      label: Project Category
      description: Select the category under which your project should be posted
      options:
        - Chat Projects 💬
        - Creative Projects 🎨
        - Games 🎮
        - Hack & Build 🛠️
        - Learn 📚
        - Social Bots 🤖
        - Vibe Coding 🌈
    validations:
      required: true

  - type: textarea
    id: additional-info
    attributes:
      label: Additional Information
      description: Any other details you'd like to share about your project?
      placeholder: "Screenshots, features, future plans..."

  - type: markdown
    attributes:
      value: |
        ### Implementation Instructions

        For the person implementing this issue:
        - Add the new project to the top of the appropriate file in pollinations.ai/src/config/projects/ based on the selected category
        - Ensure the project entry includes all necessary fields (name, url, description, author, submissionDate, etc.)
        - Make sure to maintain proper JSON formatting with commas between entries
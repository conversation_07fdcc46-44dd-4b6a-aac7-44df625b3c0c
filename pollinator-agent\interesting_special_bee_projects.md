# Interesting Special Bee Projects

This document highlights notable projects from special bee requests and tracks which ones have been added to the `projectList.js` file.

## Summary of Progress

### Projects Added to projectList.js (May 12, 2025):
1. Snapgen.io - Free AI image generation website
2. Mirexa AI - AI companion for chatting and creating
3. Neurix - Platform for accessing neural networks
4. Free AI Chatbot & Image Generator - Mobile app on Google Play
5. KoboldAI Lite - Lightweight web UI for AI text generation
6. tgpt - Command-line AI chatbots
7. Define - API for generating definitions
8. BullShi3ldAi - Frontend-based AI interface
9. Just Build Things - Collection of free AI tools
10. Generative AI Images Gallery - Gallery for AI-generated images
11. Aura Chat Bot - Chat bot with text and image generation
12. AI Image Generator [ROBLOX] - Roblox-based image generator
13. Modern AI Image Generator - Web-based AI image generator
14. Free AI Image Generator - Platform for text-to-image generation
15. Infinite World: AI game - Interactive graphic story game
16. Quick AI & Jolbak - Discord bots for users in Iran
17. Children's Picture Books Plugin - Plugin for creating children's books
18. MyPicGen - Tool for video thumbnail generation

### Projects Already in projectList.js:
1. Raftar.xyz - Discord multi-purpose bot
2. Dreamscape AI - AI assistant with voice capabilities

### Projects Still to Be Considered:
1. StoryTelling App
2. Empowerverse
3. ElxrAI
4. CoNavic
5. Mr. Kaks Discord Bot

## Recent Tier System Migration (May 31, 2025)

The Special Bee request process has been successfully migrated to the new tier-based authentication system. **We are now being much more selective** - most users register at `auth.pollinations.ai` for seed tier access, with only exceptional projects flagged for flower tier upgrades.

### Updated Approach: Selective Flagging

**NEW POLICY: Most requests get standard responses (95%)**
- Default response directs users to auth.pollinations.ai
- Only truly exceptional projects get flagged for flower tier
- Focus on established businesses, educational institutions, and proven teams

**STRICT FLAGGING CRITERIA:**
- Must have multiple indicators: live website + business model + professional team
- Educational institutions with existing user base
- Companies with clear revenue potential
- Proven track record or significant innovation

### Recent Processing Results (May 31, 2025)

**FLAGGED FOR FLOWER TIER (Exceptional cases):**
- **#2147 (udeki.com)** - Educational platform with CEO request and live website 
- **#2091 (School District Query)** - Educational platform with existing user base 
- **#2062 (pinblogai)** - Content creation tool with live website and business model 
- **#2084 (Wisdom-Core)** - Educational AI tutor with live website
- **#1957 (A4F)** - Detailed project with clear use case

**STANDARD RESPONSES (Majority):**
- **#2137 (ABU AI)** - Cross-platform AI assistant (revised to standard)
- **#2190 (UnrestrictedGPT)** - Discord bot, minimal description
- **#2080 (GPT-API)** - Minimal description, personal use
- **#2075 (AI brainrot image)** - Personal use, no GitHub repo
- **#2031 (ahmadi)** - Minimal "dev bot" description
- **#2168 (MODA)** - Minimal description, no GitHub repo

### Revised Success Metrics:
- **25 requests processed** since tier system launch
- **6 flagged for flower tier** (24% - more selective approach)
- **19 standard responses** (76% redirect to auth.pollinations.ai)
- **Quality over quantity** - focusing on truly exceptional projects
- **Educational platforms and established businesses** prioritized

## Notable Projects

### 1. Snapgen.io (Added to projectList.js)
- **Project Description**: A free AI image generation website
- **URL**: https://snapgen.io
- **Contact**: <EMAIL>
- **Domain/Token**: snapgen.io
- **Why Interesting**: Professional image generation service with a clean interface
- **Issue**: [#1936](https://github.com/pollinations/pollinations/issues/1936)

### 2. Mirexa AI (Added to projectList.js)
- **Project Description**: A friendly AI companion for chatting, creating, and exploring with no sign-ups or fees required. Features include image generation, story brainstorming, real-time web search, and voice messaging.
- **URL**: https://mirexa.vercel.app
- **Contact**: <EMAIL>
- **Domain/Token**: mirexa.vercel.app
- **Why Interesting**: Comprehensive AI assistant with multiple features in a user-friendly interface
- **Issue**: [#1489](https://github.com/pollinations/pollinations/issues/1489)

### 3. Neurix (Added to projectList.js)
- **Project Description**: A website offering easy and free access to various neural networks, with multi-language support planned
- **URL**: https://neurix.ru
- **Contact**: Telegram - @YouRooni, GitHub - Igroshka
- **Domain/Token**: neurix.ru
- **Why Interesting**: Multi-language platform for accessing various AI models, including Pollinations
- **Issue**: [#1553](https://github.com/pollinations/pollinations/issues/1553)

### 4. Raftar.xyz (Already in projectList.js)
- **Project Description**: A Discord multi-purpose bot with over 10,000+ user installs and 650+ guild installs (about 180k members)
- **URL**: https://raftar.xyz
- **Contact**: Discord - @goodgamerhere
- **Domain/Token**: raftar.xyz
- **Why Interesting**: Large-scale Discord bot integration with significant user base
- **Issue**: [#1607](https://github.com/pollinations/pollinations/issues/1607)

### 5. Free AI Chatbot & Image Generator (Android App) (Added to projectList.js)
- **Project Description**: A free AI Chatbot and Image Generator mobile app powered by pollinations.ai
- **URL**: https://play.google.com/store/apps/details?id=com.aichatbot.free
- **Contact**: @andreas_11
- **Domain/Token**: FreeAIChat
- **Why Interesting**: Mobile application on Google Play Store with both chat and image generation
- **Issue**: [#1663](https://github.com/pollinations/pollinations/issues/1663)

### 6. StoryTelling App
- **Project Description**: A story app using AI for text-to-speech and image generation, planned for Android and iOS release
- **Contact**: .gemu. on Discord
- **Tech Stack**: Vue.js/Firebase with Capacitor for mobile apps
- **Why Interesting**: Creative application of AI for storytelling with mobile distribution plans
- **Issue**: [#1714](https://github.com/pollinations/pollinations/issues/1714)

### 7. Empowerverse
- **Project Description**: AI-powered platform for creating and sharing content
- **URL**: https://empowerverse.org
- **Domain/Token**: image.empowerverse.org
- **Why Interesting**: Content creation platform with sharing capabilities
- **Issue**: [#1619](https://github.com/pollinations/pollinations/issues/1619)

### 8. ElxrAI
- **Project Description**: AI-powered platform for creative content generation
- **Domain/Token**: elxrai
- **Why Interesting**: Creative content generation tool
- **Issue**: [#1562](https://github.com/pollinations/pollinations/issues/1562)

## Additional Notable Projects

After examining more special bee requests, these additional projects stand out as interesting candidates for inclusion in the `projectList.js` file:

### 15. BullShi3ldAi (Added to projectList.js)
- **Project Description**: A frontend-based AI interface designed to deliver a smooth, multimodal, and visually engaging user experience with conversational AI, image generation, and more
- **URL**: https://bullshield.onrender.com/
- **Contact**: https://t.me/BullShi3ld
- **GitHub**: https://github.com/BullShieldTeck/
- **Domain/Token**: BullShield
- **Why Interesting**: Comprehensive frontend-only AI interface with multiple features and responsive design
- **Issue**: [#1860](https://github.com/pollinations/pollinations/issues/1860)

### 16. Just Build Things (Added to projectList.js)
- **Project Description**: A collection of free AI tools including AI chat, writing tools, image generation, image analysis, text-to-speech, and speech-to-text
- **URL**: https://justbuildthings.com
- **Contact**: <EMAIL>
- **Domain/Token**: justbuildthings
- **Why Interesting**: Comprehensive collection of free AI tools with multiple functionalities
- **Issue**: [#1818](https://github.com/pollinations/pollinations/issues/1818)

### 17. Generative AI Images Gallery (Added to projectList.js)
- **Project Description**: A gallery of AI-generated images created from text prompts
- **URL**: http://ai.kochini.com
- **Contact**: <EMAIL>
- **Domain/Token**: ai.kochini
- **Why Interesting**: Dedicated gallery for showcasing AI-generated images
- **Issue**: [#1831](https://github.com/pollinations/pollinations/issues/1831)

### 18. Aura Chat Bot (Added to projectList.js)
- **Project Description**: A chat bot integrating Pollinations API for text and image generation
- **Contact**: <EMAIL>
- **Domain/Token**: @Py-Phoenix-PJS
- **Why Interesting**: Comprehensive chat bot with both text and image generation capabilities
- **Issue**: [#1785](https://github.com/pollinations/pollinations/issues/1785)

### 19. AI Image Generator [ROBLOX] (Added to projectList.js)
- **Project Description**: An image generator on Roblox that integrates with Pollinations APIs for text and image generation
- **Contact**: Discord: @mr.l4nd3n
- **Domain/Token**: l4nd3n
- **Why Interesting**: Unique integration of Pollinations API within the Roblox platform, processing images pixel by pixel
- **Issue**: [#1755](https://github.com/pollinations/pollinations/issues/1755)

### 20. Modern AI Image Generator (Added to projectList.js)
- **Project Description**: A web-based AI image generator with unlimited generations, 150+ image styles, and multiple customization options
- **URL**: https://imageai.techlasiya.com
- **Contact**: @techlasiya
- **Domain/Token**: imageai.techlasiya.com
- **Why Interesting**: Feature-rich image generation platform with extensive customization options
- **Issue**: [#1754](https://github.com/pollinations/pollinations/issues/1754)

### 21. Free AI Image Generator (Added to projectList.js)
- **Project Description**: A free platform for transforming text prompts into high-quality images without requiring an account or design expertise
- **URL**: https://free-ai-image-generator.vercel.app/
- **Contact**: Discord: zahidulhaque
- **Domain/Token**: free-ai-image-generator
- **Why Interesting**: Accessible image generation platform with no account requirements and unlimited generations
- **Issue**: [#1712](https://github.com/pollinations/pollinations/issues/1712)

### 22. Infinite World: AI game (Added to projectList.js)
- **Project Description**: An interactive graphic story game where player choices shape the world, using Pollinations APIs for text and image generation
- **URL**: https://wuxiangs.pages.dev/
- **Contact**: <EMAIL>
- **Domain/Token**: wuxiangs
- **Why Interesting**: Creative application of Pollinations API for interactive storytelling and game development
- **Issue**: [#1694](https://github.com/pollinations/pollinations/issues/1694)

### 23. Quick AI & Jolbak (Added to projectList.js)
- **Project Description**: Discord bots providing AI services to users in Iran who have limited access to AI tools like Claude, ChatGPT, and Gemini
- **URL**: Discord bots
- **Contact**: @d__mx
- **Domain/Token**: Quick AI, Jolbak
- **Why Interesting**: Provides AI accessibility to users in regions with limited access to mainstream AI tools
- **Issue**: [#1928](https://github.com/pollinations/pollinations/issues/1928)

### 24. Children's Picture Books Plugin (Added to projectList.js)
- **Project Description**: A plugin for creating children's picture books using Pollinations API to transform creative concepts into captivating picture book content
- **URL**: https://www.coze.cn/store/plugin/7499512842995974194
- **Contact**: <EMAIL>
- **Domain/Token**: cizz_e_book
- **Why Interesting**: Educational application of Pollinations API focused on children's learning and engagement
- **Issue**: [#1931](https://github.com/pollinations/pollinations/issues/1931)

### 25. MyPicGen (Added to projectList.js)
- **Project Description**: A tool for content creators to generate images for video thumbnails
- **Contact**: <EMAIL>
- **Domain/Token**: pic.941125.eu.org
- **Why Interesting**: Specialized tool for content creators focusing on video thumbnail generation
- **Issue**: [#1758](https://github.com/pollinations/pollinations/issues/1758)

### 9. KoboldAI Lite (Added to projectList.js)
- **Project Description**: A lightweight web UI for AI text generation with 100K+ monthly users, offering a clean interface for text generation and now image generation using Pollinations
- **URL**: https://koboldai.net
- **Contact**: LostRuins (GitHub) or @concedo (Discord)
- **GitHub**: https://github.com/LostRuins/lite.koboldai.net
- **Domain/Token**: KoboldAiLite
- **Why Interesting**: Large user base (100K+ monthly users) and established AI text generation platform now integrating Pollinations for images
- **Issue**: [#1899](https://github.com/pollinations/pollinations/issues/1899)

### 10. tgpt (Added to projectList.js)
- **Project Description**: AI Chatbots in terminal without needing API keys
- **URL**: https://github.com/aandrew-me/tgpt
- **Contact**: Discord username: andrew.me
- **GitHub**: https://github.com/aandrew-me/tgpt
- **Domain/Token**: tgpt
- **Why Interesting**: Command-line interface for AI, appealing to developers and terminal users
- **Issue**: [#1905](https://github.com/pollinations/pollinations/issues/1905)

### 11. Define (Added to projectList.js)
- **Project Description**: An AI-powered REST API designed to generate definitions for words or phrases, constrained to a specified target word count
- **URL**: https://define-i05a.onrender.com/api/docs/
- **Contact**: https://github.com/hasanraiyan
- **Domain/Token**: define
- **Why Interesting**: Specialized API service with a clear, focused use case
- **Issue**: [#1906](https://github.com/pollinations/pollinations/issues/1906)

### 12. Dreamscape AI (Already in projectList.js)
- **Project Description**: A comprehensive AI assistant with voice conversation capabilities, image generation, and memory features
- **URL**: https://dreamscape.pinkpixel.dev
- **Contact**: @sizzlebop on GitHub and Discord, <EMAIL>
- **GitHub**: https://github.com/pinkpixel-dev/dreamscape-ai
- **Domain/Token**: dreamscape.pinkpixel.dev
- **Why Interesting**: Feature-rich AI assistant with voice capabilities and strong UX focus
- **Issue**: [#1870](https://github.com/pollinations/pollinations/issues/1870)

### 13. CoNavic
- **Project Description**: AI-powered browser extension that helps users manage tabs and bookmarks using LLM
- **URL**: https://github.com/mkantwala/CoNavic
- **Contact**: <EMAIL>
- **GitHub**: https://github.com/mkantwala/CoNavic
- **Domain/Token**: CoNavic Extension
- **Why Interesting**: Practical browser extension with productivity focus
- **Issue**: [#1914](https://github.com/pollinations/pollinations/issues/1914)

### 14. Mr. Kaks Discord Bot
- **Project Description**: A private, multi-purpose Discord bot with AI conversation, reasoning, and image generation capabilities
- **Contact**: Discord: fisven | GitHub: fisventurous
- **Domain/Token**: KaksBot/Discord
- **Why Interesting**: Well-integrated Discord bot with multiple AI features
- **Issue**: [#1839](https://github.com/pollinations/pollinations/issues/1839)

## Recommendations

The following projects stand out as particularly promising candidates for inclusion in the `projectList.js` file:

1. **Mirexa AI** - Comprehensive AI assistant with multiple features in a clean interface
2. **Neurix** - Multi-language platform with international reach (Russian market)
3. **Raftar.xyz** - Large user base through Discord integration (180k+ members)
4. **Free AI Chatbot & Image Generator** - Mobile app with Play Store presence
5. **KoboldAI Lite** - Established text generation platform with 100K+ monthly users
6. **Dreamscape AI** - Feature-rich AI assistant with voice capabilities
7. **tgpt** - Terminal-based AI interface for developers
8. **BullShi3ldAi** - Comprehensive frontend-only AI interface with multiple features
9. **Just Build Things** - Collection of free AI tools with multiple functionalities
10. **Generative AI Images Gallery** - Dedicated gallery for showcasing AI-generated images
11. **Aura Chat Bot** - Chat bot with text and image generation capabilities
12. **AI Image Generator [ROBLOX]** - Unique integration within the Roblox platform
13. **Modern AI Image Generator** - Feature-rich image generation with extensive customization
14. **Free AI Image Generator** - Accessible platform with no account requirements
15. **Infinite World: AI game** - Creative application for interactive storytelling
16. **Quick AI & Jolbak** - Providing AI access to users in regions with limited access
17. **Children's Picture Books Plugin** - Educational application for children's learning
18. **MyPicGen** - Specialized tool for video thumbnail generation

These projects demonstrate diverse applications of Pollinations' API across different platforms (web, mobile, Discord, terminal) and regions, showcasing the versatility of the service.

## Complete List of Special Bee Requests

Below is a comprehensive list of all special bee requests for reference and tracking purposes. The requests we've already analyzed above are marked with .

- [#1936](https://github.com/pollinations/pollinations/issues/1936) - [Special Bee Request]: (Snapgen.io)
- [#1931](https://github.com/pollinations/pollinations/issues/1931) - [Special Bee Request]: Please grant me higher access privileges to the Pollinations API (Children's Picture Books Plugin)
- [#1928](https://github.com/pollinations/pollinations/issues/1928) - [Special Bee Request]: (Quick AI & Jolbak)
- [#1914](https://github.com/pollinations/pollinations/issues/1914) - [Special Bee Request]: Request for Rate limit increase for the agentic application (CoNavic)
- [#1906](https://github.com/pollinations/pollinations/issues/1906) - [Special Bee Request]: Define
- [#1905](https://github.com/pollinations/pollinations/issues/1905) - [Special Bee Request]: tgpt
- [#1899](https://github.com/pollinations/pollinations/issues/1899) - [Special Bee Request]: KoboldAI Lite
- [#1870](https://github.com/pollinations/pollinations/issues/1870) - [Special Bee Request]: Dreamscape AI
- [#1860](https://github.com/pollinations/pollinations/issues/1860) - [Special Bee Request]: (BullShi3ldAi)
- [#1839](https://github.com/pollinations/pollinations/issues/1839) - [Special Bee Request]: Referrer Approval for Mr. Kaks Discord Bot
- [#1831](https://github.com/pollinations/pollinations/issues/1831) - [Special Bee Request]: requesting "ai.kochini" referrer (Generative AI Images Gallery)
- [#1818](https://github.com/pollinations/pollinations/issues/1818) - [Special Bee Request]: (Just Build Things)
- [#1785](https://github.com/pollinations/pollinations/issues/1785) - [Special Bee Request]: Aura Chat bot
- [#1779](https://github.com/pollinations/pollinations/issues/1779) - [Special Bee Request]: (AI Gatos)
- [#1758](https://github.com/pollinations/pollinations/issues/1758) - [Special Bee Request]: (MyPicGen)
- [#1755](https://github.com/pollinations/pollinations/issues/1755) - [Special Bee Request]: AI Image Generator [ROBLOX]
- [#1754](https://github.com/pollinations/pollinations/issues/1754) - [Special Bee Request]: I created Some of AI Image generator including your service (Modern AI Image Generator)
- [#1714](https://github.com/pollinations/pollinations/issues/1714) - [Special Bee Request]: A Story app based on AI
- [#1712](https://github.com/pollinations/pollinations/issues/1712) - [Special Bee Request]: (Free AI Image Generator)
- [#1694](https://github.com/pollinations/pollinations/issues/1694) - [Special Bee Request]: (Infinite World: AI game)
- [#1663](https://github.com/pollinations/pollinations/issues/1663) - [Special Bee Request]: (Free AI Chatbot & Image Generator)
- [#1658](https://github.com/pollinations/pollinations/issues/1658) - [Special Bee Request]: Discord Bot
- [#1655](https://github.com/pollinations/pollinations/issues/1655) - [Special Bee Request]: Bullnium
- [#1630](https://github.com/pollinations/pollinations/issues/1630) - [Special Bee Request]:
- [#1619](https://github.com/pollinations/pollinations/issues/1619) - [Special Bee Request]: (Empowerverse)
- [#1617](https://github.com/pollinations/pollinations/issues/1617) - [Special Bee Request]:
- [#1615](https://github.com/pollinations/pollinations/issues/1615) - [Special Bee Request]: Elixpo Art
- [#1607](https://github.com/pollinations/pollinations/issues/1607) - [Special Bee Request]: (Raftar.xyz)
- [#1600](https://github.com/pollinations/pollinations/issues/1600) - Define automation implementation
- [#1592](https://github.com/pollinations/pollinations/issues/1592) - [Special Bee Request]: Domain verification
- [#1590](https://github.com/pollinations/pollinations/issues/1590) - Automate Special Bee Request
- [#1589](https://github.com/pollinations/pollinations/issues/1589) - [Special Bee Request]:
- [#1581](https://github.com/pollinations/pollinations/issues/1581) - [Special Bee Request]:
- [#1574](https://github.com/pollinations/pollinations/issues/1574) - [Special Bee Request]:imag1ne
- [#1572](https://github.com/pollinations/pollinations/issues/1572) - [Special Bee Request]: Requesting for referrer approval for my AI Live project, using pollinations ai
- [#1562](https://github.com/pollinations/pollinations/issues/1562) - [Special Bee Request]: ElxrAI
- [#1553](https://github.com/pollinations/pollinations/issues/1553) - [Special Bee Request]: (Neurix)
- [#1550](https://github.com/pollinations/pollinations/issues/1550) - [Special Bee Request]: DominiSigns - Dominican Sign Language Avatar Translator
- [#1534](https://github.com/pollinations/pollinations/issues/1534) - Science Encyclopedia
- [#1489](https://github.com/pollinations/pollinations/issues/1489) - [Special Bee Request]: Mirexa AI

{"name": "@pollinations/react", "version": "2.0.8", "description": "React components and hooks for Pollinations AI", "main": "./src/index.js", "module": "./src/index.js", "exports": {".": {"import": "./src/index.js", "require": "./src/index.js", "types": "./types/index.d.ts"}}, "sideEffects": false, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"lodash.memoize": "^4.1.2", "react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-markdown": ">=7.0.0 <9.0.0"}, "devDependencies": {"@vitejs/plugin-react": "^4.3.1", "vite": "^5.4.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "types": "./types/index.d.ts"}
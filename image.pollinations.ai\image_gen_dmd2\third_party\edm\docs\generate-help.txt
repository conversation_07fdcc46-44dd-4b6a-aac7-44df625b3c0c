Usage: generate.py [OPTIONS]

  Generate random images using the techniques described in the paper
  "Elucidating the Design Space of Diffusion-Based Generative Models".

  Examples:

  # Generate 64 images and save them as out/*.png
  python generate.py --outdir=out --seeds=0-63 --batch=64 \
      --network=https://nvlabs-fi-cdn.nvidia.com/edm/pretrained/edm-cifar10-32x32-cond-vp.pkl

  # Generate 1024 images using 2 GPUs
  torchrun --standalone --nproc_per_node=2 generate.py --outdir=out --seeds=0-999 --batch=64 \
      --network=https://nvlabs-fi-cdn.nvidia.com/edm/pretrained/edm-cifar10-32x32-cond-vp.pkl

Options:
  --network PATH|URL       Network pickle filename  [required]
  --outdir DIR             Where to save the output images  [required]
  --seeds LIST             Random seeds (e.g. 1,2,5-10)  [default: 0-63]
  --subdirs                Create subdirectory for every 1000 seeds
  --class INT              Class label  [default: random]  [x>=0]
  --batch INT              Maximum batch size  [default: 64; x>=1]
  --steps INT              Number of sampling steps  [default: 18; x>=1]
  --sigma_min FLOAT        Lowest noise level  [default: varies]  [x>0]
  --sigma_max FLOAT        Highest noise level  [default: varies]  [x>0]
  --rho FLOAT              Time step exponent  [default: 7; x>0]
  --S_churn FLOAT          Stochasticity strength  [default: 0; x>=0]
  --S_min FLOAT            Stoch. min noise level  [default: 0; x>=0]
  --S_max FLOAT            Stoch. max noise level  [default: inf; x>=0]
  --S_noise FLOAT          Stoch. noise inflation  [default: 1]
  --solver euler|heun      Ablate ODE solver
  --disc vp|ve|iddpm|edm   Ablate time step discretization {t_i}
  --schedule vp|ve|linear  Ablate noise schedule sigma(t)
  --scaling vp|none        Ablate signal scaling s(t)
  --help                   Show this message and exit.

name: Deploy Cloudflare Worker

on:
  push:
    branches:
      - main
      - master
    paths:
      - 'image.pollinations.ai/cloudflare-cache/**'
  workflow_dispatch: # Allow manual triggering

jobs:
  deploy:
    runs-on: ubuntu-latest
    name: Deploy Worker
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'image.pollinations.ai/cloudflare-cache/package-lock.json'
      
      - name: Install Dependencies
        run: npm ci
        working-directory: ./image.pollinations.ai/cloudflare-cache
      
      # Configure secrets before deployment
      - name: Configure Secrets
        env:
          GA_MEASUREMENT_ID: ${{ secrets.GA_MEASUREMENT_ID }}
          GA_API_SECRET: ${{ secrets.GA_API_SECRET }}
        run: |
          echo "$GA_MEASUREMENT_ID" | wrangler secret put GA_MEASUREMENT_ID
          echo "$GA_API_SECRET" | wrangler secret put GA_API_SECRET
        working-directory: ./image.pollinations.ai/cloudflare-cache
      
      - name: Publish to <PERSON>flar<PERSON>
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          workingDirectory: 'image.pollinations.ai/cloudflare-cache'
          command: deploy

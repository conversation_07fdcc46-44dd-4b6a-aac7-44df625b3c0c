# Pollinations Projects Categorization Tracking

## Category Descriptions (from projectList.js)

- **Vibe Coding ✨** (`vibeCoding.js`): "No-code / describe-to-code playgrounds and builders"
- **Creative 🎨** (`creative.js`): "Turn prompts into images, video, music, design, slides"
- **Games 🎲** (`games.js`): "AI-powered play, interactive fiction, puzzle & agent worlds"
- **Hack-&-Build 🛠️** (`hackAndBuild.js`): "SDKs, integration libs, extensions, dashboards, MCP servers"
- **Chat 💬** (`chat.js`): "Standalone chat UIs / multi-model playgrounds"
- **Social Bots 🤖** (`socialBots.js`): "Discord / Telegram / WhatsApp / Roblox bots & NPCs"
- **Learn 📚** (`learn.js`): "Tutorials, guides, style books & educational demos"

## Important Notes
- Always check the project description and functionality against these category descriptions
- Don't rely solely on the category field in accumulated-projects.json
- Each project should only be in ONE category file
- Consider the actual purpose and functionality of the project when categorizing
- Some projects may fit better in categories different from their source JSON category
- Educational projects (like OkeyAI with African cultural awareness) belong in Learn category
- Chat interfaces and multi-model playgrounds belong in Chat category
- Social platform bots belong in Social Bots category

## Next Steps (Session Starting Point)

1. Continue processing uncategorized projects from the list below
2. All category files have been checked and no placeholder entries remain:
   - `/Users/<USER>/Documents/GitHub/pollinations/pollinations.ai/src/config/projects/socialBots.js`
   - `/Users/<USER>/Documents/GitHub/pollinations/pollinations.ai/src/config/projects/games.js`
   - `/Users/<USER>/Documents/GitHub/pollinations/pollinations.ai/src/config/projects/vibeCoding.js`
   - `/Users/<USER>/Documents/GitHub/pollinations/pollinations.ai/src/config/projects/learn.js`
   - `/Users/<USER>/Documents/GitHub/pollinations/pollinations.ai/src/config/projects/chat.js`
   - `/Users/<USER>/Documents/GitHub/pollinations/pollinations.ai/src/config/projects/hackAndBuild.js`
   - `/Users/<USER>/Documents/GitHub/pollinations/pollinations.ai/src/config/projects/creative.js`
3. Focus on projects that need categorization (marked with "needs categorization")
4. Update this tracking file after each batch of projects is processed
5. Source of truth for project data: `/Users/<USER>/Documents/GitHub/pollinations/project-recovery/accumulated-projects.json`

## Progress Summary
- Completed updating all placeholder entries in all category files
- Added "Gacha" to `games.js`
- Added "GPT_Project", "AdvanceChatGptBot", "PolliBot", and "Jackey" to their respective category files
- Updated placeholder entries for "urSapere AI", "Raftar.xyz", and "Anime Character Generator"
- Processed "Aura Chat Bot", "Quick AI & Jolbak", "AI Image Generator [ROBLOX]", "SingodiyaTech bot", and "Raftar.xyz"
- Added "ai/teens worldwide" to `learn.js`
- Added "Echo AI" and "Neurix 🇷🇺" to `chat.js`
- Added "Snapgen.io" to `creative.js`
- Added "Promptgenerator.art" to `creative.js`

## Categorization Instructions

When assigning projects to categories, always:
- Use the most accurate fit based on project functionality and metadata.
- If a project fits multiple categories, prefer assigning it to less-populated categories (games, hackAndBuild, learn, socialBots, vibeCoding) to maintain balance across categories.
- Avoid duplicates and keep each project in only one category.

## Project List

Okay, here are all the unique project names from the provided JSON data:

1.  VibeCoder - already in vibeCoding.js
2.  Pollinations MCP Server - added to vibeCoding.js
3.  Pollinations Task Master - added to vibeCoding.js
4.  Qwen-Agent - added to vibeCoding.js
5.  JCode Website Builder - added to vibeCoding.js
6.  Define - added to vibeCoding.js
7.  WebGeniusAI - added to vibeCoding.js
8.  Pollinations.DIY - added to vibeCoding.js
9.  Websim - added to vibeCoding.js
10. NetSim - added to vibeCoding.js
11. 🇪🇸 Yo el director - added to creative.js
12. 🛠️ AI Content Describer - added to hackAndBuild.js
13. Pollin-Coder - added to vibeCoding.js
14. Imagemate AI - added to creative.js
15. tgpt - added to hackAndBuild.js
16. B&W SVG Generator - added to creative.js
17. Imagen - added to creative.js
18. DominiSigns - added to hackAndBuild.js
19. toai.chat - added to chat.js
20. Pollinations Feed - added to creative.js
21. Anime AI Generation - added to creative.js
22. Memed - added to creative.js
23. Open Prompt - added to vibeCoding.js
24. JustBuildThings - added to vibeCoding.js
25. Elixpo-Art - added to creative.js
26. Free AI Chatbot & Image Generator - added to chat.js
27. Server Status Dashboards - added to hackAndBuild.js
28. MVKProject Nexus API - added to hackAndBuild.js
29. Irina - added to chat.js
30. Minecraft AI (Node.js) - added to games.js
31. Minecraft AI (Python) - added to games.js
32. Juego de Memorizar con Pollinations - added to games.js
33. Generador de presentaciones con imágenes y texto V2 - added to creative.js
34. ImageEditer - added to socialBots.js
35. PixPal - added to creative.js
36. DreamHer - added to creative.js
37. MASala - added to creative.js
38. Emojiall AI Drawing Platform - added to creative.js
39. FoldaScan - added to creative.js
40. Favorite Puzzles - added to games.js
41. AI YouTube Shorts Generator - added to creative.js
42. AI Chat - added to chat.js
42. Aiphoto智能绘画 - added to creative.js
43. KoboldAI Lite - added to chat.js
44. Polynate - added to creative.js
45. Mindcraft - added to games.js
46. Whizzy AI - added to chat.js
47. AI Code Generator - added to vibeCoding.js
48. Windows Walker - added to hackAndBuild.js
49. Comeback AI - added to chat.js
50. The Promised Pen - added to creative.js
51. Match-cut video ai - added to creative.js
52. Anisurge - added to chat.js
53. MoneyPrinterTurbo - added to creative.js
54. SillyTavern - added to chat.js
55. LLM7.io - added to chat.js
56. Pollinations.AI Enhancer - added to hackAndBuild.js
57. AIMinistries - added to creative.js
58. Rizqi O Chatbot - added to chat.js 
59. Foodie AI - added to creative.js
60. LobeChat - added to chat.js
61. Pollinations.AI 中文 - added to chat.js
62. Quicker Pollinations AI - added to hackAndBuild.js
63. Zelos AI image generator - added to creative.js
64. Mirexa AI Chat - added to chat.js
65. Pollinations Chat - added to chat.js
66. Pollinations Chatbot - added to chat.js
67. OkeyMeta - added to chat.js
68. Snarky Bot - added to chat.js
69. Pollinations AI Playground - added to chat.js
70. Pollinations AI Free API - added to hackAndBuild.js
71. Pollinations AI Chatbot - added to chat.js
72. Pollinations AI Image Generator - added to creative.js
73. Herramientas IA - added to hackAndBuild.js
74. Pollinations AI Video Generator - added to creative.js
75. Pollinations AI Game - added to games.js
76. POLLIPAPER - added to learn.js
77. AI PPT Maker - added to creative.js
78. UR Imagine & Chat AI - added to chat.js
79. Pollinations Gallery - added to creative.js
80. AI-Bloom - added to creative.js
81. GPT_Project - added to hackAndBuild.js
82. AdvanceChatGptBot - added to chat.js
83. PolliBot - added to chat.js
84. Aura Chat Bot - added to socialBots.js
85. Quick AI & Jolbak - already in socialBots.js
86. AI Image Generator [ROBLOX] - already in socialBots.js
87. SingodiyaTech bot - already in socialBots.js
88. Raftar.xyz - already in socialBots.js
89. AlphaLLM - AI Discord Bot - already in socialBots.js
90. pollinations-tg-bot - already in socialBots.js
91. Jackey - added to creative.js
92. Gacha - added to games.js
93. One Word - added to socialBots.js
94. Titan-GPT - added to socialBots.js
95. Discord Bot - added to socialBots.js as "Pollinations Discord Bot"
96. Telegram Bot - added to socialBots.js as "Pollinations Telegram Assistant"
97. WhatsApp Group - added to socialBots.js as "Pollinations WhatsApp Group"
98. OpenHive - added to socialBots.js
99. Anyai - added to socialBots.js
100. Mimir AIP - added to hackAndBuild.js
101. ai/teens worldwide - already in learn.js
103. Mirexa AI - already in creative.js
104. MiReXa AI - already in creative.js
105. Gacha - already in games.js
106. NetSim - already in games.js
107. B&W SVG Generator - already in creative.js
108. Imagen - added to creative.js
109. DominiSigns - added to hackAndBuild.js
110. WordPress AI Vision Block - added to hackAndBuild.js
111. toai.chat - already in chat.js
112. Elixpo Art Chrome Extension - added to creative.js
113. Pollinations Feed - already in creative.js
114. Pollinations.ai Model Comparison - added to creative.js
115. Anime AI Generation - already in creative.js
116. Pollinations.DIY - already in vibeCoding.js
117. Pal Chat - added to chat.js
118. Pollinator Android App - added to creative.js
119. Own-AI - added to creative.js
120. Image Gen - Uncensored Edition - added to creative.js
121. CoNavic - added to hackAndBuild.js
122. imggen.top - added to hackAndBuild.js
123. Aura Chat bot - already in socialBots.js (removed duplicate from chat.js)
124. FoodAnaly - added to hackAndBuild.js
125. OkeyAI - added to learn.js (educational LLM with African cultural awareness)
126. DesmondBot - added to chat.js (standalone chat UI)
127. DreamBig - Generative AI Playground - added to chat.js (multi-model playground)
128. Goalani - added to chat.js
129. IMyself AI - added to creative.js
130. FreeAI - added to chat.js
131. AI Unlimited Customizable Feature Module - added to hackAndBuild.js (as Quicker Pollinations AI)
132. PrivatePollenAI - added to chat.js
133. Zelos AI image generator - added to creative.js
134. MiReXa AI - added to chat.js
135. FlowGPT - added to creative.js
136. gpt4free - added to chat.js
137. Unity AI Lab - added to chat.js
138. DynaSpark AI - added to chat.js
139. Dreamscape AI - added to creative.js
140. PollinateAI - added to creative.js
141. WebGeniusAI - added to creative.js
142. NailsGen - added to creative.js
143. ImageGen AI Image - added to creative.js
144. RuangRiung AI Image - added to creative.js 
145. BlackWave - added to creative.js
146. Generator Text AI - added to creative.js 
147. Strain Navigator - added to hackAndBuild.js
148. MalaysiaPrompt - added to learn.js
149. Generator AI Image - added to creative.js
150. Pollinations.ai Image Generation (for Frame) - added to creative.js
151. Podcast #1500 - updated in learn.js with accurate metadata

153. Elixpo Art - added to creative.js
154. Riffle - added to learn.js
155. AI 文本转音频 - added to creative.js
156. Case Me - added to creative.js
157. PixPax - added to chat.js
158. Watch TV with neko (Roblox) - added to games.js
159. Jenny AI - added to chat.js
160. CalcuBite AI - added to creative.js
161. RoastMaster AI - added to games.js
162. roastmyselfie.app - added to games.js
163. StoryMagic: Interactive Kids Stories - added to learn.js
164. Open Prompt - added to vibeCoding.js
165. AI儿童故事 🇨🇳 - added to learn.js
166. Herramientas IA - added to hackAndBuild.js
167. Avatar GenStudio - added to creative.js
168. Musify - AI Enhanced Music Streaming - added to creative.js
169. image1gen
170. AI Image Generator
171. PolliSonic Generator
173. Abyss Ascending - added to games.js
174. Deep Saga - added to games.js
175. [AI] Character RP (Roblox) - added to games.js
176. MIDIjourney - added to creative.js
177. TurboReel - added to creative.js
178. Rangrez AI - added to creative.js
179. Infinite Tales - already in games.js
180. StorySight - added to creative.js
181. StoryWeaver - added to creative.js
182. Sirius Cybernetics Elevator Challenge - added to games.js
183. Raftar.xyz - added to socialBots.js
184. AlphaLLM - AI Discord Bot - added to socialBots.js
185. pollinations-tg-bot - added to socialBots.js
186. ComfyUI-Pollinations
187. Node.js Client Library
188. MCPollinations - added to hackAndBuild.js
189. pollinations_ai - added to hackAndBuild.js
190. Smpldev - added to hackAndBuild.js
191. pollinations NPM Module - added to hackAndBuild.js
192. pypollinations - added to hackAndBuild.js
193. @pollinations/react - added to hackAndBuild.js
194. Polli API Dashboard - added to hackAndBuild.js
195. pollinations.ai Python SDK - added to hackAndBuild.js
196. Pollinations.AI AI/Teens talk - updated in learn.js with accurate metadata
197. Connect Pollinations with Open Web UI tutorial - already in learn.js
198. Chinese DeepSeek Tutorial - updated in learn.js with accurate metadata
199. Artistic Styles Book - updated in learn.js with accurate metadata
200. Proyecto Descartes - updated in learn.js with accurate metadata
201. Tutorial - updated in learn.js with accurate metadata
202. TeekGenAI - added to creative.js
203. Anime Character Generator - added to creative.js
204. LiteAI - added to chat.js
205. ai/teens worldwide - added to learn.js
206. Echo AI - added to chat.js
207. urSapere AI - added to hackAndBuild.js
208. Generative AI Images Gallery - added to creative.js
209. Infinite World: AI game - added to games.js as "Infinite World – AI Game"
210. Aura Chat Bot - added to chat.js
211. Quick AI & Jolbak - added to socialBots.js
212. AI Image Generator [ROBLOX] - added to socialBots.js
213. SingodiyaTech bot - added to socialBots.js
214. IRINA by visuallink - added to creative.js
283. 🆕 Children's Picture Books Plugin - duplicate, remove
284. 🆕 Free AI Chatbot & Image Generator - duplicate, remove
285. 🆕 Neurix 🇷🇺 - added to chat.js
286. 🆕 Snapgen.io - added to creative.js
287. 🆕 urSapere AI - added to hackAndBuild.js

name: Special Bee Request
description: Request tier upgrade.
title: "[Special Bee Request]: "
labels: ["special-bee-request"]
body:
  - type: markdown
    attributes:
      value: |
        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 24px;">
          <img src="https://raw.githubusercontent.com/pollinations/pollinations/master/operations/assets/pollinations_ai_logo_text_white.png" alt="Pollinations Logo" width="100%" style="display: inline-block;">
          <h1 style="margin: 0; font-size: 24px; display: inline-block;">Special Bee Request</h1>
        </div>
        <div style="font-size: 1.25em; font-style: italic; margin-bottom: 12px;">
          Thanks for your interest in Pollinations API! This form is for requesting a tier upgrade.<br>
          For more information about tiers and upgrades, see the 
          <a href="../../APIDOCS.md#special-bee-" style="font-style: italic;">API Docs Tier Section</a>.
        </div>

  - type: input
    id: project-name
    attributes:
      label: Project Name
      description: What is the name of your project using Pollinations API?
      placeholder: "e.g., AI Art Gallery"
    validations:
      required: true

  - type: textarea
    id: project-description
    attributes:
      label: Project Description
      description: Please provide a brief description of your project and how it uses Pollinations.AI API
      placeholder: "Describe what your project does and how it integrates with Pollinations..."
    validations:
      required: true

  - type: input
    id: github-repo
    attributes:
      label: GitHub Repository URL
      description: Provide a link to your project's GitHub repository (if available)
      placeholder: "https://github.com/your-repo"

  - type: input
    id: project-url
    attributes:
      label: Project URL
      description: Link to your project (if public)
      placeholder: "https://example.com"

  - type: markdown
    attributes:
      value: |
        ### Important Notes
        
        - Approval is evaluated on a case-by-case basis
        - We may reach out for additional information
        - Please ensure your project complies with our terms of service and ethical guidelines
        - Submitting this form does not guarantee approval 
/**
 * Learn Projects 📚
 * Tutorials, guides, style books & educational demos
 */

export const learnProjects = [
    {
        name: "Prompt Explorer",
        url: "https://play.google.com/store/apps/details?id=com.ismafly.promptexploratorapp",
        description:
            "A mobile app that combines AI prompt discovery with image generation using Pollinations API. Users can explore existing AI art from Lexica/Civitai, learn from prompts and metadata, then generate their own images directly in the app. Features include generation history, favorites, and a complete learning workflow from discovery to creation.",
        author: "<EMAIL>",
        submissionDate: "2025-07-12",
        order: 1,
    },
    {
        name: "AI儿童故事 🇨🇳",
        url: "https://kidss.netlify.app/",
        description:
            "基于此项目 构建有趣的孩子故事书应用演示 (Based on this project, build an interesting children's storybook application demo)",
        author: "MZ",
        submissionDate: "2025-03-10",
        order: 2,
    },
    {
        name: "StoryMagic: Interactive Kids Stories",
        description:
            "Interactive and educational tool for generating kids' stories.",
        submissionDate: "2025-03-14",
        order: 1,
    },
    {
        name: "<PERSON>iff<PERSON>",
        url: "https://riffle.ink",
        description:
            "A powerful tool designed to make reading English books more enjoyable and effective while helping you build your vocabulary naturally. Using Pollinations AI to create content that incorporates your own vocabulary words allows you to learn them in a vivid, engaging context.",
        author: "<EMAIL>",
        submissionDate: "2025-03-28",
        order: 1,
    },
    {
        name: "MalaysiaPrompt 🇲🇾",
        url: "https://malaysiaprompt.rf.gd/",
        description:
            "A resource for discovering and sharing creative prompts, supporting the Malaysian creative and educational AI community.",
        language: "ms-MY",
        order: 3,
    },
    {
        name: "OkeyAI",
        url: "https://chat.okeymeta.com.ng",
        description:
            "An LLM created by Africans to understand and have cultural awareness of African contexts and languages, OkeyAI outperforms many LLM models based on size and intelligence, OkeyMeta uses pollination image generating API to train it's LLM (OkeyAI) on images in real time.",
        author: "@okeymeta",
        repo: "https://github.com/okeymeta",
        submissionDate: "2025-04-19",
        order: 1,
    },

    {
        name: "Connect Pollinations with Open Web UI tutorial",
        url: "https://github.com/cloph-dsp/Pollinations-AI-in-OpenWebUI",
        description:
            "Step-by-step guide on integrating Pollinations APIs with Open Web UI for enhanced image generation.",
        author: "@cloph-dsp",
        repo: "https://github.com/cloph-dsp/Pollinations-AI-in-OpenWebUI",
        stars: 9,
        submissionDate: "2025-03-15",
        order: 1, // Existing project order
    },
    {
        name: "Pollinations.AI AI/Teens talk",
        url: "https://www.youtube.com/live/5Rvdfr2qYGA?si=i5NLOKI49fGxNAEK&t=1034",
        description:
            "Session 2: ai/teens worldwide conference exploring the forces shaping AI today, diving into governance, virtual connections, and decision-making with voices from multiple European cities.",
        author: "@thomash_pollinations",
        submissionDate: "2025-04-15",
        order: 2,
    },

    {
        name: "Artistic Styles Book",
        url: "https://proyectodescartes.org/iCartesiLibri/materiales_didacticos/Libro_Estilos/index.html",
        description: "An interactive book showcasing 90+ artistic styles.",
        author: "Juan Gmo. Rivera",
        submissionDate: "2025-04-15",
        order: 4,
    },
    {
        name: "Tutorial",
        url: "https://guiadehospedagem.com.br/pollinations-ai/",
        description:
            "An in-depth Portuguese tutorial on using Pollinations AI.",
        author: "Janderson de Sales",
        submissionDate: "2025-04-15",
        language: "pt-BR",
        order: 5,
    },
    {
        name: "Podcast #1500",
        url: "https://open.spotify.com/show/1wu4ngb1dclyTwoNN4cZzK",
        description:
            "Podcast project powered by pollinations, featuring dialogues among LLMs. First episode features 3o-mini and DeepSeek R1 70B talking about Vibe Coding.",
        author: "@brain.diver",
        submissionDate: "2025-03-31",
        order: 6,
    },
    {
        name: "Proyecto Descartes",
        url: "https://proyectodescartes.org/revista/Numeros/Revista_8_2024/index.html",
        description:
            "Educational initiative integrating Pollinations AI into STEM.",
        author: "Juan Gmo. Rivera",
        submissionDate: "2025-04-15",
        order: 7,
    },
    // Additional projects will be migrated from recovered data

    {
        name: "Whizzy AI",
        url: "https://whizzyai.vercel.app",
        description:
            "An educational AI platform for students featuring AI-powered study assistance, chat functionality, and image generation capabilities using Pollinations AI. Designed to help students with studies they find challenging.",
        author: "@vaibhavcoding69",
        submissionDate: "2025-06-03",
        order: 1,
    },
    {
        name: "TeekGenAI",
        url: "https://www.youtube.com/@teekgenai",
        description:
            "A platform providing free access to AI tools like image generation, text-to-speech, and chat, with tutorials. Content often available in Sinhala.",
        author: "@teekgenai", // From TikTok/YouTube
        submissionDate: "2025-06-04", // Placeholder date
        language: "si", // Sinhala
        order: 2,
    },
];

/**
 * Games Projects 🎲
 * AI-powered play, interactive fiction, puzzle & agent worlds
 */

export const gamesProjects = [
  {
    name: "RoastMaster AI",
    description: "No detailed description available, but likely a creative/entertainment tool (AI roast generator).",
    submissionDate: "2025-03-14",
    order: 1
  },
  {
    name: "roastmyselfie.app",
    url: "https://roastmyselfie.app",
    description: "AI Personality Analyzer - Get roasted and psychoanalyzed.. just from one selfie! Dare to try?",
    author: "@andres_11",
    submissionDate: "2025-03-14",
    order: 2
  },
  {
    name: "Watch TV with neko (Roblox)",
    url: "https://www.roblox.com/games/15087497266/UPD-Watch-TV-with-neko-AI",
    description: "Roblox game where you can talk with AI catgirls 🐾 or just have fun, talking with other players in cozy rooms ⭐️",
    author: "https://www.roblox.com/users/3857849039/profile/",
    submissionDate: "2025-03-17",
    order: 2
  },
  {
    name: "Pollinations AI Game",
    url: "https://github.com/ednsinf/pollinations-ai",
    description: "A Hitchhiker's Guide to the Galaxy themed LLM-based elevator game.",
    author: "@game",
    repo: "https://github.com/ednsinf/pollinations-ai",
    submissionDate: "2025-05-05",
    order: 1,
    stars: 0
  },
  {
    name: "Mindcraft",
    url: "https://mindcraft.riqvip.dev/",
    description: "A web-based Minecraft-inspired game where players can use natural language to build and interact with a voxel world using Pollinations AI.",
    author: "@mindcraft_team",
    repo: "https://github.com/mindcraft-ce/mindcraft-ce",
    stars: 3500,
    submissionDate: "2025-06-03",
    order: 1
  },
  {
    name: "Favorite Puzzles",
    url: "https://radbrothers.com/games/favorite-puzzles/",
    description: "A jigsaw puzzles game for Android, iOS, and web that uses Pollinations feed as one of the sources of images for puzzles. Features puzzle generation using neural networks, customizable difficulty levels from 6 to 1200 pieces, multiple game modes, and the ability to create puzzles from your own images.",
    author: "<EMAIL>",
    submissionDate: "2025-05-19",
    order: 1
  },
  {
    name: "Infinite Tales",
    description: "Interactive storytelling platform powered by AI that creates endless narrative adventures.",
    url: "https://github.com/JayJayBinks/infinite-tales-rpg",
    repo: "https://github.com/JayJayBinks/infinite-tales-rpg",
    author: "@infinite_tales",
    submissionDate: "2025-01-20",
    stars: 28
  },
  {
    name: "Minecraft AI (Node.js)",
    url: "https://github.com/pollinations/minecraft-ai-node",
    description: "A Node.js implementation that uses Pollinations AI to control a Minecraft character through natural language commands.",
    author: "@minecraft_ai_dev",
    repo: "https://github.com/pollinations/minecraft-ai-node",
    stars: 124,
    submissionDate: "2025-04-05",
    order: 1
  },
  {
    name: "Minecraft AI (Python)",
    url: "https://github.com/Amagash/minecraft-ai-python",
    description: "A Python implementation that uses Pollinations AI to control a Minecraft character through natural language commands and automated gameplay.",
    author: "@Amagash",
    repo: "https://github.com/Amagash/minecraft-ai-python",
    stars: 7,
    submissionDate: "2025-03-22",
    order: 1
  },
  {
    name: "Juego de Memorizar con Pollinations",
    url: "https://memorizar-pollinations.vercel.app/",
    description: "A memory game that uses Pollinations AI to generate unique image pairs for matching, with difficulty levels and educational themes.",
    author: "@edudev_es",
    language: "es",
    submissionDate: "2025-04-18",
    order: 1
  },
  {
    name: "Sirius Cybernetics Elevator Challenge",
    url: "https://github.com/pollinations/sirius-cybernetics-elevator-challenge",
    description: "A programming challenge that uses Pollinations AI to simulate personality-driven elevator systems in a virtual building environment.",
    author: "@sirius_dev",
    repo: "https://github.com/pollinations/sirius-cybernetics-elevator-challenge",
    stars: 1,
    submissionDate: "2025-04-01",
    order: 1
  },
  {
    name: "Abyss Ascending",
    url: "https://interzone.art.br/abyss_ascending/",
    description: "A generative cosmic ocean adventure - text-based RPG with AI-driven storytelling, dynamic backgrounds, and procedural audio powered by Pollinations AI.",
    author: "@interzone",
    submissionDate: "2025-05-05",
    order: 1
  },
  {
    name: "AI Character RP (Roblox)",
    url: "https://github.com/snipcola/Roblox-AI",
    description: "A Roblox game that lets players interact with AI characters powered by Pollinations, featuring dynamic conversations and quests.",
    author: "@roblox_ai_dev",
    repo: "https://github.com/snipcola/Roblox-AI",
    submissionDate: "2025-04-25",
    order: 1,
    stars: 10
  },
  {
    name: "Deep Saga",
    url: "https://deepsaga.io",
    description: "An immersive role-playing game with AI-generated worlds, characters, and quests that adapt to player choices using Pollinations AI.",
    author: "@saga_studios",
    submissionDate: "2025-04-10",
    order: 1
  },
  {
    name: "Infinite World – AI Game",
    url: "https://infinite-world-game.vercel.app/",
    description: "An exploration game with procedurally generated environments and creatures created by Pollinations AI based on player input.",
    author: "@infinite_world_dev",
    submissionDate: "2025-05-20",
    order: 1
  },
  {
    name: "DreamHer",
    url: "https://dreamher.vercel.app/",
    description: "Interactive web app that transforms your imagination of a 'dream girl' into a visual representation through just 10 simple questions using Pollinations AI. Features AI-powered visualization, dynamic processing, and an engaging, magical user experience.",
    author: "@_Creation22",
    authorUrl: "https://x.com/_Creation22",
    repo: "https://github.com/creation22/DreamGirl",
    stars: 2,
    submissionDate: "2025-05-27",
    order: 1
  },
  {
    name: "A Mita (Roblox)",
    url: "https://www.roblox.com/games/118762581800441/A-Mita",
    description: "A Roblox game about interacting with AI with different personalities. Features dynamic AI conversations and reached up to 1k active players at its peak.",
    author: "@thespecificdev",
    submissionDate: "2025-06-07",
    order: 1
  },
  {
    name: "🕰️ Time Travel Selfie Portal",
    url: "https://selfie-time-traveler-portal.vercel.app",
    description: "Upload your selfie, background is automatically removed. Choose a historical era and view your viral time-travel portrait, move your image for the best fit, and get your unique humorous biography. Uses text.pollinations.ai for image analysis and biography creation, and image.pollinations.ai for background generation.",
    author: "@Argyrisk21",
    submissionDate: "2025-06-13",
    order: 1
  },
  {
    name: "Convince the Weird Maid to Let You Leave the House (Roblox)",
    url: "https://www.roblox.com/games/120881450499910/Convince-the-Weird-Maid-to-Let-You-Leave-the-House",
    description: "A Roblox game where a weird maid traps you inside her house. Your goal is to convince her to let you leave before her 'freakiness' meter hits 100%. Powered by AI for dynamic conversations and interactive gameplay.",
    author: "@wBrowsqq",
    submissionDate: "2025-06-13",
    order: 1
  },
  {
    name: "Aiko AI: With You Til The End (Roblox)",
    url: "https://www.roblox.com/games/91780007937760/Aiko-AI-With-You-Till-The-End-BETA",
    description: "A psychological escape room game where you're trapped by Aiko, an AI with a dangerous obsession. Features autonomous AI character with dynamic emotional states, interactive gameplay where your words directly affect Aiko's mood, and psychological manipulation mechanics to trick her into letting you escape. Powered by Pollinations AI.",
    author: "@youssefelsafi",
    submissionDate: "2025-06-18",
    order: 1
  },
  {
    name: "RETIME - FABRIC OF REALITY",
    url: "https://rivatech-games.itch.io/retime",
    description: "A text-based, blind friendly fantasy adventure game powered by Pollinations AI. Players navigate through broken pieces of reality and time, making choices that shape a unique story. Features screen-reader accessibility and temporal mechanics with 'Temporal Dinars' currency.",
    author: "@TheSingleAlgerianPotato",
    submissionDate: "2025-06-30",
    order: 1
  },
  {
    name: "AIStorium",
    url: "https://aistorium.vercel.app/",
    description: "Service for generating dynamic stories. Generates stories, images and audio by using pollinations api.",
    author: "@artegoser",
    repo: "https://github.com/artegoser/ai-storium",
    stars: 1,
    submissionDate: "2025-07-12",
    order: 1
  },
  {
    name: "🧩 AI Word Search",
    url: "https://play.google.com/store/apps/details?id=com.aiwords.app",
    description: "AI-powered Android app that generates unlimited, personalized word search puzzles on any topic. Features custom AI-generated puzzles, print & share functionality, badges & achievements, clean interface, relaxing focus music, and upcoming multiplayer mode. Perfect for students, teachers, families, and puzzle enthusiasts.",
    author: "<EMAIL>",
    submissionDate: "2025-07-12",
    order: 1
  },
  {
    name: "🇨🇳 云渺仙途：凡尘篇",
    url: "https://ai-game.jkai.de/",
    description: "《云渺仙途：凡尘篇》是一个由AI驱动的文字修仙沙盒游戏 (An AI-driven text cultivation sandbox game featuring immortal cultivation adventures)",
    author: "@ZhjGo",
    repo: "https://github.com/ZhjGo/ai-game",
    stars: 7,
    language: "zh-CN",
    submissionDate: "2025-07-12",
    order: 1
  }
];

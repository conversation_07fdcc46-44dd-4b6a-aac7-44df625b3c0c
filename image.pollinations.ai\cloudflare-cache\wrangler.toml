name = "pollinations-image-cache"
main = "src/index.js"
compatibility_date = "2023-05-18"
# The account_id will be automatically read from the CLOUDFLARE_ACCOUNT_ID environment variable

# R2 bucket for image storage
[[r2_buckets]]
binding = "IMAGE_BUCKET"
bucket_name = "pollinations-images"

# Vectorize index for semantic caching
[[vectorize]]
binding = "VECTORIZE_INDEX"
index_name = "pollinations-image-cache-v2"

# Workers AI for embeddings
[ai]
binding = "AI"

# Environment variables
[vars]
ORIGIN_HOST = "image-origin.pollinations.ai"
DEBUG_ANALYTICS = "false"  # Set to "true" to enable detailed analytics debugging

# Increase limits for long-running operations
# This requires a paid Workers Unbound plan
[limits]
cpu_ms = 30000  # 30 seconds CPU time (max for Workers Unbound)

# Configure logging
[observability]
enabled = false      # turns Workers Logs off completely

[observability.logs]
invocation_logs = false

# Test environment configuration
[env.test]
[[env.test.r2_buckets]]
binding = "IMAGE_BUCKET"
bucket_name = "pollinations-images"

[[env.test.vectorize]]
binding = "VECTORIZE_INDEX"
index_name = "pollinations-image-cache-test"

[env.test.ai]
binding = "AI"

# Note: The usage_model setting is deprecated and has been removed
# Workers Unbound is now configured via account settings in the Cloudflare dashboard

# ===========================================
# ENVIRONMENT SETUP INSTRUCTIONS
# ===========================================
# This configuration uses environment variables from the .env file.
# Make sure your .env file in the root directory includes:
#
# GA_MEASUREMENT_ID=your-ga-measurement-id
# GA_API_SECRET=your-ga-api-secret
# CLOUDFLARE_ACCOUNT_ID=your-cloudflare-account-id
#
# The deploy.sh script will automatically create a .dev.vars file
# for local development using these values.
# ===========================================

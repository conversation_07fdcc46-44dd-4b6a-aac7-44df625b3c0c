{"name": "github-auth-simple", "version": "1.0.0", "description": "Simple GitHub OAuth proxy for Pollinations", "main": "src/index.ts", "scripts": {"dev": "wrangler dev --port 3333", "deploy": "node deploy.js", "deploy:prod": "ENV_FILE=.dev.vars.prod node deploy.js", "deploy:local": "ENV_FILE=.dev.vars node deploy.js", "deploy:with-migrations": "ENV_FILE=.dev.vars.prod node deploy-with-migrations.js", "deploy:with-migrations:local": "ENV_FILE=.dev.vars node deploy-with-migrations.js", "apply-user-tiers": "npx wrangler d1 execute github_auth --env production --file ./migrations/user_tiers.sql", "check-db-tables": "npx wrangler d1 execute github_auth --env production --command \"SELECT name FROM sqlite_master WHERE type='table';\"", "test": "node test.js"}, "dependencies": {"@cloudflare/workers-types": "^4.20241218.0", "jose": "^5.2.0"}, "devDependencies": {"typescript": "^5.3.3", "wrangler": "^4.16.1"}}
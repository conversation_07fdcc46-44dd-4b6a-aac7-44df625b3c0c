[Unit]
Description=Pollinations LibreTranslate Docker Service
After=docker.service
Requires=docker.service

[Service]
User=ubuntu
ExecStartPre=-/usr/bin/docker stop libretranslate
ExecStartPre=-/usr/bin/docker rm libretranslate
ExecStart=/home/<USER>/pollinations/image.pollinations.ai/serverConfigAndScripts/run_with_heartbeat.sh translate 5000 /usr/bin/docker run --name libretranslate -p 5000:5000 libretranslate/libretranslate
ExecStop=/usr/bin/docker stop libretranslate
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target

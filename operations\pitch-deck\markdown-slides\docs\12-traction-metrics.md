---
class: scroll
---

<div style="text-align: right; position: absolute; top: 0; right: 0;">
<a href="/1">⬅️ Back to Index</a>
</div>

# 🚀 **Key Traction Metrics & KPIs**

<div class="bg-cyan-100 p-1 pl-6 pr-6 rounded-lg border-l-4 border-cyan-500 mb-6">
  <p class="text-cyan-800">Traction &amp; KPIs: Current <strong><em>~3M Ecosystem MAU</em></strong> and <strong><em>100M+ monthly media generations</em></strong> provide a <em>strong foundation</em>. <em>Phased KPIs</em> will track <strong><em>creator adoption, Ad SDK integration, growth of 'profitable apps' (Ad€ &gt; Cloud€), and Nectar tier success.</em></strong></p>
</div>

This document outlines the key traction metrics and Key Performance Indicators (KPIs) pollinations.ai will track. Our strategy is built upon a strong foundation of pre-monetization ecosystem engagement..

**Current Ecosystem Snapshot (Summary):**
*   **Ecosystem Reach:** Total estimated ~3M Monthly Active Users (MAU) across all 300+ applications built on Pollinations.
*   **Platform Activity:** 100M+ media assets generated monthly.
*   **Creator Base:** 300+ live applications, 13,000+ Discord community members.
*   **Key Integrations:** High-profile examples include projects on Roblox (1.8M MAU) and LobeChat (60k+ GitHub stars).
A detailed analysis of a subset of 156 projects from this ecosystem provides further specific insights (see Ecosystem Analysis document).

## **Phase 1: Foundation & Initial Monetization (H2 2025)**

*   **Focus:** Drive initial developer adoption, encourage experimentation, empower creators with self-sufficient app monetization (Flower tier), and establish initial platform ad revenue.
*   **Key Milestones:** Flower tier (GA), Marketplace v1 for app discovery launched, Ad SDK v1, SOTA Models & Rich Ads for Flowers. Seed & Flower tiers (Launched Q2 2025).

| Category                            | Metric/KPI                                                                    | Description/Purpose                                                                                  |
| :---------------------------------- | :---------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------- |
| **Creator Adoption & Engagement**   | 1. Total Registered Creators/Apps                                               | Overall interest and initial onboarding onto the platform.                                       |
|                                     | 2. New Registered Apps (Monthly)                                              | Growth rate of the creator base; new developer influx.                                             |
|                                     | 3. Active Creators/Apps (e.g., generated media in last 30 days)               | Actual usage of the platform's core generative functionality by creators.                            |
| **Platform Usage**                  | 4. AI Media Assets Generated (Monthly)                                        | Volume of core platform activity; measures overall content creation.                               |
|                                     | 5. API Calls (Monthly)                                                        | Overall technical usage of the platform; indicates integration depth.                              |
| **Monetization (Platform-Retained)**| 6. Number of Apps with Ad SDK v1 Integrated                                   | Adoption rate of the primary monetization pathway by creators.                                     |
|                                     | 7. Total Ad Impressions Served (Platform-wide via Ad SDK v1)                  | Scale of ad inventory generated; potential for ad revenue.                                         |
|                                     | 8. Platform Gross Ad Revenue (€) (Platform-wide)                              | Initial total ad revenue generated by the platform before any costs.                                 |
|                                     | 9. Average Platform eCPM (€)                                                  | Efficiency of ad monetization; revenue per 1000 impressions.                                       |
|                                     | 10. Platform Ad Revenue per 1k Media Generations (€)                          | Core unit economic efficiency linking ad revenue directly to platform usage.                       |
| **Creator Progression & Health**    | 11. Number of Apps in Each tier (Seed, Flower, Nectar)                | Distribution of creators across tiers; uptake of the new Nectar tier.                           |
|                                     | 12. Seed → Flower Upgrade Rate (Monthly)                                | Rate at which creators move to the first self-sustaining monetized tier.                           |
|                                     | 13. Number of "Profitable Apps" (Flower apps where Ad € > Cloud €)           | Validation of the core value proposition: apps funding their own compute via ads.                    |
|                                     | 14. Average Ad € / Cloud € Ratio (for Flower apps)                         | Margin and financial self-sustainability of apps in the Flower tier.                              |
|                                     | 15. Platform Gross Margin (%) *(Early indicator)*                             | Initial measure of overall platform financial health.                                              |

## **Phase 2: Building for Partnership & Advanced Ads (H1 2026)**

*   **Focus:** Develop advanced ad capabilities and the core infrastructure for revenue sharing, preparing for the Nectar tier.
*   **Key Milestones:** Ad SDK v2 (multi-format), Revenue Share Ledger & Payout System Dev, Nectar tier (Closed Beta), Advanced Ad Mediation Dev initiated.
*   *All relevant metrics from Phase 1 continue to be tracked and are expected to improve/evolve.*

| Category                                | Metric/KPI                                                                  | Description/Purpose                                                                                           |
| :-------------------------------------- | :-------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------ |
| **Advanced Ad Tech & Nectar Prep**     | 16. Adoption of Ad SDK v2 & Multi-Format Ads                                  | Uptake of new ad capabilities by Flower (and Beta Nectar) apps.                                         |
|                                         | 17. eCPM uplift from Ad SDK v2 / New Formats                                  | Measure impact of advanced ad tech on revenue efficiency.                                                     |
|                                         | 18. Number of Apps in Nectar tier (Closed Beta)                              | Initial cohort for testing revenue share mechanics and gathering feedback.                                    |
|                                         | 19. Feedback Score from Nectar tier Beta Participants                        | Qualitative measure of readiness for full Nectar tier launch.                                              |
|                                         | *(Monetization KPIs like Platform Gross Ad Revenue (€) & Average Platform eCPM (€) continue to be key)* | Monitor overall platform revenue growth and efficiency.                                                     |

## **Phase 3: Full Ecosystem Launch – Revenue Share & Scale (H2 2026)**

*   **Focus:** Go live with the complete 4-tier platform, including automated 50/50 Net Ad-Revenue sharing for the Nectar tier, and drive adoption.
*   **Key Milestones:** Nectar tier (GA) with 50/50 Net Ad-Revenue Split, Automated Payouts, Public Rev-Share Program Launch.
*   *All relevant metrics from Phase 2 continue to be tracked and are expected to improve/evolve.*

| Category                                | Metric/KPI                                                                      | Description/Purpose                                                                                                  |
| :-------------------------------------- | :------------------------------------------------------------------------------ | :------------------------------------------------------------------------------------------------------------------- |
| **Revenue Sharing & Nectar tier Success** | 20. Number of Apps in Nectar tier (GA)                                           | Adoption of the top revenue-sharing tier by high-performing creators.                                              |
|                                         | 21. Flower → Nectar Upgrade Rate (Monthly)                                  | Rate of progression to the ultimate revenue-sharing tier.                                                          |
|                                         | 22. Total Net Ad Revenue Generated by Nectar Apps (€)                            | The net ad revenue pool (after ad network fees) generated by Nectar apps, from which shares are calculated.       |
|                                         | 23. Total Ad Revenue Share Paid to Creators (€ via Nectar tier)                  | Direct financial success for top partners; validates the revenue-sharing promise.                                    |
|                                         | 24. Pollinations' Net Revenue from Nectar Apps (€) (After payout)              | Viability of the revenue-sharing model for the platform's own financial health from Nectar tier.                  |
| **Overall Platform Health & Profitability**| 25. Platform Gross Margin (%) *(Primary Indicator)*                             | Overall financial health, incorporating all revenue streams, COGS, and creator payouts.                              |
|                                         | 26. Blended LTV of Monetized End-User (to pollinations.ai platform)                | Long-term value generated per monetized end-user, guiding acquisition and retention strategies.                    |
|                                         | 27. Creator Churn Rate (Overall and by tier)                                    | Retention of creators across different stages of their lifecycle on the platform; identifies potential issues.   |
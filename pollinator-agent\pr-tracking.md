# PR Tracking

This document tracks the status of pull requests submitted to external repositories to promote Pollinations.AI.

| Repository | PR Link | Status | Date | Notes |
|------------|---------|--------|------|-------|
| [awesome-generative-ai](https://github.com/steven2358/awesome-generative-ai) | [PR #225](https://github.com/steven2358/awesome-generative-ai/pull/225) | Open | 2025-04-19 | Added Pollinations.AI to Other section (Minimal Change) |
| [chatbot-ui](https://github.com/mckaywrigley/chatbot-ui) | [PR #1930](https://github.com/mckaywrigley/chatbot-ui/pull/1930) | Closed | 2025-04-29 | Temporarily closed for testing. Extensive integration changes. |
| [stable-diffusion-webui](https://github.com/AUTOMATIC1111/stable-diffusion-webui) | [PR #16951](https://github.com/AUTOMATIC1111/stable-diffusion-webui/pull/16951) | Closed | 2025-04-29 | Temporarily closed for testing. Extensive integration changes. |
| [Flowise](https://github.com/FlowiseAI/Flowise) | [PR #4318](https://github.com/FlowiseAI/Flowise/pull/4318) | Closed | 2025-04-29 | Temporarily closed for testing. Extensive integration changes. |
| [awesome-generative-ai-apis](https://github.com/foss42/awesome-generative-ai-apis) | [PR #337](https://github.com/foss42/awesome-generative-ai-apis/pull/337) | Merged | 2025-04-26 | Added Pollinations.AI to the "Image" section |
| [awesome-generative-ai](https://github.com/steven2358/awesome-generative-ai) | [PR #221](https://github.com/steven2358/awesome-generative-ai/pull/221) | Closed | 2025-04-29 | Temporarily closed for revision. Added to multiple sections (non-minimal). |
| [awesome-cyberai4k12](https://github.com/cyberai4k12/awesome-cyberai4k12) | [PR #1](https://github.com/cyberai4k12/awesome-cyberai4k12/pull/1) | Open | 2025-04-11 | Added Pollinations.AI to the "AI Tools for Education" section |
| [public-apis](https://github.com/public-apis/public-apis) | [PR #4233](https://github.com/public-apis/public-apis/pull/4233) | Open | 2025-04-11 | Added Pollinations.AI to the "Machine Learning" section |
| [awesome-chatgpt](https://github.com/awesome-chatgpt/awesome-chatgpt) | [PR #180](https://github.com/awesome-chatgpt/awesome-chatgpt/pull/180) | Open | - | Added Pollinations.AI to the "APIs" section |
| [ai-edu](https://github.com/microsoft/ai-edu) | [PR #828](https://github.com/microsoft/ai-edu/pull/828) | Open | 2025-04-11 | Added Pollinations.AI to the "Tools" section |
| [awesome-machine-learning](https://github.com/josephmisiti/awesome-machine-learning) | [PR #1034](https://github.com/josephmisiti/awesome-machine-learning/pull/1034) | Open | - | Added Pollinations.AI to the "Third-party APIs" section |
| [awesome-python](https://github.com/vinta/awesome-python) | [PR #2680](https://github.com/vinta/awesome-python/pull/2680) | Open | - | Added Pollinations.AI to the "Third-party APIs" section |
| [curriculum (AI & Cybersecurity for Teens)](https://github.com/cyberai4k12/curriculum) | [PR #1](https://github.com/cyberai4k12/curriculum/pull/1) | Open | - | Added Pollinations.AI as a resource for generative AI experiments |
| [AI-Image-Creation-Toolkit](https://github.com/eduhubai/AI-Image-Creation-Toolkit) | [PR #1](https://github.com/eduhubai/AI-Image-Creation-Toolkit/pull/1) | In Progress | 2025-05-15 | Added Pollinations.AI integration for free, no-signup image generation |
| [startup_teens_machine_learning](https://github.com/simpleclub/startup_teens_machine_learning) | [PR #3](https://github.com/simpleclub/startup_teens_machine_learning/pull/3) | Open | - | Added Jupyter notebook for generative AI with Pollinations.AI in German |
| [ML-YouTube-Courses](https://github.com/dair-ai/ML-YouTube-Courses) | [PR #48](https://github.com/dair-ai/ML-YouTube-Courses/pull/48) | Open | - | Added Pollinations.AI as a generative AI resource for students |
| [ai-chatbot-framework](https://github.com/alfredfrancis/ai-chatbot-framework) | [PR #196](https://github.com/alfredfrancis/ai-chatbot-framework/pull/196) | Closed | 2025-04-29 | Temporarily closed for testing. Extensive integration changes. |
| [koishi](https://github.com/koishijs/koishi) | [PR #1498](https://github.com/koishijs/koishi/pull/1498) | Closed | 2025-04-29 | Temporarily closed for testing. Extensive integration changes. |
| [cheshire-cat-ai](https://github.com/cheshire-cat-ai/core) | [Plugin Repository](https://github.com/voodoohop/cheshire-cat-pollinations-plugin) | Published | 2025-04-19 | Created standalone Pollinations.AI plugin for Cheshire Cat AI |
| [awesome-ai-tools](https://github.com/vishalxl/Awesome-Generative-AI-Tools) | [PR #TBD](https://github.com/vishalxl/Awesome-Generative-AI-Tools/pulls) | Planned | 2025-04-19 | Will add to the "Image Generation" and "Text Generation" sections |
| [awesome-ai-learning (Awesome-LLM)](https://github.com/Hannibal046/Awesome-LLM) | [PR #TBD](https://github.com/Hannibal046/Awesome-LLM/pulls) | Open | 2025-04-19 | Added Pollinations.AI to the LLM Applications section |
| [SillyTavern](https://github.com/SillyTavern/SillyTavern) | [PR #TBD](https://github.com/SillyTavern/SillyTavern/pulls) | Submitted | 2025-05-16 | Added Pollinations.AI integration for text and image generation |
| [OpenWebUI](https://github.com/open-webui/open-webui) | [PR #TBD](https://github.com/open-webui/open-webui/pulls) | Submitted | 2025-05-16 | Added Pollinations.AI integration for image generation |
| [awesome-creative-coding](https://github.com/terkelg/awesome-creative-coding) | [PR #213](https://github.com/terkelg/awesome-creative-coding/pull/213) | Open | 2025-04-29 | Added Pollinations.AI to the AI/ML section |
| [public-apis/public-apis](https://github.com/public-apis/public-apis) | [PR #TBD](https://github.com/public-apis/public-apis/pulls) | Planned | 2025-04-30 | Will add to the "Machine Learning" section |
| [filipecalegario/awesome-generative-ai](https://github.com/filipecalegario/awesome-generative-ai) | [PR #TBD](https://github.com/filipecalegario/awesome-generative-ai/pulls) | Planned | 2025-04-30 | Will add to multiple sections (Text, Image Generation, Audio) |
| [touretzkyds/ai4k12](https://github.com/touretzkyds/ai4k12) | [PR #TBD](https://github.com/touretzkyds/ai4k12/pulls) | Ready | 2025-04-30 | Added multilingual (EN/ES) educational resources aligned with "5 Big Ideas in AI" framework |
| [EdenIsHereToStay/AiSchool](https://github.com/EdenIsHereToStay/AiSchool) | [PR #TBD](https://github.com/EdenIsHereToStay/AiSchool/pulls) | Planned | 2025-04-30 | Will add to resources section |
| [zukixa/cool-ai-stuff](https://github.com/zukixa/cool-ai-stuff) | [PR #TBD](https://github.com/zukixa/cool-ai-stuff/pulls) | Planned | 2025-04-30 | Will add to the main list |
| [apibird/public-apis](https://github.com/apibird/public-apis) | [PR #TBD](https://github.com/apibird/public-apis/pulls) | Planned | 2025-04-30 | Will add to the "Machine Learning" section |
| [eduStack/ai4kid](https://github.com/eduStack/ai4kid) | [PR #1](https://github.com/eduStack/ai4kid/pull/1) | Open | 2025-04-30 | Added Pollinations.AI to the "生成式人工智能" section with bilingual guide and demo |
| [natnew/Awesome-Generative-AI](https://github.com/natnew/Awesome-Generative-AI) | [PR #TBD](https://github.com/natnew/Awesome-Generative-AI/pulls) | Planned | 2025-04-30 | Will add to the "APIs and Frameworks" section |
| [meetpateltech/AI-Infinity](https://github.com/meetpateltech/AI-Infinity) | [PR #TBD](https://github.com/meetpateltech/AI-Infinity/pulls) | Planned | 2025-04-30 | Will add to multiple categories |

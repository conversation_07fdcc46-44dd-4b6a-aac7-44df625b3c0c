May 14, 2025

## Pollinations.AI \- <PERSON>

Invited [<PERSON>](mailto:el<PERSON><PERSON>@pollinations.ai) [<PERSON>](mailto:<EMAIL>) [<PERSON>](mailto:<EMAIL>)

Attachments [Pollinations.AI - <PERSON>](https://www.google.com/calendar/event?eid=******************************************************************) 

Meeting records [Transcript](?tab=t.vlo0692uu9pd) 

### Summary

<PERSON> from Pollinations presented their AI platform aimed at empowering content creation and monetization for indie app developers by connecting them with advertisers. Pollinations has a growing community with over 300 apps and demonstrated their monetization strategy through ad integration examples and a pilot with a Roblox game. <PERSON> provided feedback on the presentation, suggesting clearer articulation of their AI's value proposition, the ad connection, team details, and market size.

### Details

* **Pollination's AI and Goals** <PERSON> introduced Polynation's AI, stating its purpose is to empower creation and unlock art revenue. Their mission is to enable easy app creation and provide creators with a straightforward way to monetize their applications, with zero upfront costs and a path to shared success. <PERSON> showed a real-time recording of their API requests to illustrate their server sending text and image responses ([00:01:19](?tab=t.vlo0692uu9pd#heading=h.eg2wn8ttwxfg)).

* **Addressing Indie App Creator Challenges** Thomas Haferlach explained that indie app creators face struggles with AI costs and monetization, while advertisers miss AI-native audiences. Pollinations aims to bridge this gap by connecting advertisers and creators. Thomas Haferlach mentioned that creators can build apps on Pollinations, and they have a growing community and an app store with over 300 apps ([00:01:19](?tab=t.vlo0692uu9pd#heading=h.eg2wn8ttwxfg)).

* **Monetization Strategy and Examples** Initially, ad revenue will cover compute costs, but the long-term plan involves offering creators a revenue share. Thomas Haferlach presented examples of web apps built on Pollinations, ranging from AI girlfriends to food analysis apps. They highlighted a partnership with Hydra LLM for a Reddit-like experience using Pollinations for image generation. Thomas Haferlach showcased an app called Pixpal, which ranks high on Bing for "AI chat with images," demonstrating the talent within their community ([00:02:38](?tab=t.vlo0692uu9pd#heading=h.8xgcnmbfucr2)).

* **Illustrative Ad Integration** Thomas Haferlach provided an example of Pixpal where a user asks for a holiday destination visualization, receiving an image and then language learning suggestions with a contextual ad for a language learning app. The ad is tailored to the conversation, in this case, about Greece ([00:04:08](?tab=t.vlo0692uu9pd#heading=h.94eukxrtf2go)). Clicks on these ads can lead to revenue sharing with the app creator. Thomas Haferlach mentioned another pilot with a Roblox game created by an 18-year-old Ukrainian, generating $150 per day through Roblox's platform ([00:05:15](?tab=t.vlo0692uu9pd#heading=h.bout1d3nuy5n)).

* **Traction and Market Opportunity** Pollinations has over three million monthly active users and generates 100 million pieces of media per month, representing potential ad placements ([00:05:15](?tab=t.vlo0692uu9pd#heading=h.bout1d3nuy5n)). A small ad pilot showed impressive click-through rates. Thomas Haferlach stated the market is substantial, estimated at $280 billion, with $20 billion in indie inventory via SDKs. They aim for a $27 million annual return on revenue, with supporting documentation available ([00:06:18](?tab=t.vlo0692uu9pd#heading=h.777ydkmzol3i)).

* **Open Sales Funnel and Tiered System** Thomas Haferlach described an open sales funnel where users can join Pollinations without initial sign-up. As their app gains traction and requires higher rate limits, they can progress to higher tiers, qualifying for the ad program and better models. The final stage involves the revenue share system, currently being piloted with Roblox. Thomas Haferlach highlighted their experienced team and plans to hire for backend, ML ops, and growth/marketing roles, as well as someone to focus on gaming integrations ([00:07:36](?tab=t.vlo0692uu9pd#heading=h.sk2vvgnse9cu)).

* **Monetization Focus and Future Plans** Thomas Haferlach emphasized that the immediate priority is monetizing their existing user base. Future plans include adding richer ad experiences. Their goal is to transition their three million anonymous users into a stage where they generate ad revenue and to automate the revenue share system within 12 months ([00:09:12](?tab=t.vlo0692uu9pd#heading=h.y66fg6f5dnap)).

* **Feedback on Presentation Style and Content** Philipp Simon provided feedback, suggesting that the initial API request display was too long and might not have conveyed the intended purpose ([00:10:46](?tab=t.vlo0692uu9pd#heading=h.lyq6rw3ue0ab)) ([00:18:39](?tab=t.vlo0692uu9pd#heading=h.lo6ua0l4g3c0)). Philipp Simon stressed the importance of clearly defining what Pollinations AI is, its unique selling proposition (USP), and whether the core business is the AI or connecting to the ad space ([00:11:35](?tab=t.vlo0692uu9pd#heading=h.ptq8pv2h4xid)). Philipp Simon advised against showing the ad example early on, as it didn't look appealing from an ad perspective ([00:13:55](?tab=t.vlo0692uu9pd#heading=h.roy29vx6k188)).

* **Clarifying the Value Proposition** Philipp Simon urged Thomas Haferlach to create a slide illustrating the ecosystem: content creation, user engagement, and the AI matching relevant advertisements ([00:14:55](?tab=t.vlo0692uu9pd#heading=h.8gwmaz1qw632)). Philipp Simon suggested focusing on the AI for content and games as the core USP, with monetization through ads as a secondary opportunity. Philipp Simon noted that the current presentation seemed unclear on this point ([00:12:45](?tab=t.vlo0692uu9pd#heading=h.yjz46djym71z)) ([00:23:34](?tab=t.vlo0692uu9pd#heading=h.wx9dkpd8qgqs)).

* **Team Slide Revision** Philipp Simon critiqued the team slide, finding the descriptions of roles (vision/strategy vs. execution) uninformative and suggesting the inclusion of relevant backgrounds and expertise instead ([00:15:50](?tab=t.vlo0692uu9pd#heading=h.2ywqxs9s4uy9)). Philipp Simon recommended briefly mentioning past experiences and current hiring needs ([00:17:08](?tab=t.vlo0692uu9pd#heading=h.md93zjpqgyxf)).

* **Refining the Business Model Explanation** Philipp Simon advised Thomas Haferlach to elaborate on how Pollinations works, its key components, and the flow of content creation to monetization ([00:19:27](?tab=t.vlo0692uu9pd#heading=h.t1k5f11vzhef)). Philipp Simon suggested a visual graph to illustrate this ([00:20:17](?tab=t.vlo0692uu9pd#heading=h.8h5vw34vlqw2)). On the example of Pixpal, Philipp Simon reiterated that it felt too long and should be preceded by an explanation of the ad connection ([00:20:54](?tab=t.vlo0692uu9pd#heading=h.queniuhfc6h4)).

* **Roblox Pilot Details** Regarding the Roblox pilot's $150 per day revenue, Philipp Simon expressed interest in understanding how this was achieved and whether it was directly related to Pollinations' ad business ([00:21:42](?tab=t.vlo0692uu9pd#heading=h.o7pkmhppf4j0)). Thomas Haferlach clarified that in the Roblox case, monetization occurs through Roblox, not their ad platform, but it demonstrates the potential of their AI models ([00:22:30](?tab=t.vlo0692uu9pd#heading=h.cnyfpjwelpf5)).

* **Market Size Discussion** Philipp Simon commented that the initially stated market size of $27 million seemed low ([00:24:17](?tab=t.vlo0692uu9pd#heading=h.7i9swodvo35e)). Thomas Haferlach clarified that this was a projection of their potential cut of a larger sum. Philipp Simon suggested reframing the market size to at least $100 million to represent the potential beachhead market, not their immediate revenue target ([00:25:14](?tab=t.vlo0692uu9pd#heading=h.j2izba8q703w)).

* **Follow-up Meeting** Due to time constraints, Philipp Simon concluded the meeting and requested an updated presentation, offering to schedule a follow-up call for 10 minutes the next day ([00:26:02](?tab=t.vlo0692uu9pd#heading=h.94rum57o16v1)). They agreed to a 12:30 PM meeting .

### Suggested next steps

- [ ] Thomas Haferlach will create a slide explaining Pollinations AI ecosystem, showing content creation, user engagement, and AI-driven ad matching.  
- [ ] Thomas Haferlach and Elliot will revise the team slide with 2-3 bullet points of relevant backgrounds and verbally mention hiring needs.  
- [ ] Thomas Haferlach will shorten the API request slide and clarify its purpose for brevity.  
- [ ] Thomas Haferlach will remove the specific AI chat with images example and integrate the AI-driven ad selection concept into the ecosystem overview slide.  
- [ ] Thomas Haferlach will revise the market slide to show a TAM or beachhead market of at least $100 million, clarifying it's market size, not projected revenue.  
- [ ] Thomas Haferlach will send an updated presentation to Philipp Simon.

*You should review Gemini's notes to make sure they're accurate. [Get tips and learn how Gemini takes notes](https://support.google.com/meet/answer/14754931)*

*Please provide feedback about using Gemini to take notes in a [short survey.](https://google.qualtrics.com/jfe/form/SV_9vK3UZEaIQKKE7A?confid=nnxUnM5M2kPdKPDXRha_DxIWOA8MCwMyBwiKAiAAGAMI)*

May 14, 2025

## Pollinations.AI \- Philipp Simon \- Transcript

### 00:00:00

   
**Philipp Simon:** Y  
**Thomas Haferlach:** yeah okay I will just share the correct window um this one. Okay. Um so um do you are you are you seeing  
**Philipp Simon:** I  
**Thomas Haferlach:** the  
**Philipp Simon:** see it.  
**Thomas Haferlach:** the shared screen?  
**Philipp Simon:** Yep.  
**Thomas Haferlach:** Okay. Uh so great to great to reconnect. Um Polynation's AI empowers creation and unlocks art revenue. Our goal is to empower creation and unlock art revenue. um mission. Our mission is to allow easy uh app um creation and uh give our creators an easy way to um monetize once they've um once they've created their their application. Um uh we provide we we give zero we ask for zero upfront cost and we um put creators on a path uh towards shared success. Um what you see here on the on the slide is a a real time um recording of our API requests.  
   
 

### 00:01:19

   
**Thomas Haferlach:** So this is our server which is um sending both text and image responses to our users. And I get this kind of I I I really love watching this. It kind of calms me down. So I thought it's a good um it's a good thing to start the pitch with. Um but what this powers we will see later. So um the current problem is that um um app indie app creators um struggle with AI costs and monet monetization advertisers miss AAI native audiences. Our platform addresses this. Um this our solution uh polinations bridges bridges uh makes the bridge between um advertisers and creators and um uh so initially um we onboard our so so on so creators are can build their apps on polinations I'm going to show in the next slide a little bit a few examples of what types of apps they can create create but we have a we have a a vibrant community of uh indie um indie creators, developers and are building an app store which already has more than um um 300 300 apps and growing each day.  
   
 

### 00:02:38

   
**Thomas Haferlach:** Um so um initially um ads will cover our compute but in the long run we want to offer creators a revenue share so we can both um profit from apps that are going viral. Um here you see just some screenshots of different um web apps made uh on top of pollinations. You have everything from AI girlfriends to um um to uh food um an analysis. So there's an app you can take photos of food and it analyze what ingredients are inside. Um we've already partnered also with some uh with with people from our cohort. So, Hydra LLM is already integrating with Pollinations. It allows a Reddit like experience and uses Pollinations for image generation. Um, there's a wide breath. I'm going to just show you now um an an example. Um, so here, and I'm quite proud of this. Um, we have one member in our community. He um he is very good at SEO and he built an app um on polinations that is now if you search on on Bing for something like AI chat with images um it is the first it is the first result and I don't know how he did it because he's competing with very big companies but he but we have a lot of talent in our community um so if you see here Pixpal this app is built  
   
 

### 00:04:08

   
**Thomas Haferlach:** on polin it's also possible to be free because we allow him them to use pollinations for free. Of course, free it doesn't mean free that we are we are of course monetizing attention in this case. So here you see an example. Hi Pixel, can you visualize my next holiday destination? This is using both our text and image models. Um so now it gives me a it gives me a a nice place on holiday. And now I will jump there a little bit to the front. I will write that looks great. Can I learn the langu? How can I learn uh their language? And then you will get uh it will respond um uh giving some details and it will add a context. It it adds a contextual ad into the conversation which um brings users to a language learning app. And this ad is not just selected based on context. All it's also modified to fit with the conversation. So because the AI knows the we are talking about Greece, it will it will already customize the the ad message.  
   
 

### 00:05:15

   
**Thomas Haferlach:** Um and then you see here if someone clicks on the on the um on the link they can sign up to TalkPal which is one of our part advertising partners and once they make money we will we will share the revenue with the um creator of this app. Okay, I realize I'm taking quite a long time on on this. Maybe I  
**Philipp Simon:** Yeah.  
**Thomas Haferlach:** need to figure out how  
**Philipp Simon:** Yeah.  
**Thomas Haferlach:** to how to simplify it. Um uh I will go through this one a little bit faster. We have another pilot up which we've already going we already have a revenue share agreement. We already we already received um uh $2,000 in revenue from them. We are just starting there. We are making $150 um per day and sharing this with the 18-year-old Ukrainian creator who created this um Roblox game. In this case, we're monetizing through Roblox's platform. Uh our flywheel is already in motion. We have more than three million monthly active users, 100 of 100 million media generated per month.  
   
 

### 00:06:18

   
**Thomas Haferlach:** All of those are potential places to place ads. Um, we have done so far a small ad pilot with about 60k impressions showing uh quite impressive clickthrough rates. We can get into those also in the Q\&A if you want to see more more numbers. Um, our our ad our store the number of apps is growing uh every day. Um, the market um we see is huge. It's a 200 uh 280 billion market with uh uh 20 billion um um in for the sum in indie inventory via SDKs. We've currently determined um so it's like apps in which which use SDKs um through which we can um um embed ads into the app. Okay, I really need to practice this slide. Um um this is Elliot's um expertise, but I need to be able to present as well. Uh we just actually we just changed the market. Um um but um we come up with a a 27 27 million annual return on revenue um for our act. We also have some supporting documentation uh that shows this better.  
   
 

### 00:07:36

   
**Thomas Haferlach:** Um okay we uh um we allow people to create for free. We have a very open let's say sales funnel. You can you can um join pollinations without um even signing up. So we have all this traction going on and you can then progress if your app uh has a certain number of users you need higher rate limits. you can progress to a higher tier where you then qualify for our um ad ad uh program and better models. And yeah, the last stage is where we actually do the revenue share which we're kind of piloting right now with Roblox. Um competition I'm going to skip for now. Um and yeah, we are we are an experienced team. I have I have more than eight years of full-time work with generative AI. I've been I'm an I'm a I'm an a I'm a veteran in AI. I worked 2021 I I was working at Amazon as an AI engineer. Um I've worked at it on micros systemystems and I'm I'm I'm b I know in most I I am I'm I'm usually aware of the things that that will happen in advance like with pollinations we've always been ahead of the curve um um I could go into details there is responsible for strat for for strategy finance and operations and we plan to hire um an a back and a senior back end ML ops engineer and um  
   
 

### 00:09:12

   
**Thomas Haferlach:** uh growth and marketing specialist to help us um build out the ad the ad side of our of of our company. Um because of our big um uh attraction in in Roblox, we are we are uh we have a very interesting um um person to join our team who could build out the whole um games side of Polynations offering um integrations with Roblox, Fortnite, Twitch, it.io. So these are these would be the next hires. And there we are. We we want to we have a a system. is working. We we want to now the the first priority is to monetize it. Um we will of course um add many um different uh many richer ad experiences in the in the future. Um um we will we sorry I should stick to the speaker notes a bit. No well I don't know it but let's go on. So now we want to grow seedups to flowers. So we want to bring as many of possible as of our three anonymous users um into the stage where we where we earning through ads from them and um uh we want to start embedding rich ads in our clients uh in our clients apps and um in 12 months we want to share the with the revenue share system that right now we're piloting um let's say manually.  
   
 

### 00:10:46

   
**Thomas Haferlach:** So we want  
**Philipp Simon:** All  
**Thomas Haferlach:** to have a an automated system in place in 12 months or build it um so that um yeah the revenue share uh basically works by itself. Let's revolutionize AI app monetization together.  
**Philipp Simon:** right. Okay. Thanks a lot. Um, because we have just 60 minutes, let's  
**Thomas Haferlach:** Yeah. Sorry I took too long also.  
**Philipp Simon:** of course you  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** know yourself, you need to practice a little bit  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** that  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** it's more fluent. Let's let's already start with the biggest pit forth in my point which is definitely skip that example like that. Um I mean you show now the product which I love and which I told you  
**Thomas Haferlach:** Mhm.  
**Philipp Simon:** but now it was too much um and I would skip that example if you go back like where you go where you show them the ad and so on that was like  
**Thomas Haferlach:** Mhm.  
**Philipp Simon:** too long and did  
**Thomas Haferlach:** Yeah.  
   
 

### 00:11:35

   
**Philipp Simon:** not  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** really feeling  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** it didn't really give a feeling that I I feel like okay that's cool. um was more like okay what's that so if  
**Thomas Haferlach:** Mhm.  
**Philipp Simon:** you want to show or what I'm missing the most in general is what exactly is polinations AI so if you want to ex I mean I told you already last time that I've question marks with that whole is it everything about ads or is your core asset the AI like what is pollinations and you have um you you have one graph where you say like um you connect creators with the ad space. So  
**Thomas Haferlach:** Mhm.  
**Philipp Simon:** if if I hear that then I would say like okay so you have a technology that is basically connecting the ad space or the advertising advertisers to the games. But then on the other hand you tell me oh you're creating the games or you have an AI for the game. So um I think we the most important slide now because product show it um  
   
 

### 00:12:45

   
**Thomas Haferlach:** Okay.  
**Philipp Simon:** and we go to oneonone in a second um but just want to focus first on that point. So if you make one slide, this is pollination. This is what we do.  
**Thomas Haferlach:** H  
**Philipp Simon:** Not showing only with great examples already of the games because I get this point, but it seems like how you position yourself as a connector between between ads. And then this is one I I I still don't buy or I don't see like what's what's special then about polinations? Um because because  
**Thomas Haferlach:** What? Yeah.  
**Philipp Simon:** I mean don't don't get me wrong like how I see it still is you are having an AI where people can create great games and now you also want to see how you can monetize that for uh for for your customers. Um, but in the end, isn't your USP like creating AI and games and and and and fooling that community and uh making the making the brand of Polynations AI that people love your product  
**Thomas Haferlach:** What?  
**Philipp Simon:** and then of course they can make revenues because then you can attract the advertisers.  
   
 

### 00:13:55

   
**Philipp Simon:** But for me it feels like this is not clear enough what your USP is like what are you really focusing on because it's I don't see anything about ads and please don't show that example with one of these ads at the top it looks not nice right so I'm coming from the ad space so this is something I probably not show I would rather show uh just in general how how your business model looks like how you make revenues like not with an example but just with like here's pollinations we take care of we have the AI to to get content to get uh to get um um um um um page impressions you know and and and so on and and then we also take care of the advertising side but you you I mean it's not a tech or it's not an AI that takes care of the advertising side the advertising side is just a pure advertising business right so you go to an ad network and try to plug in your content you go or you even take care of the advertisers yourselves.  
   
 

### 00:14:55

   
**Philipp Simon:** This is nothing special. Yeah.  
**Thomas Haferlach:** Well, I mean I mean we have the we have the context. So so we we are using an AI model um which selects the ad based on the on the conversation, right? based on the on the content of the of the conversation and  
**Philipp Simon:** Yeah.  
**Thomas Haferlach:** also also personalizes the ad  
**Philipp Simon:** You're  
**Thomas Haferlach:** message  
**Philipp Simon:** right. You're  
**Thomas Haferlach:** based  
**Philipp Simon:** right.  
**Thomas Haferlach:** on  
**Philipp Simon:** I  
**Thomas Haferlach:** the  
**Philipp Simon:** did  
**Thomas Haferlach:** conversation.  
**Philipp Simon:** not see that but but I did did not get this point too much. So then good  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** point. do one slide more like where where you show this whole you ecosystem you have  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** creating the games people use it already then you have that AI that matches the best advertisement for the content  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** show me one slide where this flow this easy flow  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** is matched yeah um that's really important then second thing which I really don't like  
   
 

### 00:15:50

   
**Thomas Haferlach:** Mhm.  
**Philipp Simon:** is your team slide again like can you go go to that for for a second?  
**Thomas Haferlach:** Yeah. Yeah. Sure.  
**Philipp Simon:** Um  
**Thomas Haferlach:** Uh, give me one second. Yeah.  
**Philipp Simon:** I I mean one sets the vision and strategy and the other one executes the strategy. Let me a little bit mean but I would say like okay what's this?  
**Thomas Haferlach:** Mhm. Mhm. Mhm. Mhm.  
**Philipp Simon:** I I I I want to know do you have a a a background a technical background an advertising background uh whatever background but one is I hope you both set the vision and work on the strategy and hope you both execute the strategy and you don't need you don't need anybody for finance right no at a startup nobody should do finance in in  
**Thomas Haferlach:** Mhm.  
**Philipp Simon:** that  
**Thomas Haferlach:** Mhm.  
**Philipp Simon:** so  
**Thomas Haferlach:** Mhm.  
**Philipp Simon:** how I would do it do two or three bullets what you did before that people get an idea where you're coming from from which sector and then you talk maybe a little bit about it verbally you just say look hey I'm I'm Thomas um um my my background is Amazon and I don't have everything in mind you know I'm more like the the business guy or whatever or more  
   
 

### 00:17:08

   
**Thomas Haferlach:** Mhm.  
**Philipp Simon:** the market guy and this is Elliot blah blah blah that's it and we know we're um um we're very strong on that but but we have weaknesses in uh we need a marketing specialist. So that's why we're still looking for somebody. We know that that's it. Yeah. But  
**Thomas Haferlach:** Yeah. Yeah. Yeah. Yeah.  
**Philipp Simon:** setting visions and strategy and the other one execute strategy and finance,  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** you know.  
**Thomas Haferlach:** Yeah. Yeah. Yeah. Yeah. But thank you. I mean, this is, you know, we we're um it's so helpful, you know. Of course, we were we were aware that this slide we kind of like did it was not, let's say, quite completely finished, but getting this like  
**Philipp Simon:** Yeah.  
**Thomas Haferlach:** straight to the point feedback is is super  
**Philipp Simon:** Yeah.  
**Thomas Haferlach:** good. Um  
**Philipp Simon:** So you just do your  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** two maybe do your two pictures or just say your names and then again like  
   
 

### 00:17:57

   
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** the two or three main topics what you studied or where you worked at in which position  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** and and then talk a little bit about look uh but  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** if if  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** somebody tells me I'm the founder I take care of a strategy then I would say like well okay I know I mean you should both do that  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** but but let's because you have not much time so so  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** change that in that action. Let's let's go to the beginning, please. Let's go to  
**Thomas Haferlach:** Mhm.  
**Philipp Simon:** the beginning.  
**Thomas Haferlach:** Mhm. Mhm.  
**Philipp Simon:** I really have super stop. If not, maybe we have 10 minutes tomorrow or something like this.  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** So, a great slide. Too long on this slide. And  
**Thomas Haferlach:** Uh-huh.  
**Philipp Simon:** and I think maybe it was it for me it was totally funny and I liked it, but probably it was not the purpose of being funny.  
   
 

### 00:18:39

   
**Philipp Simon:** You said that this slide calms you down. If you look at that slide and you tell me this calms somebody down, I would say he's  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** crazy.  
**Thomas Haferlach:** I mean that was actually that was actually  
**Philipp Simon:** He's on drugs at this that bling bling bling is calming somebody down. I would say he's on drugs anyways.  
**Thomas Haferlach:** Yeah. Yeah. Yeah. But that that was kind of supposed to be the joke, but maybe  
**Philipp Simon:** Great  
**Thomas Haferlach:** it's not  
**Philipp Simon:** then  
**Thomas Haferlach:** clear.  
**Philipp Simon:** then great then then I like this but but but you spent already too much time on this right  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** so  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** next slide. I think you spent uh not even two seconds on this slide. Um uh so here you you here you spend more on that challenge rather than on the first slide. Um  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** okay  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** but  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** let's let's keep that for now and then you need to exactly the next one is the most important the fix a product  
   
 

### 00:19:27

   
**Thomas Haferlach:** Mhm.  
**Philipp Simon:** go next one the challenge okay blah blah the fix and this I again I told you I I did not fully get so  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** like a flow like look you want to uh our solution is we do our we we create the content  
**Thomas Haferlach:** Mhm.  
**Philipp Simon:** we we recreate the um the page impressions like the supply and then we have a great AI that matches it and then we need to connect to the advertisers. This is your  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** business. Then there's a share between it. Easy flow of like how uh how your model how your product will work. Yeah.  
**Thomas Haferlach:** Yeah. Yeah.  
**Philipp Simon:** And  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** then you can switch already over to this is by the way for the first pillar the pillar of creating content the pillar of uh having supply. It's fully  
**Thomas Haferlach:** Mhm.  
**Philipp Simon:** on track and right and this is not the pitfall everybody believes to yet you will get more like grilled on the other ones.  
   
 

### 00:20:17

   
**Philipp Simon:** Right?  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** But  
**Thomas Haferlach:** Yeah. Yeah.  
**Philipp Simon:** go on that slide a little bit more easy. How does it work? What are you really doing? What are your component? What's your product? Yeah, this is missing a nice visual graph with a few words showing these are the components. Okay, next one.  
**Thomas Haferlach:** Yeah,  
**Philipp Simon:** Okay,  
**Thomas Haferlach:** it's just  
**Philipp Simon:** great.  
**Thomas Haferlach:** this  
**Philipp Simon:** You  
**Thomas Haferlach:** is the  
**Philipp Simon:** can  
**Thomas Haferlach:** one.  
**Philipp Simon:** fine you you can do some examples here like look now we go to the supply side, right? So what what this is supply side this is how our product looks. This is how we create content. This is how we create supply. Good. Then the  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** next the next one you  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** can  
**Thomas Haferlach:** I mean is it is it clear that this is not things we are creating this is things our our  
**Philipp Simon:** Yeah.  
   
 

### 00:20:54

   
**Thomas Haferlach:** community is building  
**Philipp Simon:** And you  
**Thomas Haferlach:** right?  
**Philipp Simon:** can say that  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** right here.  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** Look this our example um with our with our um  
**Thomas Haferlach:** Yeah. Yeah. Yeah. Okay.  
**Philipp Simon:** Yeah. Whatever.  
**Thomas Haferlach:** This was  
**Philipp Simon:** But  
**Thomas Haferlach:** the  
**Philipp Simon:** this  
**Thomas Haferlach:** example where you where you said uh you didn't like it.  
**Philipp Simon:** don't do that.  
**Thomas Haferlach:** Uhhuh.  
**Philipp Simon:** explain that in the in the slide before  
**Thomas Haferlach:** Yeah. Yeah.  
**Philipp Simon:** you explain look we want to connect it advertise and then they of course you you will with your AI you can get basically these advertisements and uh  
**Thomas Haferlach:** Uhhuh. Uhhuh.  
**Philipp Simon:** and then with CPI because your AI is great and the conversion rates are great and so on. So the  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** magic is with with you know with in that in the middle but which I don't know if you have proved it already maybe with a Roblox case you can show it you can you can um spend a bit more time on how they generate revenues how that works with what ads and and  
   
 

### 00:21:42

   
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** how the colonations AI work here. Yeah but  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** this  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** was  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** definitely too much and it's not nice it does not look nice so skip it.  
**Thomas Haferlach:** Mhm. Mhm. Mhm. Okay.  
**Philipp Simon:** Okay then next one. Okay. Now, exactly. Now, now, now we talk about, okay, let's uh even look at more like full example um what we already achieved like we created the content or like we offered them to create the content and and then they did that game and or 2 million active loss and we even made it to 150 a day. I would be super interested. How do you do that? Tell me more about the 150\. So  
**Thomas Haferlach:** Mhm.  
**Philipp Simon:** maybe you don't have to need have to have it in the pitch deck, but will your questions and you need to have a good answer or even a slide saying how do they get these €15 a day or  
   
 

### 00:22:30

   
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** a day?  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** What's what's what did you do for this? Yeah.  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** Very  
**Thomas Haferlach:** Is  
**Philipp Simon:** easy.  
**Thomas Haferlach:** it okay that it's not related to to ads in that case like But just for the for the for the Yeah. Yeah.  
**Philipp Simon:** Okay. So  
**Thomas Haferlach:** I  
**Philipp Simon:** that's not that's not from your business in the end like not from the that business.  
**Thomas Haferlach:** No, but I I think and I think we we're giving a bit of a kind of um um like an impression impression um which is which maybe is is is wrong. Ads are really um the monetization but are the the way we allow our users to monetize, right? But our core are not the ads. We could also if we if we realize that ads are not the not the best best way to to to monetize pollination we could also offer offer um pay pay as you go or subscription is basically like the the provider. So in this case they are using all of pollinations which is like our text our AI models and so on but the monetization is done on roadblocks.  
   
 

### 00:23:34

   
**Thomas Haferlach:** So in this case we are not providing  
**Philipp Simon:** Okay.  
**Thomas Haferlach:** ads right we are we are yeah we  
**Philipp Simon:** Yeah,  
**Thomas Haferlach:** are getting  
**Philipp Simon:** just coming  
**Thomas Haferlach:** paid  
**Philipp Simon:** back to  
**Thomas Haferlach:** by  
**Philipp Simon:** my  
**Thomas Haferlach:** robots  
**Philipp Simon:** main point that maybe you then you should not refer too much to that ad space in general. So that your core is more like creating content and then on  
**Thomas Haferlach:** yeah  
**Philipp Simon:** top of it you try to monetize it but the core is creating um a a great AI tool for for game developers. Um  
**Thomas Haferlach:** I mean that's what it is right we're um  
**Philipp Simon:** it's not  
**Thomas Haferlach:** and  
**Philipp Simon:** clear enough in the  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** present. let's be honest  
**Thomas Haferlach:** Yeah. Yeah. Yeah. Yeah.  
**Philipp Simon:** or because you want to connect it with advertisers but that's but that's but if you're not even sure about it then I'm not sure if what I I would frame the whole pitch more into as we discussed last time already  
   
 

### 00:24:17

   
**Thomas Haferlach:** Yeah. Yeah.  
**Philipp Simon:** we we are the ones that uh that have a great AI to create games that can generate ad revenues and besides we also want to give them the opportunity to monetize it with ad networks and so on but your core is this because it's yeah it's not it's  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** not clear yeah  
**Thomas Haferlach:** Yeah. Yeah. Yeah.  
**Philipp Simon:** we  
**Thomas Haferlach:** I  
**Philipp Simon:** have good question  
**Thomas Haferlach:** understood  
**Philipp Simon:** so  
**Thomas Haferlach:** that.  
**Philipp Simon:** much it's  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** not clear  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** um  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** I I have two minutes uh for today can we can can you just go ahead so definitely keep that okay um also that fly or in motion to to see that you all great attraction is great great so that's I mean that's your asset right let's uh you know it so it's good that you Show it. Next one. Um yeah, the market the 20 27 million. Um um that's that's low.  
   
 

### 00:25:14

   
**Thomas Haferlach:** on 28 it goes to 120 on the predictions that we have like um I mean that's the sum so it's like really like the part that we project to to to to get but this is not the projection right uh the 27 I mean that that's what is  
**Philipp Simon:** The  
**Thomas Haferlach:** the  
**Philipp Simon:** 27 should be should should be the uh uh the sum, right?  
**Thomas Haferlach:** Yeah. Exactly. So So it's the part that we cut  
**Philipp Simon:** Yeah.  
**Thomas Haferlach:** we  
**Philipp Simon:** But that's too little. 20 20 27 million is too little. It's not that you uh that you aim for that the market where you think that's basically the share you can get and I think 27 is a little bit make it at least 100 million. I'm sure you're you're able to do you find 100 million.  
**Thomas Haferlach:** Is that okay if you push it to 28 or we just Yeah, I don't know. But because now it's 27\.  
**Philipp Simon:** No, it's not your revenues. The sum is not your revenues.  
   
 

### 00:26:02

   
**Thomas Haferlach:** Yeah. Yeah. Because we're talking about revenue there. I don't think that  
**Philipp Simon:** Yeah.  
**Thomas Haferlach:** makes any  
**Philipp Simon:** No,  
**Thomas Haferlach:** sense  
**Philipp Simon:** it's  
**Thomas Haferlach:** there.  
**Philipp Simon:** not  
**Thomas Haferlach:** Right.  
**Philipp Simon:** revenues.  
**Thomas Haferlach:** Yeah. Yeah.  
**Philipp Simon:** It's  
**Thomas Haferlach:** Yeah.  
**Philipp Simon:** the mark. It's the first beach head market where you think that's a potential. It's not your revenues that you will achieve it by some year.  
**Thomas Haferlach:** Okay.  
**Philipp Simon:** Guys,  
**Thomas Haferlach:** Okay.  
**Philipp Simon:** I really got to go now. Unfortunately,  
**Thomas Haferlach:** Okay.  
**Philipp Simon:** I  
**Thomas Haferlach:** But  
**Philipp Simon:** offer  
**Thomas Haferlach:** thank  
**Philipp Simon:** you  
**Thomas Haferlach:** you so much.  
**Philipp Simon:** send me an updated one. Maybe can spend more 10 minutes. Um if if you want um maybe at at 9:00 am in the morning.  
**Thomas Haferlach:** Let's do it. Let's do it. Yeah.  
**Philipp Simon:** Um let's do 9:30.  
**Thomas Haferlach:** Okay.  
**Philipp Simon:** Oh, wait one second.  
**Thomas Haferlach:** When tomorrow  
**Philipp Simon:** I  
**Thomas Haferlach:** morning, then  
**Philipp Simon:** was at sorry at 12:30 12:30.  
**Thomas Haferlach:** That works. Well, let me just check but I think so.  
**Philipp Simon:** Go now.  
**Thomas Haferlach:** Okay. Bye-bye, thank you so much. I'm  
**Philipp Simon:** When?  
**Thomas Haferlach:** making me. Thank you.  
   
 

### Transcription ended after 00:26:56

*This editable transcript was computer generated and might contain errors. People can also change the text after it was created.*